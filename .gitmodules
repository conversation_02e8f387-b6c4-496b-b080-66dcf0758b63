[submodule "contracts/lib/openzeppelin-contracts"]
	path = contracts/lib/openzeppelin-contracts
	url = https://github.com/openzeppelin/openzeppelin-contracts
[submodule "external/geth"]
	path = external/geth
	url = **************:primev/mev-commit-geth.git
[submodule "contracts/lib/openzeppelin-contracts-upgradeable"]
	path = contracts/lib/openzeppelin-contracts-upgradeable
	url = https://github.com/OpenZeppelin/openzeppelin-contracts-upgradeable
[submodule "contracts/lib/openzeppelin-foundry-upgrades"]
	path = contracts/lib/openzeppelin-foundry-upgrades
	url = https://github.com/OpenZeppelin/openzeppelin-foundry-upgrades
[submodule "contracts/lib/eigenlayer-contracts"]
	path = contracts/lib/eigenlayer-contracts
	url = https://github.com/Layr-Labs/eigenlayer-contracts.git
[submodule "contracts/lib/core"]
	path = contracts/lib/core
	url = https://github.com/symbioticfi/core
[submodule "contracts/lib/openzeppelin-upgrades"]
	path = contracts/lib/openzeppelin-upgrades
	url = https://github.com/OpenZeppelin/openzeppelin-upgrades.git
[submodule "contracts/lib/burners"]
	path = contracts/lib/burners
	url = https://github.com/symbioticfi/burners
[submodule "contracts/lib/forge-std"]
	path = contracts/lib/forge-std
	url = https://github.com/foundry-rs/forge-std

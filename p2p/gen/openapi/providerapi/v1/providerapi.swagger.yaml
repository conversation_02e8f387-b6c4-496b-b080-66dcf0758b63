swagger: "2.0"
info:
  title: Provider API
  version: 1.0.0-alpha
  license:
    name: Business Source License 1.1
    url: https://github.com/primev/mev-commit/blob/main/LICENSE
consumes:
  - application/json
produces:
  - application/json
paths:
  /v1/provider/get_commitment_info:
    get:
      summary: GetCommitmentInfo
      description: GetCommitmentInfo is called by the provider to retrieve the commitment information.
      operationId: Provider_GetCommitmentInfo
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1CommitmentInfoResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: blockNumber
          description: Optional block number for which to get the commitment information. If not specified all block numbers are returned in ascending order
          in: query
          required: false
          type: string
          format: int64
        - name: page
          description: Optional page number for pagination. Defaults to 0.
          in: query
          required: false
          type: integer
          format: int32
        - name: limit
          description: Optional limit for the number of commitments to return per page. Defaults to 100.
          in: query
          required: false
          type: integer
          format: int32
  /v1/provider/get_min_stake:
    get:
      summary: GetMinStake
      description: GetMinStake is called by the provider to get the minimum stake required to be in the provider registry.
      operationId: Provider_GetMinStake
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1StakeResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
  /v1/provider/get_provider_reward:
    get:
      summary: GetProviderReward
      description: |-
        GetProviderReward is called by the provider to retrieve their current reward balance
        without withdrawing it from the bidder registry.
      operationId: Provider_GetProviderReward
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1RewardResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
  /v1/provider/get_stake:
    get:
      summary: GetStake
      description: GetStake is called by the provider to get its stake in the provider registry.
      operationId: Provider_GetStake
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1StakeResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
  /v1/provider/receive_bids:
    get:
      summary: ReceiveBids
      description: |-
        ReceiveBids is called by the provider to receive bids from the mev-commit node.
        The mev-commit node will stream bids to the provider as the response. The bid can optionally
        have the raw transaction payload in it. The order of the transaction hashes will be the same
        as the raw transaction payloads if included.
      operationId: Provider_ReceiveBids
      responses:
        "200":
          description: A successful response.(streaming responses)
          schema:
            type: object
            properties:
              result:
                $ref: '#/definitions/providerapiv1Bid'
              error:
                $ref: '#/definitions/googlerpcStatus'
            title: Stream result of providerapiv1Bid
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
  /v1/provider/send_processed_bids:
    post:
      summary: SendProcessedBids
      description: |-
        SendProcessedBids is called by the provider to send processed bids to the mev-commit node.
        The provider will stream processed bids to the mev-commit node.
      operationId: Provider_SendProcessedBids
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/providerapiv1EmptyMessage'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: body
          description: Response sent by the provider with the decision on the bid received. (streaming inputs)
          in: body
          required: true
          schema:
            $ref: '#/definitions/v1BidResponse'
  /v1/provider/stake/{amount}:
    post:
      summary: Stake
      description: Stake is called by the provider to register or add to its stake in the provider registry.
      operationId: Provider_Stake
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1StakeResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: amount
          description: Amount of ETH to stake in the provider registry.
          in: path
          required: true
          type: string
        - name: blsPublicKeys
          description: BLS public keys of the provider.
          in: query
          required: false
          type: array
          items:
            type: string
            pattern: ^(0x)?[a-fA-F0-9]{96}$
          collectionFormat: multi
        - name: blsSignatures
          description: BLS signatures corresponding to the BLS public keys.
          in: query
          required: false
          type: array
          items:
            type: string
            pattern: ^(0x)?[a-fA-F0-9]{192}$
          collectionFormat: multi
  /v1/provider/unstake:
    post:
      summary: Unstake
      description: Unstake is called by the provider to request a unstake from the provider registry.
      operationId: Provider_Unstake
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/providerapiv1EmptyMessage'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
  /v1/provider/withdraw_provider_reward:
    post:
      summary: WithdrawProviderReward
      description: |-
        WithdrawProviderReward is called by the provider to withdraw their accumulated rewards
        from the bidder registry contract.
      operationId: Provider_WithdrawProviderReward
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1WithdrawalResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
  /v1/provider/withdraw_stake:
    post:
      summary: WithdrawStake
      description: WithdrawStake is called by the provider to withdraw its stake from the provider registry.
      operationId: Provider_WithdrawStake
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1WithdrawalResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
definitions:
  CommitmentInfoResponseBlockCommitments:
    type: object
    properties:
      blockNumber:
        type: string
        format: int64
        description: Block number for which the commitments are made.
      commitments:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1CommitmentInfoResponseCommitment'
        description: List of commitments made in the block.
  googlerpcStatus:
    type: object
    properties:
      code:
        type: integer
        format: int32
      message:
        type: string
      details:
        type: array
        items:
          type: object
          $ref: '#/definitions/protobufAny'
  protobufAny:
    type: object
    properties:
      '@type':
        type: string
    additionalProperties: {}
  providerapiv1Bid:
    type: object
    example:
      amount: "1000000000000000000"
      bidDigest: 9dJinwL+FZ6B1xsIQQo8t8B0ZXJubJwY86l/Yu7yAH159QrPHU0qj2P+YFj+llbuI1ZygdxGsX8+P3byMEA5ig==
      blockNumber: 123456
      decayEndTimestamp: 1.725365302e+12
      decayStartTimestamp: 1.725365301e+12
      revertingTxHashes:
        - fe4cb47db3630551beedfbd02a71ecc69fd59758e2ba699606e2d5c74284ffa7
      txHashes:
        - fe4cb47db3630551beedfbd02a71ecc69fd59758e2ba699606e2d5c74284ffa7
        - 71c1348f2d7ff7e814f9c3617983703435ea7446de420aeac488bf1de35737e8
    properties:
      txHashes:
        type: array
        items:
          type: string
          pattern: '[a-fA-F0-9]{64}'
        description: Hex string encoding of the hashes of the transactions that the bidder wants to include in the block.
      bidAmount:
        type: string
        description: Amount of ETH that the bidder is willing to pay to the provider for including the transaction in the block.
        pattern: '[0-9]+'
      blockNumber:
        type: string
        format: int64
        description: Max block number that the bidder wants to include the transaction in.
      bidDigest:
        type: string
        format: byte
        description: Digest of the bid message signed by the bidder.
      decayStartTimestamp:
        type: string
        format: int64
        description: Timestamp at which the bid starts decaying.
      decayEndTimestamp:
        type: string
        format: int64
        description: Timestamp at which the bid ends decaying.
      revertingTxHashes:
        type: array
        items:
          type: string
        description: Optional array of tx hashes that are allowed to revert or be discarded.
      rawTransactions:
        type: array
        items:
          type: string
        description: Optional array of RLP encoded raw signed transaction payloads that the bidder wants to include in the block.
      slashAmount:
        type: string
        description: Amount of ETH that will be slashed from the provider if they fail to include the transaction. If zero, the decayed bid amount is used for slashing.
        pattern: '[0-9]+'
      bidOptions:
        $ref: '#/definitions/providerapiv1BidOptions'
        description: Optional bid options for the transaction.
    description: Signed bid message from bidders to the provider.
    title: Bid message
    required:
      - txHashes
      - bidAmount
      - blockNumber
      - bidDigest
  providerapiv1BidOption:
    type: object
    properties:
      positionConstraint:
        $ref: '#/definitions/providerapiv1PositionConstraint'
        description: Position constraint for the transaction in the block.
  providerapiv1BidOptions:
    type: object
    properties:
      options:
        type: array
        items:
          type: object
          $ref: '#/definitions/providerapiv1BidOption'
        description: List of bid options.
    description: Options for the bid.
    title: Bid Options
  providerapiv1EmptyMessage:
    type: object
  providerapiv1PositionConstraint:
    type: object
    properties:
      anchor:
        $ref: '#/definitions/providerapiv1PositionConstraintAnchor'
        description: Anchor position of the transaction in the block.
      basis:
        $ref: '#/definitions/providerapiv1PositionConstraintBasis'
        description: Basis for the position constraint.
      value:
        type: integer
        format: int32
        description: Value of the position constraint. If anchor is TOP, this is the position from the top of the block. If anchor is BOTTOM, this is the position from the bottom of the block.
    description: Constraint on the position of the transaction in the block.
    title: Position Constraint
  providerapiv1PositionConstraintAnchor:
    type: string
    enum:
      - ANCHOR_TOP
      - ANCHOR_BOTTOM
    title: |-
      - ANCHOR_TOP: Position is at the top of the block
       - ANCHOR_BOTTOM: Position is at the bottom of the block
  providerapiv1PositionConstraintBasis:
    type: string
    enum:
      - BASIS_PERCENTILE
      - BASIS_ABSOLUTE
      - BASIS_GAS_PERCENTILE
    title: |-
      - BASIS_PERCENTILE: Position is a percentile of the block size
       - BASIS_ABSOLUTE: Position is an absolute position in the block
       - BASIS_GAS_PERCENTILE: Position is a percentile of the gas used in the block
  v1BidResponse:
    type: object
    example:
      bidDigest: 9dJinwL+FZ6B1xsIQQo8t8B0ZXJubJwY86l/Yu7yAH159QrPHU0qj2P+YFj+llbuI1ZygdxGsX8+P3byMEA5ig==
      decayDispatchTimestamp: 1.23456789e+09
      status: STATUS_ACCEPTED
    properties:
      bidDigest:
        type: string
        format: byte
        description: Digest of the bid message signed by the bidder.
      status:
        $ref: '#/definitions/v1BidResponseStatus'
        description: Status of the bid.
      dispatchTimestamp:
        type: string
        format: int64
        description: Timestamp at which the commitment is accepted by provider and is used to compute the expected revenue from the preconfirmation
    description: Response sent by the provider with the decision on the bid received.
    title: Bid response
    required:
      - bidDigest
      - status
      - decayDispatchTimestamp
  v1BidResponseStatus:
    type: string
    enum:
      - STATUS_ACCEPTED
      - STATUS_REJECTED
  v1CommitmentInfoResponse:
    type: object
    properties:
      commitments:
        type: array
        items:
          type: object
          $ref: '#/definitions/CommitmentInfoResponseBlockCommitments'
        description: List of commitments made in the block.
    description: Response containing the commitment information.
    title: Commitment info response
    required:
      - commitments
  v1CommitmentInfoResponseCommitment:
    type: object
    properties:
      txnHashes:
        type: array
        items:
          type: string
          pattern: '[a-fA-F0-9]{64}'
        description: List of transaction hashes that are part of the commitment.
      revertableTxnHashes:
        type: array
        items:
          type: string
          pattern: '[a-fA-F0-9]{64}'
        description: List of transaction hashes that are allowed to revert.
      amount:
        type: string
        description: Amount of ETH in wei committed by the bidder.
        pattern: '[0-9]+'
      blockNumber:
        type: string
        format: int64
        description: Block number at which the commitment is made.
      providerAddress:
        type: string
        description: Hex string encoding of the address of the provider that signed the commitment signature.
      decayStartTimestamp:
        type: string
        format: int64
        description: Timestamp at which the bid starts decaying.
      decayEndTimestamp:
        type: string
        format: int64
        description: Timestamp at which the bid ends decaying.
      dispatchTimestamp:
        type: string
        format: int64
        description: Timestamp at which the commitment is published.
      slashAmount:
        type: string
        description: Amount of ETH that will be slashed from the provider if they fail to include the transaction.
      status:
        type: string
        description: 'Status of the commitment. Possible values: ''pending'', ''stored'', ''opened'', ''settled'', ''slashed'', ''failed''.'
      details:
        type: string
        description: Additional details about the commitment status.
      payment:
        type: string
        description: Payment amount in wei for the commitment.
      refund:
        type: string
        description: Refund amount in wei for the commitment, if applicable.
      bidOptions:
        $ref: '#/definitions/providerapiv1BidOptions'
        description: Optional bid options for the transaction.
  v1RewardResponse:
    type: object
    example:
      amount: "500000000000000000"
    properties:
      amount:
        type: string
    description: Current reward amount for provider in the bidder registry.
    title: Reward response
  v1StakeResponse:
    type: object
    example:
      amount: "2000000000000000000"
      bls_public_keys:
        - 90000cddeec66a80e00b0ccbb62f12298073603f5209e812abbac7e870482e488dd1bbe533a9d4497ba8b756e1e82b
        - 80000cddeec66a80e00b0ccbb62f12298073603f5209e812abbac7e870482e488dd1bbe533a9d4497ba8b756e1e82b
    properties:
      amount:
        type: string
      blsPublicKeys:
        type: array
        items:
          type: string
    description: Get staked amount for provider in the provider registry.
    title: Stake response
  v1WithdrawalResponse:
    type: object
    example:
      amount: "1000000000000000000"
    properties:
      amount:
        type: string
    description: Withdrawal amount for provider in the provider registry.
    title: Withdrawal response

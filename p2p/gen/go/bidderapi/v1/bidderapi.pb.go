// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        (unknown)
// source: bidderapi/v1/bidderapi.proto

package bidderapiv1

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PositionConstraint_Anchor int32

const (
	PositionConstraint_ANCHOR_UNSPECIFIED PositionConstraint_Anchor = 0
	PositionConstraint_ANCHOR_TOP         PositionConstraint_Anchor = 1 // Position is at the top of the block
	PositionConstraint_ANCHOR_BOTTOM      PositionConstraint_Anchor = 2 // Position is at the bottom of the block
)

// Enum value maps for PositionConstraint_Anchor.
var (
	PositionConstraint_Anchor_name = map[int32]string{
		0: "ANCHOR_UNSPECIFIED",
		1: "ANCHOR_TOP",
		2: "ANCHOR_BOTTOM",
	}
	PositionConstraint_Anchor_value = map[string]int32{
		"ANCHOR_UNSPECIFIED": 0,
		"ANCHOR_TOP":         1,
		"ANCHOR_BOTTOM":      2,
	}
)

func (x PositionConstraint_Anchor) Enum() *PositionConstraint_Anchor {
	p := new(PositionConstraint_Anchor)
	*p = x
	return p
}

func (x PositionConstraint_Anchor) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PositionConstraint_Anchor) Descriptor() protoreflect.EnumDescriptor {
	return file_bidderapi_v1_bidderapi_proto_enumTypes[0].Descriptor()
}

func (PositionConstraint_Anchor) Type() protoreflect.EnumType {
	return &file_bidderapi_v1_bidderapi_proto_enumTypes[0]
}

func (x PositionConstraint_Anchor) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PositionConstraint_Anchor.Descriptor instead.
func (PositionConstraint_Anchor) EnumDescriptor() ([]byte, []int) {
	return file_bidderapi_v1_bidderapi_proto_rawDescGZIP(), []int{24, 0}
}

type PositionConstraint_Basis int32

const (
	PositionConstraint_BASIS_UNSPECIFIED    PositionConstraint_Basis = 0
	PositionConstraint_BASIS_PERCENTILE     PositionConstraint_Basis = 1 // Position is a percentile of the block size
	PositionConstraint_BASIS_ABSOLUTE       PositionConstraint_Basis = 2 // Position is an absolute position in the block
	PositionConstraint_BASIS_GAS_PERCENTILE PositionConstraint_Basis = 3 // Position is a percentile of the gas used in the block
)

// Enum value maps for PositionConstraint_Basis.
var (
	PositionConstraint_Basis_name = map[int32]string{
		0: "BASIS_UNSPECIFIED",
		1: "BASIS_PERCENTILE",
		2: "BASIS_ABSOLUTE",
		3: "BASIS_GAS_PERCENTILE",
	}
	PositionConstraint_Basis_value = map[string]int32{
		"BASIS_UNSPECIFIED":    0,
		"BASIS_PERCENTILE":     1,
		"BASIS_ABSOLUTE":       2,
		"BASIS_GAS_PERCENTILE": 3,
	}
)

func (x PositionConstraint_Basis) Enum() *PositionConstraint_Basis {
	p := new(PositionConstraint_Basis)
	*p = x
	return p
}

func (x PositionConstraint_Basis) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PositionConstraint_Basis) Descriptor() protoreflect.EnumDescriptor {
	return file_bidderapi_v1_bidderapi_proto_enumTypes[1].Descriptor()
}

func (PositionConstraint_Basis) Type() protoreflect.EnumType {
	return &file_bidderapi_v1_bidderapi_proto_enumTypes[1]
}

func (x PositionConstraint_Basis) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PositionConstraint_Basis.Descriptor instead.
func (PositionConstraint_Basis) EnumDescriptor() ([]byte, []int) {
	return file_bidderapi_v1_bidderapi_proto_rawDescGZIP(), []int{24, 1}
}

type DepositRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Amount   string `protobuf:"bytes,1,opt,name=amount,proto3" json:"amount,omitempty"`
	Provider string `protobuf:"bytes,2,opt,name=provider,proto3" json:"provider,omitempty"`
}

func (x *DepositRequest) Reset() {
	*x = DepositRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DepositRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepositRequest) ProtoMessage() {}

func (x *DepositRequest) ProtoReflect() protoreflect.Message {
	mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepositRequest.ProtoReflect.Descriptor instead.
func (*DepositRequest) Descriptor() ([]byte, []int) {
	return file_bidderapi_v1_bidderapi_proto_rawDescGZIP(), []int{0}
}

func (x *DepositRequest) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

func (x *DepositRequest) GetProvider() string {
	if x != nil {
		return x.Provider
	}
	return ""
}

type DepositResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Amount   string `protobuf:"bytes,1,opt,name=amount,proto3" json:"amount,omitempty"`
	Provider string `protobuf:"bytes,2,opt,name=provider,proto3" json:"provider,omitempty"`
}

func (x *DepositResponse) Reset() {
	*x = DepositResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DepositResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepositResponse) ProtoMessage() {}

func (x *DepositResponse) ProtoReflect() protoreflect.Message {
	mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepositResponse.ProtoReflect.Descriptor instead.
func (*DepositResponse) Descriptor() ([]byte, []int) {
	return file_bidderapi_v1_bidderapi_proto_rawDescGZIP(), []int{1}
}

func (x *DepositResponse) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

func (x *DepositResponse) GetProvider() string {
	if x != nil {
		return x.Provider
	}
	return ""
}

type DepositEvenlyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TotalAmount string   `protobuf:"bytes,1,opt,name=total_amount,json=totalAmount,proto3" json:"total_amount,omitempty"`
	Providers   []string `protobuf:"bytes,2,rep,name=providers,proto3" json:"providers,omitempty"`
}

func (x *DepositEvenlyRequest) Reset() {
	*x = DepositEvenlyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DepositEvenlyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepositEvenlyRequest) ProtoMessage() {}

func (x *DepositEvenlyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepositEvenlyRequest.ProtoReflect.Descriptor instead.
func (*DepositEvenlyRequest) Descriptor() ([]byte, []int) {
	return file_bidderapi_v1_bidderapi_proto_rawDescGZIP(), []int{2}
}

func (x *DepositEvenlyRequest) GetTotalAmount() string {
	if x != nil {
		return x.TotalAmount
	}
	return ""
}

func (x *DepositEvenlyRequest) GetProviders() []string {
	if x != nil {
		return x.Providers
	}
	return nil
}

type DepositEvenlyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Providers []string `protobuf:"bytes,1,rep,name=providers,proto3" json:"providers,omitempty"`
	Amounts   []string `protobuf:"bytes,2,rep,name=amounts,proto3" json:"amounts,omitempty"`
}

func (x *DepositEvenlyResponse) Reset() {
	*x = DepositEvenlyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DepositEvenlyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepositEvenlyResponse) ProtoMessage() {}

func (x *DepositEvenlyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepositEvenlyResponse.ProtoReflect.Descriptor instead.
func (*DepositEvenlyResponse) Descriptor() ([]byte, []int) {
	return file_bidderapi_v1_bidderapi_proto_rawDescGZIP(), []int{3}
}

func (x *DepositEvenlyResponse) GetProviders() []string {
	if x != nil {
		return x.Providers
	}
	return nil
}

func (x *DepositEvenlyResponse) GetAmounts() []string {
	if x != nil {
		return x.Amounts
	}
	return nil
}

type EnableDepositManagerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EnableDepositManagerRequest) Reset() {
	*x = EnableDepositManagerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnableDepositManagerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnableDepositManagerRequest) ProtoMessage() {}

func (x *EnableDepositManagerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnableDepositManagerRequest.ProtoReflect.Descriptor instead.
func (*EnableDepositManagerRequest) Descriptor() ([]byte, []int) {
	return file_bidderapi_v1_bidderapi_proto_rawDescGZIP(), []int{4}
}

type EnableDepositManagerResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *EnableDepositManagerResponse) Reset() {
	*x = EnableDepositManagerResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnableDepositManagerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnableDepositManagerResponse) ProtoMessage() {}

func (x *EnableDepositManagerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnableDepositManagerResponse.ProtoReflect.Descriptor instead.
func (*EnableDepositManagerResponse) Descriptor() ([]byte, []int) {
	return file_bidderapi_v1_bidderapi_proto_rawDescGZIP(), []int{5}
}

func (x *EnableDepositManagerResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type DisableDepositManagerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DisableDepositManagerRequest) Reset() {
	*x = DisableDepositManagerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DisableDepositManagerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DisableDepositManagerRequest) ProtoMessage() {}

func (x *DisableDepositManagerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DisableDepositManagerRequest.ProtoReflect.Descriptor instead.
func (*DisableDepositManagerRequest) Descriptor() ([]byte, []int) {
	return file_bidderapi_v1_bidderapi_proto_rawDescGZIP(), []int{6}
}

type DisableDepositManagerResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *DisableDepositManagerResponse) Reset() {
	*x = DisableDepositManagerResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DisableDepositManagerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DisableDepositManagerResponse) ProtoMessage() {}

func (x *DisableDepositManagerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DisableDepositManagerResponse.ProtoReflect.Descriptor instead.
func (*DisableDepositManagerResponse) Descriptor() ([]byte, []int) {
	return file_bidderapi_v1_bidderapi_proto_rawDescGZIP(), []int{7}
}

func (x *DisableDepositManagerResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type TargetDeposit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Provider      string `protobuf:"bytes,1,opt,name=provider,proto3" json:"provider,omitempty"`
	TargetDeposit string `protobuf:"bytes,2,opt,name=target_deposit,json=targetDeposit,proto3" json:"target_deposit,omitempty"`
}

func (x *TargetDeposit) Reset() {
	*x = TargetDeposit{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TargetDeposit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TargetDeposit) ProtoMessage() {}

func (x *TargetDeposit) ProtoReflect() protoreflect.Message {
	mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TargetDeposit.ProtoReflect.Descriptor instead.
func (*TargetDeposit) Descriptor() ([]byte, []int) {
	return file_bidderapi_v1_bidderapi_proto_rawDescGZIP(), []int{8}
}

func (x *TargetDeposit) GetProvider() string {
	if x != nil {
		return x.Provider
	}
	return ""
}

func (x *TargetDeposit) GetTargetDeposit() string {
	if x != nil {
		return x.TargetDeposit
	}
	return ""
}

type SetTargetDepositsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TargetDeposits []*TargetDeposit `protobuf:"bytes,1,rep,name=target_deposits,json=targetDeposits,proto3" json:"target_deposits,omitempty"`
}

func (x *SetTargetDepositsRequest) Reset() {
	*x = SetTargetDepositsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetTargetDepositsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetTargetDepositsRequest) ProtoMessage() {}

func (x *SetTargetDepositsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetTargetDepositsRequest.ProtoReflect.Descriptor instead.
func (*SetTargetDepositsRequest) Descriptor() ([]byte, []int) {
	return file_bidderapi_v1_bidderapi_proto_rawDescGZIP(), []int{9}
}

func (x *SetTargetDepositsRequest) GetTargetDeposits() []*TargetDeposit {
	if x != nil {
		return x.TargetDeposits
	}
	return nil
}

type SetTargetDepositsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SuccessfullySetDeposits       []*TargetDeposit `protobuf:"bytes,1,rep,name=successfully_set_deposits,json=successfullySetDeposits,proto3" json:"successfully_set_deposits,omitempty"`
	SuccessfullyToppedUpProviders []string         `protobuf:"bytes,2,rep,name=successfully_topped_up_providers,json=successfullyToppedUpProviders,proto3" json:"successfully_topped_up_providers,omitempty"`
}

func (x *SetTargetDepositsResponse) Reset() {
	*x = SetTargetDepositsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetTargetDepositsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetTargetDepositsResponse) ProtoMessage() {}

func (x *SetTargetDepositsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetTargetDepositsResponse.ProtoReflect.Descriptor instead.
func (*SetTargetDepositsResponse) Descriptor() ([]byte, []int) {
	return file_bidderapi_v1_bidderapi_proto_rawDescGZIP(), []int{10}
}

func (x *SetTargetDepositsResponse) GetSuccessfullySetDeposits() []*TargetDeposit {
	if x != nil {
		return x.SuccessfullySetDeposits
	}
	return nil
}

func (x *SetTargetDepositsResponse) GetSuccessfullyToppedUpProviders() []string {
	if x != nil {
		return x.SuccessfullyToppedUpProviders
	}
	return nil
}

type DepositManagerStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DepositManagerStatusRequest) Reset() {
	*x = DepositManagerStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DepositManagerStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepositManagerStatusRequest) ProtoMessage() {}

func (x *DepositManagerStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepositManagerStatusRequest.ProtoReflect.Descriptor instead.
func (*DepositManagerStatusRequest) Descriptor() ([]byte, []int) {
	return file_bidderapi_v1_bidderapi_proto_rawDescGZIP(), []int{11}
}

type DepositManagerStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enabled        bool             `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	TargetDeposits []*TargetDeposit `protobuf:"bytes,2,rep,name=target_deposits,json=targetDeposits,proto3" json:"target_deposits,omitempty"`
}

func (x *DepositManagerStatusResponse) Reset() {
	*x = DepositManagerStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DepositManagerStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepositManagerStatusResponse) ProtoMessage() {}

func (x *DepositManagerStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepositManagerStatusResponse.ProtoReflect.Descriptor instead.
func (*DepositManagerStatusResponse) Descriptor() ([]byte, []int) {
	return file_bidderapi_v1_bidderapi_proto_rawDescGZIP(), []int{12}
}

func (x *DepositManagerStatusResponse) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *DepositManagerStatusResponse) GetTargetDeposits() []*TargetDeposit {
	if x != nil {
		return x.TargetDeposits
	}
	return nil
}

type EmptyMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EmptyMessage) Reset() {
	*x = EmptyMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmptyMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmptyMessage) ProtoMessage() {}

func (x *EmptyMessage) ProtoReflect() protoreflect.Message {
	mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmptyMessage.ProtoReflect.Descriptor instead.
func (*EmptyMessage) Descriptor() ([]byte, []int) {
	return file_bidderapi_v1_bidderapi_proto_rawDescGZIP(), []int{13}
}

type GetDepositRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Provider string `protobuf:"bytes,1,opt,name=provider,proto3" json:"provider,omitempty"`
}

func (x *GetDepositRequest) Reset() {
	*x = GetDepositRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDepositRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDepositRequest) ProtoMessage() {}

func (x *GetDepositRequest) ProtoReflect() protoreflect.Message {
	mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDepositRequest.ProtoReflect.Descriptor instead.
func (*GetDepositRequest) Descriptor() ([]byte, []int) {
	return file_bidderapi_v1_bidderapi_proto_rawDescGZIP(), []int{14}
}

func (x *GetDepositRequest) GetProvider() string {
	if x != nil {
		return x.Provider
	}
	return ""
}

type GetAllDepositsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetAllDepositsRequest) Reset() {
	*x = GetAllDepositsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAllDepositsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllDepositsRequest) ProtoMessage() {}

func (x *GetAllDepositsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllDepositsRequest.ProtoReflect.Descriptor instead.
func (*GetAllDepositsRequest) Descriptor() ([]byte, []int) {
	return file_bidderapi_v1_bidderapi_proto_rawDescGZIP(), []int{15}
}

type DepositInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Provider string `protobuf:"bytes,1,opt,name=provider,proto3" json:"provider,omitempty"`
	Amount   string `protobuf:"bytes,2,opt,name=amount,proto3" json:"amount,omitempty"`
}

func (x *DepositInfo) Reset() {
	*x = DepositInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DepositInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepositInfo) ProtoMessage() {}

func (x *DepositInfo) ProtoReflect() protoreflect.Message {
	mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepositInfo.ProtoReflect.Descriptor instead.
func (*DepositInfo) Descriptor() ([]byte, []int) {
	return file_bidderapi_v1_bidderapi_proto_rawDescGZIP(), []int{16}
}

func (x *DepositInfo) GetProvider() string {
	if x != nil {
		return x.Provider
	}
	return ""
}

func (x *DepositInfo) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

type GetAllDepositsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Deposits      []*DepositInfo `protobuf:"bytes,1,rep,name=deposits,proto3" json:"deposits,omitempty"`
	BidderBalance string         `protobuf:"bytes,2,opt,name=bidder_balance,json=bidderBalance,proto3" json:"bidder_balance,omitempty"`
}

func (x *GetAllDepositsResponse) Reset() {
	*x = GetAllDepositsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAllDepositsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllDepositsResponse) ProtoMessage() {}

func (x *GetAllDepositsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllDepositsResponse.ProtoReflect.Descriptor instead.
func (*GetAllDepositsResponse) Descriptor() ([]byte, []int) {
	return file_bidderapi_v1_bidderapi_proto_rawDescGZIP(), []int{17}
}

func (x *GetAllDepositsResponse) GetDeposits() []*DepositInfo {
	if x != nil {
		return x.Deposits
	}
	return nil
}

func (x *GetAllDepositsResponse) GetBidderBalance() string {
	if x != nil {
		return x.BidderBalance
	}
	return ""
}

type RequestWithdrawalsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Providers []string `protobuf:"bytes,1,rep,name=providers,proto3" json:"providers,omitempty"`
}

func (x *RequestWithdrawalsRequest) Reset() {
	*x = RequestWithdrawalsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RequestWithdrawalsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestWithdrawalsRequest) ProtoMessage() {}

func (x *RequestWithdrawalsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestWithdrawalsRequest.ProtoReflect.Descriptor instead.
func (*RequestWithdrawalsRequest) Descriptor() ([]byte, []int) {
	return file_bidderapi_v1_bidderapi_proto_rawDescGZIP(), []int{18}
}

func (x *RequestWithdrawalsRequest) GetProviders() []string {
	if x != nil {
		return x.Providers
	}
	return nil
}

type RequestWithdrawalsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Providers []string `protobuf:"bytes,1,rep,name=providers,proto3" json:"providers,omitempty"`
	Amounts   []string `protobuf:"bytes,2,rep,name=amounts,proto3" json:"amounts,omitempty"`
}

func (x *RequestWithdrawalsResponse) Reset() {
	*x = RequestWithdrawalsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RequestWithdrawalsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestWithdrawalsResponse) ProtoMessage() {}

func (x *RequestWithdrawalsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestWithdrawalsResponse.ProtoReflect.Descriptor instead.
func (*RequestWithdrawalsResponse) Descriptor() ([]byte, []int) {
	return file_bidderapi_v1_bidderapi_proto_rawDescGZIP(), []int{19}
}

func (x *RequestWithdrawalsResponse) GetProviders() []string {
	if x != nil {
		return x.Providers
	}
	return nil
}

func (x *RequestWithdrawalsResponse) GetAmounts() []string {
	if x != nil {
		return x.Amounts
	}
	return nil
}

type WithdrawRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Providers []string `protobuf:"bytes,1,rep,name=providers,proto3" json:"providers,omitempty"`
}

func (x *WithdrawRequest) Reset() {
	*x = WithdrawRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WithdrawRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WithdrawRequest) ProtoMessage() {}

func (x *WithdrawRequest) ProtoReflect() protoreflect.Message {
	mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WithdrawRequest.ProtoReflect.Descriptor instead.
func (*WithdrawRequest) Descriptor() ([]byte, []int) {
	return file_bidderapi_v1_bidderapi_proto_rawDescGZIP(), []int{20}
}

func (x *WithdrawRequest) GetProviders() []string {
	if x != nil {
		return x.Providers
	}
	return nil
}

type WithdrawResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Amounts   []string `protobuf:"bytes,1,rep,name=amounts,proto3" json:"amounts,omitempty"`
	Providers []string `protobuf:"bytes,2,rep,name=providers,proto3" json:"providers,omitempty"`
}

func (x *WithdrawResponse) Reset() {
	*x = WithdrawResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WithdrawResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WithdrawResponse) ProtoMessage() {}

func (x *WithdrawResponse) ProtoReflect() protoreflect.Message {
	mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WithdrawResponse.ProtoReflect.Descriptor instead.
func (*WithdrawResponse) Descriptor() ([]byte, []int) {
	return file_bidderapi_v1_bidderapi_proto_rawDescGZIP(), []int{21}
}

func (x *WithdrawResponse) GetAmounts() []string {
	if x != nil {
		return x.Amounts
	}
	return nil
}

func (x *WithdrawResponse) GetProviders() []string {
	if x != nil {
		return x.Providers
	}
	return nil
}

type GetValidProvidersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetValidProvidersRequest) Reset() {
	*x = GetValidProvidersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetValidProvidersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetValidProvidersRequest) ProtoMessage() {}

func (x *GetValidProvidersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetValidProvidersRequest.ProtoReflect.Descriptor instead.
func (*GetValidProvidersRequest) Descriptor() ([]byte, []int) {
	return file_bidderapi_v1_bidderapi_proto_rawDescGZIP(), []int{22}
}

type GetValidProvidersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ValidProviders []string `protobuf:"bytes,1,rep,name=valid_providers,json=validProviders,proto3" json:"valid_providers,omitempty"`
}

func (x *GetValidProvidersResponse) Reset() {
	*x = GetValidProvidersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetValidProvidersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetValidProvidersResponse) ProtoMessage() {}

func (x *GetValidProvidersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetValidProvidersResponse.ProtoReflect.Descriptor instead.
func (*GetValidProvidersResponse) Descriptor() ([]byte, []int) {
	return file_bidderapi_v1_bidderapi_proto_rawDescGZIP(), []int{23}
}

func (x *GetValidProvidersResponse) GetValidProviders() []string {
	if x != nil {
		return x.ValidProviders
	}
	return nil
}

type PositionConstraint struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Anchor PositionConstraint_Anchor `protobuf:"varint,1,opt,name=anchor,proto3,enum=bidderapi.v1.PositionConstraint_Anchor" json:"anchor,omitempty"`
	Basis  PositionConstraint_Basis  `protobuf:"varint,2,opt,name=basis,proto3,enum=bidderapi.v1.PositionConstraint_Basis" json:"basis,omitempty"`
	Value  int32                     `protobuf:"varint,3,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *PositionConstraint) Reset() {
	*x = PositionConstraint{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PositionConstraint) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PositionConstraint) ProtoMessage() {}

func (x *PositionConstraint) ProtoReflect() protoreflect.Message {
	mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PositionConstraint.ProtoReflect.Descriptor instead.
func (*PositionConstraint) Descriptor() ([]byte, []int) {
	return file_bidderapi_v1_bidderapi_proto_rawDescGZIP(), []int{24}
}

func (x *PositionConstraint) GetAnchor() PositionConstraint_Anchor {
	if x != nil {
		return x.Anchor
	}
	return PositionConstraint_ANCHOR_UNSPECIFIED
}

func (x *PositionConstraint) GetBasis() PositionConstraint_Basis {
	if x != nil {
		return x.Basis
	}
	return PositionConstraint_BASIS_UNSPECIFIED
}

func (x *PositionConstraint) GetValue() int32 {
	if x != nil {
		return x.Value
	}
	return 0
}

type BidOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Opt:
	//
	//	*BidOption_PositionConstraint
	Opt isBidOption_Opt `protobuf_oneof:"opt"`
}

func (x *BidOption) Reset() {
	*x = BidOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidOption) ProtoMessage() {}

func (x *BidOption) ProtoReflect() protoreflect.Message {
	mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidOption.ProtoReflect.Descriptor instead.
func (*BidOption) Descriptor() ([]byte, []int) {
	return file_bidderapi_v1_bidderapi_proto_rawDescGZIP(), []int{25}
}

func (m *BidOption) GetOpt() isBidOption_Opt {
	if m != nil {
		return m.Opt
	}
	return nil
}

func (x *BidOption) GetPositionConstraint() *PositionConstraint {
	if x, ok := x.GetOpt().(*BidOption_PositionConstraint); ok {
		return x.PositionConstraint
	}
	return nil
}

type isBidOption_Opt interface {
	isBidOption_Opt()
}

type BidOption_PositionConstraint struct {
	PositionConstraint *PositionConstraint `protobuf:"bytes,1,opt,name=position_constraint,json=positionConstraint,proto3,oneof"`
}

func (*BidOption_PositionConstraint) isBidOption_Opt() {}

type BidOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Options []*BidOption `protobuf:"bytes,1,rep,name=options,proto3" json:"options,omitempty"`
}

func (x *BidOptions) Reset() {
	*x = BidOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidOptions) ProtoMessage() {}

func (x *BidOptions) ProtoReflect() protoreflect.Message {
	mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidOptions.ProtoReflect.Descriptor instead.
func (*BidOptions) Descriptor() ([]byte, []int) {
	return file_bidderapi_v1_bidderapi_proto_rawDescGZIP(), []int{26}
}

func (x *BidOptions) GetOptions() []*BidOption {
	if x != nil {
		return x.Options
	}
	return nil
}

type Bid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TxHashes            []string    `protobuf:"bytes,1,rep,name=tx_hashes,json=txHashes,proto3" json:"tx_hashes,omitempty"`
	Amount              string      `protobuf:"bytes,2,opt,name=amount,proto3" json:"amount,omitempty"`
	BlockNumber         int64       `protobuf:"varint,3,opt,name=block_number,json=blockNumber,proto3" json:"block_number,omitempty"`
	DecayStartTimestamp int64       `protobuf:"varint,4,opt,name=decay_start_timestamp,json=decayStartTimestamp,proto3" json:"decay_start_timestamp,omitempty"`
	DecayEndTimestamp   int64       `protobuf:"varint,5,opt,name=decay_end_timestamp,json=decayEndTimestamp,proto3" json:"decay_end_timestamp,omitempty"`
	RevertingTxHashes   []string    `protobuf:"bytes,6,rep,name=reverting_tx_hashes,json=revertingTxHashes,proto3" json:"reverting_tx_hashes,omitempty"`
	RawTransactions     []string    `protobuf:"bytes,7,rep,name=raw_transactions,json=rawTransactions,proto3" json:"raw_transactions,omitempty"`
	SlashAmount         string      `protobuf:"bytes,8,opt,name=slash_amount,json=slashAmount,proto3" json:"slash_amount,omitempty"`
	BidOptions          *BidOptions `protobuf:"bytes,9,opt,name=bid_options,json=bidOptions,proto3" json:"bid_options,omitempty"`
}

func (x *Bid) Reset() {
	*x = Bid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Bid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Bid) ProtoMessage() {}

func (x *Bid) ProtoReflect() protoreflect.Message {
	mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Bid.ProtoReflect.Descriptor instead.
func (*Bid) Descriptor() ([]byte, []int) {
	return file_bidderapi_v1_bidderapi_proto_rawDescGZIP(), []int{27}
}

func (x *Bid) GetTxHashes() []string {
	if x != nil {
		return x.TxHashes
	}
	return nil
}

func (x *Bid) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

func (x *Bid) GetBlockNumber() int64 {
	if x != nil {
		return x.BlockNumber
	}
	return 0
}

func (x *Bid) GetDecayStartTimestamp() int64 {
	if x != nil {
		return x.DecayStartTimestamp
	}
	return 0
}

func (x *Bid) GetDecayEndTimestamp() int64 {
	if x != nil {
		return x.DecayEndTimestamp
	}
	return 0
}

func (x *Bid) GetRevertingTxHashes() []string {
	if x != nil {
		return x.RevertingTxHashes
	}
	return nil
}

func (x *Bid) GetRawTransactions() []string {
	if x != nil {
		return x.RawTransactions
	}
	return nil
}

func (x *Bid) GetSlashAmount() string {
	if x != nil {
		return x.SlashAmount
	}
	return ""
}

func (x *Bid) GetBidOptions() *BidOptions {
	if x != nil {
		return x.BidOptions
	}
	return nil
}

type Commitment struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TxHashes             []string    `protobuf:"bytes,1,rep,name=tx_hashes,json=txHashes,proto3" json:"tx_hashes,omitempty"`
	BidAmount            string      `protobuf:"bytes,2,opt,name=bid_amount,json=bidAmount,proto3" json:"bid_amount,omitempty"`
	BlockNumber          int64       `protobuf:"varint,3,opt,name=block_number,json=blockNumber,proto3" json:"block_number,omitempty"`
	ReceivedBidDigest    string      `protobuf:"bytes,4,opt,name=received_bid_digest,json=receivedBidDigest,proto3" json:"received_bid_digest,omitempty"`
	ReceivedBidSignature string      `protobuf:"bytes,5,opt,name=received_bid_signature,json=receivedBidSignature,proto3" json:"received_bid_signature,omitempty"`
	CommitmentDigest     string      `protobuf:"bytes,6,opt,name=commitment_digest,json=commitmentDigest,proto3" json:"commitment_digest,omitempty"`
	CommitmentSignature  string      `protobuf:"bytes,7,opt,name=commitment_signature,json=commitmentSignature,proto3" json:"commitment_signature,omitempty"`
	ProviderAddress      string      `protobuf:"bytes,8,opt,name=provider_address,json=providerAddress,proto3" json:"provider_address,omitempty"`
	DecayStartTimestamp  int64       `protobuf:"varint,9,opt,name=decay_start_timestamp,json=decayStartTimestamp,proto3" json:"decay_start_timestamp,omitempty"`
	DecayEndTimestamp    int64       `protobuf:"varint,10,opt,name=decay_end_timestamp,json=decayEndTimestamp,proto3" json:"decay_end_timestamp,omitempty"`
	DispatchTimestamp    int64       `protobuf:"varint,11,opt,name=dispatch_timestamp,json=dispatchTimestamp,proto3" json:"dispatch_timestamp,omitempty"`
	RevertingTxHashes    []string    `protobuf:"bytes,12,rep,name=reverting_tx_hashes,json=revertingTxHashes,proto3" json:"reverting_tx_hashes,omitempty"`
	SlashAmount          string      `protobuf:"bytes,13,opt,name=slash_amount,json=slashAmount,proto3" json:"slash_amount,omitempty"`
	BidOptions           *BidOptions `protobuf:"bytes,14,opt,name=bid_options,json=bidOptions,proto3" json:"bid_options,omitempty"`
}

func (x *Commitment) Reset() {
	*x = Commitment{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Commitment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Commitment) ProtoMessage() {}

func (x *Commitment) ProtoReflect() protoreflect.Message {
	mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Commitment.ProtoReflect.Descriptor instead.
func (*Commitment) Descriptor() ([]byte, []int) {
	return file_bidderapi_v1_bidderapi_proto_rawDescGZIP(), []int{28}
}

func (x *Commitment) GetTxHashes() []string {
	if x != nil {
		return x.TxHashes
	}
	return nil
}

func (x *Commitment) GetBidAmount() string {
	if x != nil {
		return x.BidAmount
	}
	return ""
}

func (x *Commitment) GetBlockNumber() int64 {
	if x != nil {
		return x.BlockNumber
	}
	return 0
}

func (x *Commitment) GetReceivedBidDigest() string {
	if x != nil {
		return x.ReceivedBidDigest
	}
	return ""
}

func (x *Commitment) GetReceivedBidSignature() string {
	if x != nil {
		return x.ReceivedBidSignature
	}
	return ""
}

func (x *Commitment) GetCommitmentDigest() string {
	if x != nil {
		return x.CommitmentDigest
	}
	return ""
}

func (x *Commitment) GetCommitmentSignature() string {
	if x != nil {
		return x.CommitmentSignature
	}
	return ""
}

func (x *Commitment) GetProviderAddress() string {
	if x != nil {
		return x.ProviderAddress
	}
	return ""
}

func (x *Commitment) GetDecayStartTimestamp() int64 {
	if x != nil {
		return x.DecayStartTimestamp
	}
	return 0
}

func (x *Commitment) GetDecayEndTimestamp() int64 {
	if x != nil {
		return x.DecayEndTimestamp
	}
	return 0
}

func (x *Commitment) GetDispatchTimestamp() int64 {
	if x != nil {
		return x.DispatchTimestamp
	}
	return 0
}

func (x *Commitment) GetRevertingTxHashes() []string {
	if x != nil {
		return x.RevertingTxHashes
	}
	return nil
}

func (x *Commitment) GetSlashAmount() string {
	if x != nil {
		return x.SlashAmount
	}
	return ""
}

func (x *Commitment) GetBidOptions() *BidOptions {
	if x != nil {
		return x.BidOptions
	}
	return nil
}

type GetBidInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BlockNumber int64 `protobuf:"varint,1,opt,name=block_number,json=blockNumber,proto3" json:"block_number,omitempty"`
	Page        int32 `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	Limit       int32 `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
}

func (x *GetBidInfoRequest) Reset() {
	*x = GetBidInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBidInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBidInfoRequest) ProtoMessage() {}

func (x *GetBidInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBidInfoRequest.ProtoReflect.Descriptor instead.
func (*GetBidInfoRequest) Descriptor() ([]byte, []int) {
	return file_bidderapi_v1_bidderapi_proto_rawDescGZIP(), []int{29}
}

func (x *GetBidInfoRequest) GetBlockNumber() int64 {
	if x != nil {
		return x.BlockNumber
	}
	return 0
}

func (x *GetBidInfoRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetBidInfoRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

type GetBidInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BlockBidInfo []*GetBidInfoResponse_BlockBidInfo `protobuf:"bytes,1,rep,name=block_bid_info,json=blockBidInfo,proto3" json:"block_bid_info,omitempty"`
}

func (x *GetBidInfoResponse) Reset() {
	*x = GetBidInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBidInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBidInfoResponse) ProtoMessage() {}

func (x *GetBidInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBidInfoResponse.ProtoReflect.Descriptor instead.
func (*GetBidInfoResponse) Descriptor() ([]byte, []int) {
	return file_bidderapi_v1_bidderapi_proto_rawDescGZIP(), []int{30}
}

func (x *GetBidInfoResponse) GetBlockBidInfo() []*GetBidInfoResponse_BlockBidInfo {
	if x != nil {
		return x.BlockBidInfo
	}
	return nil
}

type GetBidInfoResponse_CommitmentWithStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProviderAddress   string `protobuf:"bytes,1,opt,name=provider_address,json=providerAddress,proto3" json:"provider_address,omitempty"`
	DispatchTimestamp int64  `protobuf:"varint,2,opt,name=dispatch_timestamp,json=dispatchTimestamp,proto3" json:"dispatch_timestamp,omitempty"`
	Status            string `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`
	Details           string `protobuf:"bytes,4,opt,name=details,proto3" json:"details,omitempty"`
	Payment           string `protobuf:"bytes,5,opt,name=payment,proto3" json:"payment,omitempty"`
	Refund            string `protobuf:"bytes,6,opt,name=refund,proto3" json:"refund,omitempty"`
}

func (x *GetBidInfoResponse_CommitmentWithStatus) Reset() {
	*x = GetBidInfoResponse_CommitmentWithStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBidInfoResponse_CommitmentWithStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBidInfoResponse_CommitmentWithStatus) ProtoMessage() {}

func (x *GetBidInfoResponse_CommitmentWithStatus) ProtoReflect() protoreflect.Message {
	mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBidInfoResponse_CommitmentWithStatus.ProtoReflect.Descriptor instead.
func (*GetBidInfoResponse_CommitmentWithStatus) Descriptor() ([]byte, []int) {
	return file_bidderapi_v1_bidderapi_proto_rawDescGZIP(), []int{30, 0}
}

func (x *GetBidInfoResponse_CommitmentWithStatus) GetProviderAddress() string {
	if x != nil {
		return x.ProviderAddress
	}
	return ""
}

func (x *GetBidInfoResponse_CommitmentWithStatus) GetDispatchTimestamp() int64 {
	if x != nil {
		return x.DispatchTimestamp
	}
	return 0
}

func (x *GetBidInfoResponse_CommitmentWithStatus) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *GetBidInfoResponse_CommitmentWithStatus) GetDetails() string {
	if x != nil {
		return x.Details
	}
	return ""
}

func (x *GetBidInfoResponse_CommitmentWithStatus) GetPayment() string {
	if x != nil {
		return x.Payment
	}
	return ""
}

func (x *GetBidInfoResponse_CommitmentWithStatus) GetRefund() string {
	if x != nil {
		return x.Refund
	}
	return ""
}

type GetBidInfoResponse_BidInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TxnHashes           []string                                   `protobuf:"bytes,1,rep,name=txn_hashes,json=txnHashes,proto3" json:"txn_hashes,omitempty"`
	RevertableTxnHashes []string                                   `protobuf:"bytes,2,rep,name=revertable_txn_hashes,json=revertableTxnHashes,proto3" json:"revertable_txn_hashes,omitempty"`
	BlockNumber         int64                                      `protobuf:"varint,3,opt,name=block_number,json=blockNumber,proto3" json:"block_number,omitempty"`
	BidAmount           string                                     `protobuf:"bytes,4,opt,name=bid_amount,json=bidAmount,proto3" json:"bid_amount,omitempty"`
	DecayStartTimestamp int64                                      `protobuf:"varint,5,opt,name=decay_start_timestamp,json=decayStartTimestamp,proto3" json:"decay_start_timestamp,omitempty"`
	DecayEndTimestamp   int64                                      `protobuf:"varint,6,opt,name=decay_end_timestamp,json=decayEndTimestamp,proto3" json:"decay_end_timestamp,omitempty"`
	BidDigest           string                                     `protobuf:"bytes,7,opt,name=bid_digest,json=bidDigest,proto3" json:"bid_digest,omitempty"`
	SlashAmount         string                                     `protobuf:"bytes,8,opt,name=slash_amount,json=slashAmount,proto3" json:"slash_amount,omitempty"`
	Commitments         []*GetBidInfoResponse_CommitmentWithStatus `protobuf:"bytes,9,rep,name=commitments,proto3" json:"commitments,omitempty"`
	BidOptions          *BidOptions                                `protobuf:"bytes,10,opt,name=bid_options,json=bidOptions,proto3" json:"bid_options,omitempty"`
}

func (x *GetBidInfoResponse_BidInfo) Reset() {
	*x = GetBidInfoResponse_BidInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBidInfoResponse_BidInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBidInfoResponse_BidInfo) ProtoMessage() {}

func (x *GetBidInfoResponse_BidInfo) ProtoReflect() protoreflect.Message {
	mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBidInfoResponse_BidInfo.ProtoReflect.Descriptor instead.
func (*GetBidInfoResponse_BidInfo) Descriptor() ([]byte, []int) {
	return file_bidderapi_v1_bidderapi_proto_rawDescGZIP(), []int{30, 1}
}

func (x *GetBidInfoResponse_BidInfo) GetTxnHashes() []string {
	if x != nil {
		return x.TxnHashes
	}
	return nil
}

func (x *GetBidInfoResponse_BidInfo) GetRevertableTxnHashes() []string {
	if x != nil {
		return x.RevertableTxnHashes
	}
	return nil
}

func (x *GetBidInfoResponse_BidInfo) GetBlockNumber() int64 {
	if x != nil {
		return x.BlockNumber
	}
	return 0
}

func (x *GetBidInfoResponse_BidInfo) GetBidAmount() string {
	if x != nil {
		return x.BidAmount
	}
	return ""
}

func (x *GetBidInfoResponse_BidInfo) GetDecayStartTimestamp() int64 {
	if x != nil {
		return x.DecayStartTimestamp
	}
	return 0
}

func (x *GetBidInfoResponse_BidInfo) GetDecayEndTimestamp() int64 {
	if x != nil {
		return x.DecayEndTimestamp
	}
	return 0
}

func (x *GetBidInfoResponse_BidInfo) GetBidDigest() string {
	if x != nil {
		return x.BidDigest
	}
	return ""
}

func (x *GetBidInfoResponse_BidInfo) GetSlashAmount() string {
	if x != nil {
		return x.SlashAmount
	}
	return ""
}

func (x *GetBidInfoResponse_BidInfo) GetCommitments() []*GetBidInfoResponse_CommitmentWithStatus {
	if x != nil {
		return x.Commitments
	}
	return nil
}

func (x *GetBidInfoResponse_BidInfo) GetBidOptions() *BidOptions {
	if x != nil {
		return x.BidOptions
	}
	return nil
}

type GetBidInfoResponse_BlockBidInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BlockNumber int64                         `protobuf:"varint,1,opt,name=block_number,json=blockNumber,proto3" json:"block_number,omitempty"`
	Bids        []*GetBidInfoResponse_BidInfo `protobuf:"bytes,2,rep,name=bids,proto3" json:"bids,omitempty"`
}

func (x *GetBidInfoResponse_BlockBidInfo) Reset() {
	*x = GetBidInfoResponse_BlockBidInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBidInfoResponse_BlockBidInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBidInfoResponse_BlockBidInfo) ProtoMessage() {}

func (x *GetBidInfoResponse_BlockBidInfo) ProtoReflect() protoreflect.Message {
	mi := &file_bidderapi_v1_bidderapi_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBidInfoResponse_BlockBidInfo.ProtoReflect.Descriptor instead.
func (*GetBidInfoResponse_BlockBidInfo) Descriptor() ([]byte, []int) {
	return file_bidderapi_v1_bidderapi_proto_rawDescGZIP(), []int{30, 2}
}

func (x *GetBidInfoResponse_BlockBidInfo) GetBlockNumber() int64 {
	if x != nil {
		return x.BlockNumber
	}
	return 0
}

func (x *GetBidInfoResponse_BlockBidInfo) GetBids() []*GetBidInfoResponse_BidInfo {
	if x != nil {
		return x.Bids
	}
	return nil
}

var File_bidderapi_v1_bidderapi_proto protoreflect.FileDescriptor

var file_bidderapi_v1_bidderapi_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x62,
	0x69, 0x64, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c,
	0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x1a, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x63, 0x2d, 0x67, 0x65, 0x6e, 0x2d, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69,
	0x76, 0x32, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x62, 0x75, 0x66, 0x2f,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x8c, 0x04, 0x0a, 0x0e, 0x44, 0x65, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0xaf, 0x01, 0x0a, 0x06, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x96, 0x01, 0x92, 0x41,
	0x45, 0x32, 0x25, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x20, 0x6f, 0x66, 0x20, 0x45, 0x54, 0x48,
	0x20, 0x74, 0x6f, 0x20, 0x62, 0x65, 0x20, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x65, 0x64,
	0x20, 0x69, 0x6e, 0x20, 0x77, 0x65, 0x69, 0x2e, 0x4a, 0x13, 0x31, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x8a, 0x01, 0x06,
	0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x2b, 0xba, 0x48, 0x4b, 0xba, 0x01, 0x48, 0x0a, 0x06, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x20, 0x6d, 0x75, 0x73,
	0x74, 0x20, 0x62, 0x65, 0x20, 0x61, 0x20, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x20, 0x69, 0x6e, 0x74,
	0x65, 0x67, 0x65, 0x72, 0x2e, 0x1a, 0x1d, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x6d, 0x61, 0x74, 0x63,
	0x68, 0x65, 0x73, 0x28, 0x27, 0x5e, 0x5b, 0x31, 0x2d, 0x39, 0x5d, 0x5b, 0x30, 0x2d, 0x39, 0x5d,
	0x2a, 0x24, 0x27, 0x29, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0xce, 0x01, 0x0a,
	0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0xb1, 0x01, 0x92, 0x41, 0x4a, 0x32, 0x1a, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x20,
	0x45, 0x74, 0x68, 0x65, 0x72, 0x65, 0x75, 0x6d, 0x20, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x2e, 0x4a, 0x2c, 0x22, 0x30, 0x78, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x22, 0xba,
	0x48, 0x61, 0xba, 0x01, 0x5e, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x12,
	0x2a, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x20, 0x6d, 0x75, 0x73, 0x74, 0x20, 0x62,
	0x65, 0x20, 0x61, 0x20, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x20, 0x45, 0x74, 0x68, 0x65, 0x72, 0x65,
	0x75, 0x6d, 0x20, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x2e, 0x1a, 0x26, 0x74, 0x68, 0x69,
	0x73, 0x2e, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x28, 0x27, 0x5e, 0x28, 0x30, 0x78, 0x29,
	0x3f, 0x5b, 0x61, 0x2d, 0x66, 0x41, 0x2d, 0x46, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x34, 0x30, 0x7d,
	0x24, 0x27, 0x29, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x3a, 0x77, 0x92,
	0x41, 0x74, 0x0a, 0x72, 0x2a, 0x0f, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x20, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x32, 0x4b, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x20, 0x66,
	0x6f, 0x72, 0x20, 0x62, 0x69, 0x64, 0x73, 0x20, 0x74, 0x6f, 0x20, 0x62, 0x65, 0x20, 0x69, 0x73,
	0x73, 0x75, 0x65, 0x64, 0x20, 0x62, 0x79, 0x20, 0x74, 0x68, 0x65, 0x20, 0x62, 0x69, 0x64, 0x64,
	0x65, 0x72, 0x20, 0x69, 0x6e, 0x20, 0x77, 0x65, 0x69, 0x2c, 0x20, 0x73, 0x70, 0x65, 0x63, 0x69,
	0x66, 0x69, 0x63, 0x20, 0x74, 0x6f, 0x20, 0x61, 0x20, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65,
	0x72, 0x2e, 0xd2, 0x01, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0xd2, 0x01, 0x08, 0x70, 0x72,
	0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x22, 0x83, 0x02, 0x0a, 0x0f, 0x44, 0x65, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x3a, 0xbb,
	0x01, 0x92, 0x41, 0xb7, 0x01, 0x0a, 0x58, 0x2a, 0x10, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x20, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x32, 0x44, 0x44, 0x65, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x20, 0x66, 0x6f, 0x72, 0x20, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x20, 0x69, 0x6e,
	0x20, 0x74, 0x68, 0x65, 0x20, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x20, 0x72, 0x65, 0x67, 0x69,
	0x73, 0x74, 0x72, 0x79, 0x20, 0x66, 0x6f, 0x72, 0x20, 0x61, 0x20, 0x70, 0x61, 0x72, 0x74, 0x69,
	0x63, 0x75, 0x6c, 0x61, 0x72, 0x20, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x2e, 0x32,
	0x5b, 0x7b, 0x22, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x3a, 0x20, 0x22, 0x31, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x22, 0x2c, 0x20, 0x22, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x22, 0x3a, 0x20, 0x22,
	0x30, 0x78, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x22, 0x7d, 0x22, 0x81, 0x04, 0x0a,
	0x14, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x6c, 0x79, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0xcc, 0x01, 0x0a, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0xa8, 0x01, 0x92,
	0x41, 0x4b, 0x32, 0x2b, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x20, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x20, 0x6f, 0x66, 0x20, 0x45, 0x54, 0x48, 0x20, 0x74, 0x6f, 0x20, 0x62, 0x65, 0x20, 0x64, 0x65,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x65, 0x64, 0x20, 0x69, 0x6e, 0x20, 0x77, 0x65, 0x69, 0x2e, 0x4a,
	0x13, 0x31, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x8a, 0x01, 0x06, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x2b, 0xba, 0x48, 0x57,
	0xba, 0x01, 0x54, 0x0a, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x25, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x20,
	0x6d, 0x75, 0x73, 0x74, 0x20, 0x62, 0x65, 0x20, 0x61, 0x20, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x20,
	0x69, 0x6e, 0x74, 0x65, 0x67, 0x65, 0x72, 0x2e, 0x1a, 0x1d, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x6d,
	0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x28, 0x27, 0x5e, 0x5b, 0x31, 0x2d, 0x39, 0x5d, 0x5b, 0x30,
	0x2d, 0x39, 0x5d, 0x2a, 0x24, 0x27, 0x29, 0x52, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0xbb, 0x01, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65,
	0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x42, 0x9c, 0x01, 0x92, 0x41, 0x1e, 0x32, 0x1c,
	0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x20, 0x45, 0x74, 0x68, 0x65, 0x72, 0x65, 0x75,
	0x6d, 0x20, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x2e, 0xba, 0x48, 0x78, 0xba,
	0x01, 0x75, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x73, 0x12, 0x36, 0x70,
	0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x73, 0x20, 0x6d, 0x75, 0x73, 0x74, 0x20, 0x62, 0x65,
	0x20, 0x61, 0x20, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x20, 0x61, 0x72, 0x72, 0x61, 0x79, 0x20, 0x6f,
	0x66, 0x20, 0x45, 0x74, 0x68, 0x65, 0x72, 0x65, 0x75, 0x6d, 0x20, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x65, 0x73, 0x2e, 0x1a, 0x30, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x61, 0x6c, 0x6c, 0x28,
	0x72, 0x2c, 0x20, 0x72, 0x2e, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x28, 0x27, 0x5e, 0x28,
	0x30, 0x78, 0x29, 0x3f, 0x5b, 0x61, 0x2d, 0x66, 0x41, 0x2d, 0x46, 0x30, 0x2d, 0x39, 0x5d, 0x7b,
	0x34, 0x30, 0x7d, 0x24, 0x27, 0x29, 0x29, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65,
	0x72, 0x73, 0x3a, 0x5c, 0x92, 0x41, 0x59, 0x0a, 0x57, 0x2a, 0x16, 0x44, 0x65, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x20, 0x65, 0x76, 0x65, 0x6e, 0x6c, 0x79, 0x20, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x32, 0x3d, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x73, 0x20, 0x73, 0x6f, 0x6d, 0x65,
	0x20, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x20, 0x6f, 0x66, 0x20, 0x45, 0x54, 0x48, 0x20, 0x65,
	0x76, 0x65, 0x6e, 0x6c, 0x79, 0x20, 0x61, 0x63, 0x72, 0x6f, 0x73, 0x73, 0x20, 0x6d, 0x75, 0x6c,
	0x74, 0x69, 0x70, 0x6c, 0x65, 0x20, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x73, 0x2e,
	0x22, 0xae, 0x01, 0x0a, 0x15, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x45, 0x76, 0x65, 0x6e,
	0x6c, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x72,
	0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x70,
	0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x73, 0x3a, 0x5d, 0x92, 0x41, 0x5a, 0x0a, 0x58, 0x2a, 0x17, 0x44, 0x65, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x20, 0x65, 0x76, 0x65, 0x6e, 0x6c, 0x79, 0x20, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x32, 0x3d, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x73, 0x20, 0x73, 0x6f, 0x6d,
	0x65, 0x20, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x20, 0x6f, 0x66, 0x20, 0x45, 0x54, 0x48, 0x20,
	0x65, 0x76, 0x65, 0x6e, 0x6c, 0x79, 0x20, 0x61, 0x63, 0x72, 0x6f, 0x73, 0x73, 0x20, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x20, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x73,
	0x2e, 0x22, 0x61, 0x0a, 0x1b, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x65, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x3a, 0x42, 0x92, 0x41, 0x3f, 0x0a, 0x3d, 0x2a, 0x1c, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x44,
	0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x20, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x32, 0x1d, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x65, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x20, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x22, 0x7e, 0x0a, 0x1c, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x65,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x3a, 0x44,
	0x92, 0x41, 0x41, 0x0a, 0x3f, 0x2a, 0x1d, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x65, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x20, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x32, 0x1e, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x65, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x20, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x22, 0x64, 0x0a, 0x1c, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x44,
	0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x3a, 0x44, 0x92, 0x41, 0x41, 0x0a, 0x3f, 0x2a, 0x1d, 0x44, 0x69, 0x73,
	0x61, 0x62, 0x6c, 0x65, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x20, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x32, 0x1e, 0x44, 0x69, 0x73, 0x61,
	0x62, 0x6c, 0x65, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x20, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x22, 0x81, 0x01, 0x0a, 0x1d, 0x44,
	0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x4d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x3a, 0x46, 0x92, 0x41, 0x43, 0x0a, 0x41, 0x2a, 0x1e, 0x44,
	0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x4d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x20, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x32, 0x1f, 0x44,
	0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x4d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x20, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x22, 0x52,
	0x0a, 0x0d, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x12,
	0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x0e, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x44, 0x65, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x22, 0xb6, 0x01, 0x0a, 0x18, 0x53, 0x65, 0x74, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x44, 0x0a, 0x0f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x62, 0x69, 0x64, 0x64, 0x65,
	0x72, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x44, 0x65,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x52, 0x0e, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x44, 0x65, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x73, 0x3a, 0x54, 0x92, 0x41, 0x51, 0x0a, 0x4f, 0x2a, 0x25, 0x4f, 0x76,
	0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x44, 0x65, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x20, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x32, 0x26, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x54, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x20, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x22, 0x97, 0x02, 0x0a, 0x19,
	0x53, 0x65, 0x74, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x57, 0x0a, 0x19, 0x73, 0x75, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x66, 0x75, 0x6c, 0x6c, 0x79, 0x5f, 0x73, 0x65, 0x74, 0x5f, 0x64, 0x65,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x62,
	0x69, 0x64, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x52, 0x17, 0x73, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x66, 0x75, 0x6c, 0x6c, 0x79, 0x53, 0x65, 0x74, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x73, 0x12, 0x47, 0x0a, 0x20, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x66, 0x75, 0x6c,
	0x6c, 0x79, 0x5f, 0x74, 0x6f, 0x70, 0x70, 0x65, 0x64, 0x5f, 0x75, 0x70, 0x5f, 0x70, 0x72, 0x6f,
	0x76, 0x69, 0x64, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x1d, 0x73, 0x75,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x66, 0x75, 0x6c, 0x6c, 0x79, 0x54, 0x6f, 0x70, 0x70, 0x65, 0x64,
	0x55, 0x70, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x73, 0x3a, 0x58, 0x92, 0x41, 0x55,
	0x0a, 0x53, 0x2a, 0x27, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x54, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x20, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x32, 0x28, 0x4f, 0x76, 0x65,
	0x72, 0x72, 0x69, 0x64, 0x65, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x44, 0x65, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x20, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x22, 0x61, 0x0a, 0x1b, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x3a, 0x42, 0x92, 0x41, 0x3f, 0x0a, 0x3d, 0x2a, 0x1c, 0x44, 0x65, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x20, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x32, 0x1d, 0x44, 0x65, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x20,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x22, 0xc4, 0x01, 0x0a, 0x1c, 0x44, 0x65, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x12, 0x44, 0x0a, 0x0f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x64, 0x65,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x62,
	0x69, 0x64, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x52, 0x0e, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x73, 0x3a, 0x44, 0x92, 0x41, 0x41, 0x0a, 0x3f,
	0x2a, 0x1d, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x20, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x32,
	0x1e, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x20, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x22,
	0x0e, 0x0a, 0x0c, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22,
	0xb6, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0xa0, 0x01, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x83, 0x01, 0x92, 0x41, 0x1c, 0x32, 0x1a,
	0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x20, 0x45, 0x74, 0x68, 0x65, 0x72, 0x65, 0x75,
	0x6d, 0x20, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x2e, 0xba, 0x48, 0x61, 0xba, 0x01, 0x5e,
	0x0a, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x12, 0x2a, 0x70, 0x72, 0x6f, 0x76,
	0x69, 0x64, 0x65, 0x72, 0x20, 0x6d, 0x75, 0x73, 0x74, 0x20, 0x62, 0x65, 0x20, 0x61, 0x20, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x20, 0x45, 0x74, 0x68, 0x65, 0x72, 0x65, 0x75, 0x6d, 0x20, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x2e, 0x1a, 0x26, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x6d, 0x61, 0x74,
	0x63, 0x68, 0x65, 0x73, 0x28, 0x27, 0x5e, 0x28, 0x30, 0x78, 0x29, 0x3f, 0x5b, 0x61, 0x2d, 0x66,
	0x41, 0x2d, 0x46, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x34, 0x30, 0x7d, 0x24, 0x27, 0x29, 0x52, 0x08,
	0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x22, 0x4f, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x41,
	0x6c, 0x6c, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x3a, 0x36, 0x92, 0x41, 0x33, 0x0a, 0x31, 0x2a, 0x16, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c,
	0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x73, 0x20, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x32, 0x17, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x73,
	0x20, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x22, 0x41, 0x0a, 0x0b, 0x44, 0x65, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x76,
	0x69, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x76,
	0x69, 0x64, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xb0, 0x01, 0x0a,
	0x16, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x35, 0x0a, 0x08, 0x64, 0x65, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x62, 0x69, 0x64, 0x64,
	0x65, 0x72, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x73, 0x12, 0x25,
	0x0a, 0x0e, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x42, 0x61,
	0x6c, 0x61, 0x6e, 0x63, 0x65, 0x3a, 0x38, 0x92, 0x41, 0x35, 0x0a, 0x33, 0x2a, 0x17, 0x47, 0x65,
	0x74, 0x41, 0x6c, 0x6c, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x73, 0x20, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x32, 0x18, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x44, 0x65, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x73, 0x20, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x22,
	0xa3, 0x02, 0x0a, 0x19, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x57, 0x69, 0x74, 0x68, 0x64,
	0x72, 0x61, 0x77, 0x61, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0xbb, 0x01,
	0x0a, 0x09, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x09, 0x42, 0x9c, 0x01, 0x92, 0x41, 0x1e, 0x32, 0x1c, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65,
	0x72, 0x20, 0x45, 0x74, 0x68, 0x65, 0x72, 0x65, 0x75, 0x6d, 0x20, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x65, 0x73, 0x2e, 0xba, 0x48, 0x78, 0xba, 0x01, 0x75, 0x0a, 0x09, 0x70, 0x72, 0x6f,
	0x76, 0x69, 0x64, 0x65, 0x72, 0x73, 0x12, 0x36, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72,
	0x73, 0x20, 0x6d, 0x75, 0x73, 0x74, 0x20, 0x62, 0x65, 0x20, 0x61, 0x20, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x20, 0x61, 0x72, 0x72, 0x61, 0x79, 0x20, 0x6f, 0x66, 0x20, 0x45, 0x74, 0x68, 0x65, 0x72,
	0x65, 0x75, 0x6d, 0x20, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x2e, 0x1a, 0x30,
	0x74, 0x68, 0x69, 0x73, 0x2e, 0x61, 0x6c, 0x6c, 0x28, 0x72, 0x2c, 0x20, 0x72, 0x2e, 0x6d, 0x61,
	0x74, 0x63, 0x68, 0x65, 0x73, 0x28, 0x27, 0x5e, 0x28, 0x30, 0x78, 0x29, 0x3f, 0x5b, 0x61, 0x2d,
	0x66, 0x41, 0x2d, 0x46, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x34, 0x30, 0x7d, 0x24, 0x27, 0x29, 0x29,
	0x52, 0x09, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x73, 0x3a, 0x48, 0x92, 0x41, 0x45,
	0x0a, 0x43, 0x2a, 0x1a, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x57, 0x69, 0x74, 0x68, 0x64,
	0x72, 0x61, 0x77, 0x61, 0x6c, 0x73, 0x20, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x32, 0x25,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x20, 0x77, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77,
	0x61, 0x6c, 0x73, 0x20, 0x66, 0x72, 0x6f, 0x6d, 0x20, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65,
	0x72, 0x28, 0x73, 0x29, 0x2e, 0x22, 0x85, 0x02, 0x0a, 0x1a, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x57, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x61, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65,
	0x72, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x07, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x3a, 0xae, 0x01, 0x92,
	0x41, 0xaa, 0x01, 0x0a, 0xa7, 0x01, 0x2a, 0x1b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x57,
	0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x61, 0x6c, 0x73, 0x20, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x32, 0x25, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x20, 0x77, 0x69, 0x74,
	0x68, 0x64, 0x72, 0x61, 0x77, 0x61, 0x6c, 0x73, 0x20, 0x66, 0x72, 0x6f, 0x6d, 0x20, 0x70, 0x72,
	0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x28, 0x73, 0x29, 0x2e, 0x4a, 0x61, 0x7b, 0x22, 0x70, 0x72,
	0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x73, 0x22, 0x3a, 0x20, 0x5b, 0x22, 0x30, 0x78, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x22, 0x5d, 0x2c, 0x20, 0x22, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x73, 0x22, 0x3a, 0x20, 0x5b, 0x22, 0x31, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x22, 0x5d, 0x7d, 0x22, 0x8d, 0x02,
	0x0a, 0x0f, 0x57, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0xbb, 0x01, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0x9c, 0x01, 0x92, 0x41, 0x1e, 0x32, 0x1c, 0x50, 0x72, 0x6f,
	0x76, 0x69, 0x64, 0x65, 0x72, 0x20, 0x45, 0x74, 0x68, 0x65, 0x72, 0x65, 0x75, 0x6d, 0x20, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x2e, 0xba, 0x48, 0x78, 0xba, 0x01, 0x75, 0x0a,
	0x09, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x73, 0x12, 0x36, 0x70, 0x72, 0x6f, 0x76,
	0x69, 0x64, 0x65, 0x72, 0x73, 0x20, 0x6d, 0x75, 0x73, 0x74, 0x20, 0x62, 0x65, 0x20, 0x61, 0x20,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x20, 0x61, 0x72, 0x72, 0x61, 0x79, 0x20, 0x6f, 0x66, 0x20, 0x45,
	0x74, 0x68, 0x65, 0x72, 0x65, 0x75, 0x6d, 0x20, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65,
	0x73, 0x2e, 0x1a, 0x30, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x61, 0x6c, 0x6c, 0x28, 0x72, 0x2c, 0x20,
	0x72, 0x2e, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x28, 0x27, 0x5e, 0x28, 0x30, 0x78, 0x29,
	0x3f, 0x5b, 0x61, 0x2d, 0x66, 0x41, 0x2d, 0x46, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x34, 0x30, 0x7d,
	0x24, 0x27, 0x29, 0x29, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x73, 0x3a,
	0x3c, 0x92, 0x41, 0x39, 0x0a, 0x37, 0x2a, 0x10, 0x57, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77,
	0x20, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x32, 0x23, 0x57, 0x69, 0x74, 0x68, 0x64, 0x72,
	0x61, 0x77, 0x20, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x73, 0x20, 0x66, 0x72, 0x6f, 0x6d,
	0x20, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x28, 0x73, 0x29, 0x2e, 0x22, 0xf6, 0x01,
	0x0a, 0x10, 0x57, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x07, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x12, 0x1c, 0x0a, 0x09,
	0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x09, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x73, 0x3a, 0xa9, 0x01, 0x92, 0x41, 0xa5,
	0x01, 0x0a, 0x40, 0x2a, 0x11, 0x57, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x20, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x32, 0x2b, 0x57, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77,
	0x6e, 0x20, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x20, 0x66, 0x72, 0x6f, 0x6d, 0x20, 0x74,
	0x68, 0x65, 0x20, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x20, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74,
	0x72, 0x79, 0x2e, 0x32, 0x61, 0x7b, 0x22, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x22, 0x3a,
	0x20, 0x5b, 0x22, 0x31, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x22, 0x5d, 0x2c, 0x20, 0x22, 0x70, 0x72, 0x6f, 0x76, 0x69,
	0x64, 0x65, 0x72, 0x73, 0x22, 0x3a, 0x20, 0x5b, 0x22, 0x30, 0x78, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x22, 0x5d, 0x7d, 0x22, 0x58, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x56, 0x61, 0x6c,
	0x69, 0x64, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x3a, 0x3c, 0x92, 0x41, 0x39, 0x0a, 0x37, 0x2a, 0x19, 0x47, 0x65, 0x74, 0x56, 0x61,
	0x6c, 0x69, 0x64, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x73, 0x20, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x32, 0x1a, 0x47, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x50, 0x72,
	0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x73, 0x20, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x22, 0x84, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x50, 0x72, 0x6f,
	0x76, 0x69, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x27,
	0x0a, 0x0f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x50, 0x72,
	0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x73, 0x3a, 0x3e, 0x92, 0x41, 0x3b, 0x0a, 0x39, 0x2a, 0x1a,
	0x47, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72,
	0x73, 0x20, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x32, 0x1b, 0x47, 0x65, 0x74, 0x56,
	0x61, 0x6c, 0x69, 0x64, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x73, 0x20, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x22, 0xc1, 0x05, 0x0a, 0x12, 0x50, 0x6f, 0x73, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x12, 0x76,
	0x0a, 0x06, 0x61, 0x6e, 0x63, 0x68, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27,
	0x2e, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6f,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74,
	0x2e, 0x41, 0x6e, 0x63, 0x68, 0x6f, 0x72, 0x42, 0x35, 0x92, 0x41, 0x32, 0x32, 0x30, 0x41, 0x6e,
	0x63, 0x68, 0x6f, 0x72, 0x20, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x6f, 0x66,
	0x20, 0x74, 0x68, 0x65, 0x20, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x20, 0x69, 0x6e, 0x20, 0x74, 0x68, 0x65, 0x20, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x2e, 0x52, 0x06,
	0x61, 0x6e, 0x63, 0x68, 0x6f, 0x72, 0x12, 0x65, 0x0a, 0x05, 0x62, 0x61, 0x73, 0x69, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e,
	0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x2e, 0x42, 0x61, 0x73, 0x69, 0x73, 0x42, 0x27, 0x92,
	0x41, 0x24, 0x32, 0x22, 0x42, 0x61, 0x73, 0x69, 0x73, 0x20, 0x66, 0x6f, 0x72, 0x20, 0x74, 0x68,
	0x65, 0x20, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x63, 0x6f, 0x6e, 0x73, 0x74,
	0x72, 0x61, 0x69, 0x6e, 0x74, 0x2e, 0x52, 0x05, 0x62, 0x61, 0x73, 0x69, 0x73, 0x12, 0xc9, 0x01,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0xb2, 0x01,
	0x92, 0x41, 0xae, 0x01, 0x32, 0xab, 0x01, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x20, 0x6f, 0x66, 0x20,
	0x74, 0x68, 0x65, 0x20, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x63, 0x6f, 0x6e,
	0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x2e, 0x20, 0x49, 0x66, 0x20, 0x61, 0x6e, 0x63, 0x68,
	0x6f, 0x72, 0x20, 0x69, 0x73, 0x20, 0x54, 0x4f, 0x50, 0x2c, 0x20, 0x74, 0x68, 0x69, 0x73, 0x20,
	0x69, 0x73, 0x20, 0x74, 0x68, 0x65, 0x20, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x20,
	0x66, 0x72, 0x6f, 0x6d, 0x20, 0x74, 0x68, 0x65, 0x20, 0x74, 0x6f, 0x70, 0x20, 0x6f, 0x66, 0x20,
	0x74, 0x68, 0x65, 0x20, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x2e, 0x20, 0x49, 0x66, 0x20, 0x61, 0x6e,
	0x63, 0x68, 0x6f, 0x72, 0x20, 0x69, 0x73, 0x20, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x2c, 0x20,
	0x74, 0x68, 0x69, 0x73, 0x20, 0x69, 0x73, 0x20, 0x74, 0x68, 0x65, 0x20, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x20, 0x66, 0x72, 0x6f, 0x6d, 0x20, 0x74, 0x68, 0x65, 0x20, 0x62, 0x6f,
	0x74, 0x74, 0x6f, 0x6d, 0x20, 0x6f, 0x66, 0x20, 0x74, 0x68, 0x65, 0x20, 0x62, 0x6c, 0x6f, 0x63,
	0x6b, 0x2e, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x43, 0x0a, 0x06, 0x41, 0x6e, 0x63,
	0x68, 0x6f, 0x72, 0x12, 0x16, 0x0a, 0x12, 0x41, 0x4e, 0x43, 0x48, 0x4f, 0x52, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x41,
	0x4e, 0x43, 0x48, 0x4f, 0x52, 0x5f, 0x54, 0x4f, 0x50, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x41,
	0x4e, 0x43, 0x48, 0x4f, 0x52, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x10, 0x02, 0x22, 0x62,
	0x0a, 0x05, 0x42, 0x61, 0x73, 0x69, 0x73, 0x12, 0x15, 0x0a, 0x11, 0x42, 0x41, 0x53, 0x49, 0x53,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x14,
	0x0a, 0x10, 0x42, 0x41, 0x53, 0x49, 0x53, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x49,
	0x4c, 0x45, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x42, 0x41, 0x53, 0x49, 0x53, 0x5f, 0x41, 0x42,
	0x53, 0x4f, 0x4c, 0x55, 0x54, 0x45, 0x10, 0x02, 0x12, 0x18, 0x0a, 0x14, 0x42, 0x41, 0x53, 0x49,
	0x53, 0x5f, 0x47, 0x41, 0x53, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x49, 0x4c, 0x45,
	0x10, 0x03, 0x3a, 0x57, 0x92, 0x41, 0x54, 0x0a, 0x52, 0x2a, 0x13, 0x50, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x20, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x32, 0x3b,
	0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x20, 0x6f, 0x6e, 0x20, 0x74, 0x68,
	0x65, 0x20, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x6f, 0x66, 0x20, 0x74, 0x68,
	0x65, 0x20, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x69, 0x6e,
	0x20, 0x74, 0x68, 0x65, 0x20, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x2e, 0x22, 0xa4, 0x01, 0x0a, 0x09,
	0x42, 0x69, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x8f, 0x01, 0x0a, 0x13, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x43,
	0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x42, 0x3a, 0x92, 0x41, 0x37, 0x32, 0x35,
	0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61,
	0x69, 0x6e, 0x74, 0x20, 0x66, 0x6f, 0x72, 0x20, 0x74, 0x68, 0x65, 0x20, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x69, 0x6e, 0x20, 0x74, 0x68, 0x65, 0x20, 0x62,
	0x6c, 0x6f, 0x63, 0x6b, 0x2e, 0x48, 0x00, 0x52, 0x12, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x42, 0x05, 0x0a, 0x03, 0x6f,
	0x70, 0x74, 0x22, 0xc6, 0x01, 0x0a, 0x0a, 0x42, 0x69, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0xb7, 0x01, 0x0a, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x2e,
	0x76, 0x31, 0x2e, 0x42, 0x69, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x83, 0x01, 0x92,
	0x41, 0x2a, 0x32, 0x28, 0x4c, 0x69, 0x73, 0x74, 0x20, 0x6f, 0x66, 0x20, 0x62, 0x69, 0x64, 0x20,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x20, 0x66, 0x6f, 0x72, 0x20, 0x74, 0x68, 0x65, 0x20,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0xba, 0x48, 0x53, 0xba,
	0x01, 0x50, 0x0a, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x2d, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x20, 0x6d, 0x75, 0x73, 0x74, 0x20, 0x62, 0x65, 0x20, 0x61, 0x20, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x20, 0x61, 0x72, 0x72, 0x61, 0x79, 0x20, 0x6f, 0x66, 0x20, 0x62, 0x69,
	0x64, 0x20, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x1a, 0x16, 0x74, 0x68, 0x69, 0x73,
	0x2e, 0x61, 0x6c, 0x6c, 0x28, 0x72, 0x2c, 0x20, 0x72, 0x20, 0x21, 0x3d, 0x20, 0x6e, 0x75, 0x6c,
	0x6c, 0x29, 0x52, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xdd, 0x14, 0x0a, 0x03,
	0x42, 0x69, 0x64, 0x12, 0x94, 0x02, 0x0a, 0x09, 0x74, 0x78, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x65,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0xf6, 0x01, 0x92, 0x41, 0x78, 0x32, 0x64, 0x48,
	0x65, 0x78, 0x20, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x20, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x69,
	0x6e, 0x67, 0x20, 0x6f, 0x66, 0x20, 0x74, 0x68, 0x65, 0x20, 0x68, 0x61, 0x73, 0x68, 0x65, 0x73,
	0x20, 0x6f, 0x66, 0x20, 0x74, 0x68, 0x65, 0x20, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x20, 0x74, 0x68, 0x61, 0x74, 0x20, 0x74, 0x68, 0x65, 0x20, 0x62, 0x69,
	0x64, 0x64, 0x65, 0x72, 0x20, 0x77, 0x61, 0x6e, 0x74, 0x73, 0x20, 0x74, 0x6f, 0x20, 0x69, 0x6e,
	0x63, 0x6c, 0x75, 0x64, 0x65, 0x20, 0x69, 0x6e, 0x20, 0x74, 0x68, 0x65, 0x20, 0x62, 0x6c, 0x6f,
	0x63, 0x6b, 0x2e, 0x8a, 0x01, 0x0f, 0x5b, 0x61, 0x2d, 0x66, 0x41, 0x2d, 0x46, 0x30, 0x2d, 0x39,
	0x5d, 0x7b, 0x36, 0x34, 0x7d, 0xba, 0x48, 0x78, 0xba, 0x01, 0x75, 0x0a, 0x09, 0x74, 0x78, 0x5f,
	0x68, 0x61, 0x73, 0x68, 0x65, 0x73, 0x12, 0x36, 0x74, 0x78, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x65,
	0x73, 0x20, 0x6d, 0x75, 0x73, 0x74, 0x20, 0x62, 0x65, 0x20, 0x61, 0x20, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x20, 0x61, 0x72, 0x72, 0x61, 0x79, 0x20, 0x6f, 0x66, 0x20, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x68, 0x61, 0x73, 0x68, 0x65, 0x73, 0x2e, 0x1a, 0x30,
	0x74, 0x68, 0x69, 0x73, 0x2e, 0x61, 0x6c, 0x6c, 0x28, 0x72, 0x2c, 0x20, 0x72, 0x2e, 0x6d, 0x61,
	0x74, 0x63, 0x68, 0x65, 0x73, 0x28, 0x27, 0x5e, 0x28, 0x30, 0x78, 0x29, 0x3f, 0x5b, 0x61, 0x2d,
	0x66, 0x41, 0x2d, 0x46, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x36, 0x34, 0x7d, 0x24, 0x27, 0x29, 0x29,
	0x52, 0x08, 0x74, 0x78, 0x48, 0x61, 0x73, 0x68, 0x65, 0x73, 0x12, 0xe0, 0x01, 0x0a, 0x06, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0xc7, 0x01, 0x92, 0x41,
	0x76, 0x32, 0x6b, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x20, 0x6f, 0x66, 0x20, 0x45, 0x54, 0x48,
	0x20, 0x74, 0x68, 0x61, 0x74, 0x20, 0x74, 0x68, 0x65, 0x20, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72,
	0x20, 0x69, 0x73, 0x20, 0x77, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x20, 0x74, 0x6f, 0x20, 0x70,
	0x61, 0x79, 0x20, 0x74, 0x6f, 0x20, 0x74, 0x68, 0x65, 0x20, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64,
	0x65, 0x72, 0x20, 0x66, 0x6f, 0x72, 0x20, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x69, 0x6e, 0x67,
	0x20, 0x74, 0x68, 0x65, 0x20, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x20, 0x69, 0x6e, 0x20, 0x74, 0x68, 0x65, 0x20, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x2e, 0x8a, 0x01,
	0x06, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x2b, 0xba, 0x48, 0x4b, 0xba, 0x01, 0x48, 0x0a, 0x06, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x20, 0x6d, 0x75,
	0x73, 0x74, 0x20, 0x62, 0x65, 0x20, 0x61, 0x20, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x20, 0x69, 0x6e,
	0x74, 0x65, 0x67, 0x65, 0x72, 0x2e, 0x1a, 0x1d, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x6d, 0x61, 0x74,
	0x63, 0x68, 0x65, 0x73, 0x28, 0x27, 0x5e, 0x5b, 0x31, 0x2d, 0x39, 0x5d, 0x5b, 0x30, 0x2d, 0x39,
	0x5d, 0x2a, 0x24, 0x27, 0x29, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0xb9, 0x01,
	0x0a, 0x0c, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x95, 0x01, 0x92, 0x41, 0x47, 0x32, 0x45, 0x4d, 0x61, 0x78, 0x20,
	0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x20, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x20, 0x74, 0x68, 0x61,
	0x74, 0x20, 0x74, 0x68, 0x65, 0x20, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x20, 0x77, 0x61, 0x6e,
	0x74, 0x73, 0x20, 0x74, 0x6f, 0x20, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x20, 0x74, 0x68,
	0x65, 0x20, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x69, 0x6e,
	0x2e, 0xba, 0x48, 0x48, 0xba, 0x01, 0x45, 0x0a, 0x0c, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x25, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x20, 0x6d, 0x75, 0x73, 0x74, 0x20, 0x62, 0x65, 0x20, 0x61, 0x20, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x20, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x65, 0x72, 0x2e, 0x1a, 0x0e, 0x75, 0x69,
	0x6e, 0x74, 0x28, 0x74, 0x68, 0x69, 0x73, 0x29, 0x20, 0x3e, 0x20, 0x30, 0x52, 0x0b, 0x62, 0x6c,
	0x6f, 0x63, 0x6b, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0xc2, 0x01, 0x0a, 0x15, 0x64, 0x65,
	0x63, 0x61, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x8d, 0x01, 0x92, 0x41, 0x2d, 0x32,
	0x2b, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x20, 0x61, 0x74, 0x20, 0x77, 0x68,
	0x69, 0x63, 0x68, 0x20, 0x74, 0x68, 0x65, 0x20, 0x62, 0x69, 0x64, 0x20, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x73, 0x20, 0x64, 0x65, 0x63, 0x61, 0x79, 0x69, 0x6e, 0x67, 0x2e, 0xba, 0x48, 0x5a, 0xba,
	0x01, 0x57, 0x0a, 0x15, 0x64, 0x65, 0x63, 0x61, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x2e, 0x64, 0x65, 0x63, 0x61, 0x79,
	0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x20, 0x6d, 0x75, 0x73, 0x74, 0x20, 0x62, 0x65, 0x20, 0x61, 0x20, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x20, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x65, 0x72, 0x2e, 0x1a, 0x0e, 0x75, 0x69, 0x6e, 0x74, 0x28,
	0x74, 0x68, 0x69, 0x73, 0x29, 0x20, 0x3e, 0x20, 0x30, 0x52, 0x13, 0x64, 0x65, 0x63, 0x61, 0x79,
	0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0xb8,
	0x01, 0x0a, 0x13, 0x64, 0x65, 0x63, 0x61, 0x79, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x87, 0x01, 0x92,
	0x41, 0x2b, 0x32, 0x29, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x20, 0x61, 0x74,
	0x20, 0x77, 0x68, 0x69, 0x63, 0x68, 0x20, 0x74, 0x68, 0x65, 0x20, 0x62, 0x69, 0x64, 0x20, 0x65,
	0x6e, 0x64, 0x73, 0x20, 0x64, 0x65, 0x63, 0x61, 0x79, 0x69, 0x6e, 0x67, 0x2e, 0xba, 0x48, 0x56,
	0xba, 0x01, 0x53, 0x0a, 0x13, 0x64, 0x65, 0x63, 0x61, 0x79, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x2c, 0x64, 0x65, 0x63, 0x61, 0x79, 0x5f,
	0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x20, 0x6d, 0x75,
	0x73, 0x74, 0x20, 0x62, 0x65, 0x20, 0x61, 0x20, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x20, 0x69, 0x6e,
	0x74, 0x65, 0x67, 0x65, 0x72, 0x2e, 0x1a, 0x0e, 0x75, 0x69, 0x6e, 0x74, 0x28, 0x74, 0x68, 0x69,
	0x73, 0x29, 0x20, 0x3e, 0x20, 0x30, 0x52, 0x11, 0x64, 0x65, 0x63, 0x61, 0x79, 0x45, 0x6e, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x8f, 0x02, 0x0a, 0x13, 0x72, 0x65,
	0x76, 0x65, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x78, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x65,
	0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x42, 0xde, 0x01, 0x92, 0x41, 0x49, 0x32, 0x47, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x20, 0x61, 0x72, 0x72, 0x61, 0x79, 0x20, 0x6f, 0x66,
	0x20, 0x74, 0x78, 0x20, 0x68, 0x61, 0x73, 0x68, 0x65, 0x73, 0x20, 0x74, 0x68, 0x61, 0x74, 0x20,
	0x61, 0x72, 0x65, 0x20, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x20, 0x74, 0x6f, 0x20, 0x72,
	0x65, 0x76, 0x65, 0x72, 0x74, 0x20, 0x6f, 0x72, 0x20, 0x62, 0x65, 0x20, 0x64, 0x69, 0x73, 0x63,
	0x61, 0x72, 0x64, 0x65, 0x64, 0x2e, 0xba, 0x48, 0x8e, 0x01, 0xba, 0x01, 0x8a, 0x01, 0x0a, 0x13,
	0x72, 0x65, 0x76, 0x65, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x78, 0x5f, 0x68, 0x61, 0x73,
	0x68, 0x65, 0x73, 0x12, 0x41, 0x72, 0x65, 0x76, 0x65, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x74,
	0x78, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x65, 0x73, 0x20, 0x6d, 0x75, 0x73, 0x74, 0x20, 0x62, 0x65,
	0x20, 0x61, 0x6e, 0x20, 0x61, 0x72, 0x72, 0x61, 0x79, 0x20, 0x6f, 0x66, 0x20, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x20, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x68,
	0x61, 0x73, 0x68, 0x65, 0x73, 0x2e, 0x1a, 0x30, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x61, 0x6c, 0x6c,
	0x28, 0x72, 0x2c, 0x20, 0x72, 0x2e, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x28, 0x27, 0x5e,
	0x28, 0x30, 0x78, 0x29, 0x3f, 0x5b, 0x61, 0x2d, 0x66, 0x41, 0x2d, 0x46, 0x30, 0x2d, 0x39, 0x5d,
	0x7b, 0x36, 0x34, 0x7d, 0x24, 0x27, 0x29, 0x29, 0x52, 0x11, 0x72, 0x65, 0x76, 0x65, 0x72, 0x74,
	0x69, 0x6e, 0x67, 0x54, 0x78, 0x48, 0x61, 0x73, 0x68, 0x65, 0x73, 0x12, 0xa3, 0x02, 0x0a, 0x10,
	0x72, 0x61, 0x77, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x42, 0xf7, 0x01, 0x92, 0x41, 0x6e, 0x32, 0x6c, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x20, 0x61, 0x72, 0x72, 0x61, 0x79, 0x20, 0x6f, 0x66, 0x20,
	0x52, 0x4c, 0x50, 0x20, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x64, 0x20, 0x72, 0x61, 0x77, 0x20,
	0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x20, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x20, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x73, 0x20, 0x74, 0x68, 0x61, 0x74,
	0x20, 0x74, 0x68, 0x65, 0x20, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x20, 0x77, 0x61, 0x6e, 0x74,
	0x73, 0x20, 0x74, 0x6f, 0x20, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x20, 0x69, 0x6e, 0x20,
	0x74, 0x68, 0x65, 0x20, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x2e, 0xba, 0x48, 0x82, 0x01, 0xba, 0x01,
	0x7f, 0x0a, 0x10, 0x72, 0x61, 0x77, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x3c, 0x72, 0x61, 0x77, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x20, 0x6d, 0x75, 0x73, 0x74, 0x20, 0x62, 0x65, 0x20, 0x61, 0x6e,
	0x20, 0x61, 0x72, 0x72, 0x61, 0x79, 0x20, 0x6f, 0x66, 0x20, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x20,
	0x72, 0x61, 0x77, 0x20, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x1a, 0x2d, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x61, 0x6c, 0x6c, 0x28, 0x72, 0x2c, 0x20, 0x72,
	0x2e, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x28, 0x27, 0x5e, 0x28, 0x30, 0x78, 0x29, 0x3f,
	0x5b, 0x61, 0x2d, 0x66, 0x41, 0x2d, 0x46, 0x30, 0x2d, 0x39, 0x5d, 0x2a, 0x24, 0x27, 0x29, 0x29,
	0x52, 0x0f, 0x72, 0x61, 0x77, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0xbf, 0x02, 0x0a, 0x0c, 0x73, 0x6c, 0x61, 0x73, 0x68, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x9b, 0x02, 0x92, 0x41, 0x9f, 0x01, 0x32,
	0x93, 0x01, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x20, 0x6f, 0x66, 0x20, 0x45, 0x54, 0x48, 0x20,
	0x74, 0x68, 0x61, 0x74, 0x20, 0x77, 0x69, 0x6c, 0x6c, 0x20, 0x62, 0x65, 0x20, 0x73, 0x6c, 0x61,
	0x73, 0x68, 0x65, 0x64, 0x20, 0x66, 0x72, 0x6f, 0x6d, 0x20, 0x74, 0x68, 0x65, 0x20, 0x70, 0x72,
	0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x20, 0x69, 0x66, 0x20, 0x74, 0x68, 0x65, 0x79, 0x20, 0x66,
	0x61, 0x69, 0x6c, 0x20, 0x74, 0x6f, 0x20, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x20, 0x74,
	0x68, 0x65, 0x20, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x20,
	0x49, 0x66, 0x20, 0x7a, 0x65, 0x72, 0x6f, 0x2c, 0x20, 0x74, 0x68, 0x65, 0x20, 0x64, 0x65, 0x63,
	0x61, 0x79, 0x65, 0x64, 0x20, 0x62, 0x69, 0x64, 0x20, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x20,
	0x69, 0x73, 0x20, 0x75, 0x73, 0x65, 0x64, 0x20, 0x66, 0x6f, 0x72, 0x20, 0x73, 0x6c, 0x61, 0x73,
	0x68, 0x69, 0x6e, 0x67, 0x2e, 0x8a, 0x01, 0x06, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x2b, 0xba, 0x48,
	0x75, 0xba, 0x01, 0x72, 0x0a, 0x0c, 0x73, 0x6c, 0x61, 0x73, 0x68, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x25, 0x73, 0x6c, 0x61, 0x73, 0x68, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x20, 0x6d, 0x75, 0x73, 0x74, 0x20, 0x62, 0x65, 0x20, 0x61, 0x20, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x20, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x65, 0x72, 0x2e, 0x1a, 0x3b, 0x74, 0x68, 0x69, 0x73, 0x20,
	0x3d, 0x3d, 0x20, 0x27, 0x27, 0x20, 0x7c, 0x7c, 0x20, 0x28, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x6d,
	0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x28, 0x27, 0x5e, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x2b, 0x24,
	0x27, 0x29, 0x20, 0x26, 0x26, 0x20, 0x75, 0x69, 0x6e, 0x74, 0x28, 0x74, 0x68, 0x69, 0x73, 0x29,
	0x20, 0x3e, 0x3d, 0x20, 0x30, 0x29, 0x52, 0x0b, 0x73, 0x6c, 0x61, 0x73, 0x68, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x69, 0x0a, 0x0b, 0x62, 0x69, 0x64, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x62, 0x69, 0x64, 0x64, 0x65,
	0x72, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x69, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x42, 0x2e, 0x92, 0x41, 0x2b, 0x32, 0x29, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x20, 0x62, 0x69, 0x64, 0x20, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x20, 0x66, 0x6f,
	0x72, 0x20, 0x74, 0x68, 0x65, 0x20, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x52, 0x0a, 0x62, 0x69, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x3a, 0xba,
	0x04, 0x92, 0x41, 0xb6, 0x04, 0x0a, 0x95, 0x01, 0x2a, 0x0b, 0x42, 0x69, 0x64, 0x20, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x32, 0x40, 0x55, 0x6e, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x20,
	0x62, 0x69, 0x64, 0x20, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x20, 0x66, 0x72, 0x6f, 0x6d,
	0x20, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x73, 0x20, 0x74, 0x6f, 0x20, 0x74, 0x68, 0x65, 0x20,
	0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x20, 0x6d, 0x65, 0x76, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x69,
	0x74, 0x20, 0x6e, 0x6f, 0x64, 0x65, 0x2e, 0xd2, 0x01, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0xd2, 0x01, 0x0c, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0xd2,
	0x01, 0x15, 0x64, 0x65, 0x63, 0x61, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0xd2, 0x01, 0x13, 0x64, 0x65, 0x63, 0x61, 0x79, 0x5f,
	0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x32, 0x9b, 0x03,
	0x7b, 0x22, 0x74, 0x78, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x65, 0x73, 0x22, 0x3a, 0x20, 0x5b, 0x22,
	0x66, 0x65, 0x34, 0x63, 0x62, 0x34, 0x37, 0x64, 0x62, 0x33, 0x36, 0x33, 0x30, 0x35, 0x35, 0x31,
	0x62, 0x65, 0x65, 0x64, 0x66, 0x62, 0x64, 0x30, 0x32, 0x61, 0x37, 0x31, 0x65, 0x63, 0x63, 0x36,
	0x39, 0x66, 0x64, 0x35, 0x39, 0x37, 0x35, 0x38, 0x65, 0x32, 0x62, 0x61, 0x36, 0x39, 0x39, 0x36,
	0x30, 0x36, 0x65, 0x32, 0x64, 0x35, 0x63, 0x37, 0x34, 0x32, 0x38, 0x34, 0x66, 0x66, 0x61, 0x37,
	0x22, 0x2c, 0x20, 0x22, 0x37, 0x31, 0x63, 0x31, 0x33, 0x34, 0x38, 0x66, 0x32, 0x64, 0x37, 0x66,
	0x66, 0x37, 0x65, 0x38, 0x31, 0x34, 0x66, 0x39, 0x63, 0x33, 0x36, 0x31, 0x37, 0x39, 0x38, 0x33,
	0x37, 0x30, 0x33, 0x34, 0x33, 0x35, 0x65, 0x61, 0x37, 0x34, 0x34, 0x36, 0x64, 0x65, 0x34, 0x32,
	0x30, 0x61, 0x65, 0x61, 0x63, 0x34, 0x38, 0x38, 0x62, 0x66, 0x31, 0x64, 0x65, 0x33, 0x35, 0x37,
	0x33, 0x37, 0x65, 0x38, 0x22, 0x5d, 0x2c, 0x20, 0x22, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22,
	0x3a, 0x20, 0x22, 0x31, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x22, 0x2c, 0x20, 0x22, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x3a, 0x20, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x2c,
	0x20, 0x22, 0x64, 0x65, 0x63, 0x61, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x3a, 0x20, 0x31, 0x36, 0x33, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x2c, 0x20, 0x22, 0x64, 0x65, 0x63, 0x61, 0x79, 0x5f, 0x65, 0x6e, 0x64,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x3a, 0x20, 0x31, 0x36, 0x33,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x2c, 0x20, 0x22, 0x72, 0x65, 0x76, 0x65, 0x72, 0x74,
	0x69, 0x6e, 0x67, 0x5f, 0x74, 0x78, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x65, 0x73, 0x22, 0x3a, 0x20,
	0x5b, 0x22, 0x66, 0x65, 0x34, 0x63, 0x62, 0x34, 0x37, 0x64, 0x62, 0x33, 0x36, 0x33, 0x30, 0x35,
	0x35, 0x31, 0x62, 0x65, 0x65, 0x64, 0x66, 0x62, 0x64, 0x30, 0x32, 0x61, 0x37, 0x31, 0x65, 0x63,
	0x63, 0x36, 0x39, 0x66, 0x64, 0x35, 0x39, 0x37, 0x35, 0x38, 0x65, 0x32, 0x62, 0x61, 0x36, 0x39,
	0x39, 0x36, 0x30, 0x36, 0x65, 0x32, 0x64, 0x35, 0x63, 0x37, 0x34, 0x32, 0x38, 0x34, 0x66, 0x66,
	0x61, 0x37, 0x22, 0x5d, 0x2c, 0x20, 0x22, 0x73, 0x6c, 0x61, 0x73, 0x68, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x22, 0x3a, 0x20, 0x22, 0x35, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x22, 0x7d, 0x22, 0xad, 0x0e, 0x0a, 0x0a,
	0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x95, 0x01, 0x0a, 0x09, 0x74,
	0x78, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0x78,
	0x92, 0x41, 0x75, 0x32, 0x61, 0x48, 0x65, 0x78, 0x20, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x20,
	0x65, 0x6e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x20, 0x6f, 0x66, 0x20, 0x74, 0x68, 0x65, 0x20,
	0x68, 0x61, 0x73, 0x68, 0x20, 0x6f, 0x66, 0x20, 0x74, 0x68, 0x65, 0x20, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x74, 0x68, 0x61, 0x74, 0x20, 0x74, 0x68, 0x65,
	0x20, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x20, 0x77, 0x61, 0x6e, 0x74, 0x73, 0x20, 0x74, 0x6f,
	0x20, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x20, 0x69, 0x6e, 0x20, 0x74, 0x68, 0x65, 0x20,
	0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x2e, 0x8a, 0x01, 0x0f, 0x5b, 0x61, 0x2d, 0x66, 0x41, 0x2d, 0x46,
	0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x36, 0x34, 0x7d, 0x52, 0x08, 0x74, 0x78, 0x48, 0x61, 0x73, 0x68,
	0x65, 0x73, 0x12, 0x8f, 0x01, 0x0a, 0x0a, 0x62, 0x69, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x70, 0x92, 0x41, 0x6d, 0x32, 0x6b, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x20, 0x6f, 0x66, 0x20, 0x45, 0x54, 0x48, 0x20, 0x74, 0x68, 0x61, 0x74,
	0x20, 0x74, 0x68, 0x65, 0x20, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x20, 0x68, 0x61, 0x73, 0x20,
	0x61, 0x67, 0x72, 0x65, 0x65, 0x64, 0x20, 0x74, 0x6f, 0x20, 0x70, 0x61, 0x79, 0x20, 0x74, 0x6f,
	0x20, 0x74, 0x68, 0x65, 0x20, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x20, 0x66, 0x6f,
	0x72, 0x20, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x69, 0x6e, 0x67, 0x20, 0x74, 0x68, 0x65, 0x20,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x69, 0x6e, 0x20, 0x74,
	0x68, 0x65, 0x20, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x2e, 0x52, 0x09, 0x62, 0x69, 0x64, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x6d, 0x0a, 0x0c, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x4a, 0x92, 0x41, 0x47, 0x32,
	0x45, 0x4d, 0x61, 0x78, 0x20, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x20, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x20, 0x74, 0x68, 0x61, 0x74, 0x20, 0x74, 0x68, 0x65, 0x20, 0x62, 0x69, 0x64, 0x64, 0x65,
	0x72, 0x20, 0x77, 0x61, 0x6e, 0x74, 0x73, 0x20, 0x74, 0x6f, 0x20, 0x69, 0x6e, 0x63, 0x6c, 0x75,
	0x64, 0x65, 0x20, 0x74, 0x68, 0x65, 0x20, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x20, 0x69, 0x6e, 0x2e, 0x52, 0x0b, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x7b, 0x0a, 0x13, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x5f,
	0x62, 0x69, 0x64, 0x5f, 0x64, 0x69, 0x67, 0x65, 0x73, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x4b, 0x92, 0x41, 0x48, 0x32, 0x46, 0x48, 0x65, 0x78, 0x20, 0x73, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x20, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x20, 0x6f, 0x66, 0x20, 0x64, 0x69,
	0x67, 0x65, 0x73, 0x74, 0x20, 0x6f, 0x66, 0x20, 0x74, 0x68, 0x65, 0x20, 0x62, 0x69, 0x64, 0x20,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x20, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x20, 0x62,
	0x79, 0x20, 0x74, 0x68, 0x65, 0x20, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x11, 0x72,
	0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x42, 0x69, 0x64, 0x44, 0x69, 0x67, 0x65, 0x73, 0x74,
	0x12, 0x7d, 0x0a, 0x16, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x5f, 0x62, 0x69, 0x64,
	0x5f, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x47, 0x92, 0x41, 0x44, 0x32, 0x42, 0x48, 0x65, 0x78, 0x20, 0x73, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x20, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x20, 0x6f, 0x66, 0x20, 0x73, 0x69,
	0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x20, 0x6f, 0x66, 0x20, 0x74, 0x68, 0x65, 0x20, 0x62,
	0x69, 0x64, 0x64, 0x65, 0x72, 0x20, 0x74, 0x68, 0x61, 0x74, 0x20, 0x73, 0x65, 0x6e, 0x74, 0x20,
	0x74, 0x68, 0x69, 0x73, 0x20, 0x62, 0x69, 0x64, 0x2e, 0x52, 0x14, 0x72, 0x65, 0x63, 0x65, 0x69,
	0x76, 0x65, 0x64, 0x42, 0x69, 0x64, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12,
	0x62, 0x0a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x69,
	0x67, 0x65, 0x73, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x35, 0x92, 0x41, 0x32, 0x32,
	0x30, 0x48, 0x65, 0x78, 0x20, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x20, 0x65, 0x6e, 0x63, 0x6f,
	0x64, 0x69, 0x6e, 0x67, 0x20, 0x6f, 0x66, 0x20, 0x64, 0x69, 0x67, 0x65, 0x73, 0x74, 0x20, 0x6f,
	0x66, 0x20, 0x74, 0x68, 0x65, 0x20, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x52, 0x10, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x69, 0x67,
	0x65, 0x73, 0x74, 0x12, 0x9e, 0x01, 0x0a, 0x14, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x6b, 0x92, 0x41, 0x68, 0x32, 0x66, 0x48, 0x65, 0x78, 0x20, 0x73, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x20, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x20, 0x6f, 0x66, 0x20,
	0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x20, 0x6f, 0x66, 0x20, 0x74, 0x68, 0x65,
	0x20, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x20, 0x73, 0x69, 0x67, 0x6e,
	0x65, 0x64, 0x20, 0x62, 0x79, 0x20, 0x74, 0x68, 0x65, 0x20, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64,
	0x65, 0x72, 0x20, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x69, 0x6e, 0x67, 0x20, 0x74, 0x68,
	0x69, 0x73, 0x20, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x52,
	0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x12, 0x88, 0x01, 0x0a, 0x10, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65,
	0x72, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x5d, 0x92, 0x41, 0x5a, 0x32, 0x58, 0x48, 0x65, 0x78, 0x20, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x20, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x20, 0x6f, 0x66, 0x20, 0x74, 0x68, 0x65,
	0x20, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x20, 0x6f, 0x66, 0x20, 0x74, 0x68, 0x65, 0x20,
	0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x20, 0x74, 0x68, 0x61, 0x74, 0x20, 0x73, 0x69,
	0x67, 0x6e, 0x65, 0x64, 0x20, 0x74, 0x68, 0x65, 0x20, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x20, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x2e, 0x52, 0x0f,
	0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12,
	0x64, 0x0a, 0x15, 0x64, 0x65, 0x63, 0x61, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x42, 0x30,
	0x92, 0x41, 0x2d, 0x32, 0x2b, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x20, 0x61,
	0x74, 0x20, 0x77, 0x68, 0x69, 0x63, 0x68, 0x20, 0x74, 0x68, 0x65, 0x20, 0x62, 0x69, 0x64, 0x20,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x73, 0x20, 0x64, 0x65, 0x63, 0x61, 0x79, 0x69, 0x6e, 0x67, 0x2e,
	0x52, 0x13, 0x64, 0x65, 0x63, 0x61, 0x79, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x5e, 0x0a, 0x13, 0x64, 0x65, 0x63, 0x61, 0x79, 0x5f, 0x65,
	0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x2e, 0x92, 0x41, 0x2b, 0x32, 0x29, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x20, 0x61, 0x74, 0x20, 0x77, 0x68, 0x69, 0x63, 0x68, 0x20, 0x74, 0x68, 0x65, 0x20,
	0x62, 0x69, 0x64, 0x20, 0x65, 0x6e, 0x64, 0x73, 0x20, 0x64, 0x65, 0x63, 0x61, 0x79, 0x69, 0x6e,
	0x67, 0x2e, 0x52, 0x11, 0x64, 0x65, 0x63, 0x61, 0x79, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x63, 0x0a, 0x12, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63,
	0x68, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x34, 0x92, 0x41, 0x31, 0x32, 0x2f, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x20, 0x61, 0x74, 0x20, 0x77, 0x68, 0x69, 0x63, 0x68, 0x20, 0x74, 0x68, 0x65, 0x20, 0x63,
	0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x20, 0x69, 0x73, 0x20, 0x70, 0x75, 0x62,
	0x6c, 0x69, 0x73, 0x68, 0x65, 0x64, 0x2e, 0x52, 0x11, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63,
	0x68, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x7c, 0x0a, 0x13, 0x72, 0x65,
	0x76, 0x65, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x78, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x65,
	0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x09, 0x42, 0x4c, 0x92, 0x41, 0x49, 0x32, 0x47, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x20, 0x61, 0x72, 0x72, 0x61, 0x79, 0x20, 0x6f, 0x66, 0x20,
	0x74, 0x78, 0x20, 0x68, 0x61, 0x73, 0x68, 0x65, 0x73, 0x20, 0x74, 0x68, 0x61, 0x74, 0x20, 0x61,
	0x72, 0x65, 0x20, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x20, 0x74, 0x6f, 0x20, 0x72, 0x65,
	0x76, 0x65, 0x72, 0x74, 0x20, 0x6f, 0x72, 0x20, 0x62, 0x65, 0x20, 0x64, 0x69, 0x73, 0x63, 0x61,
	0x72, 0x64, 0x65, 0x64, 0x2e, 0x52, 0x11, 0x72, 0x65, 0x76, 0x65, 0x72, 0x74, 0x69, 0x6e, 0x67,
	0x54, 0x78, 0x48, 0x61, 0x73, 0x68, 0x65, 0x73, 0x12, 0x85, 0x01, 0x0a, 0x0c, 0x73, 0x6c, 0x61,
	0x73, 0x68, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x62, 0x92, 0x41, 0x5f, 0x32, 0x5d, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x20, 0x6f, 0x66, 0x20,
	0x45, 0x54, 0x48, 0x20, 0x74, 0x68, 0x61, 0x74, 0x20, 0x77, 0x69, 0x6c, 0x6c, 0x20, 0x62, 0x65,
	0x20, 0x73, 0x6c, 0x61, 0x73, 0x68, 0x65, 0x64, 0x20, 0x66, 0x72, 0x6f, 0x6d, 0x20, 0x74, 0x68,
	0x65, 0x20, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x20, 0x69, 0x66, 0x20, 0x74, 0x68,
	0x65, 0x79, 0x20, 0x66, 0x61, 0x69, 0x6c, 0x20, 0x74, 0x6f, 0x20, 0x69, 0x6e, 0x63, 0x6c, 0x75,
	0x64, 0x65, 0x20, 0x74, 0x68, 0x65, 0x20, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x52, 0x0b, 0x73, 0x6c, 0x61, 0x73, 0x68, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x69, 0x0a, 0x0b, 0x62, 0x69, 0x64, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x69, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x42,
	0x2e, 0x92, 0x41, 0x2b, 0x32, 0x29, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x20, 0x62,
	0x69, 0x64, 0x20, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x20, 0x66, 0x6f, 0x72, 0x20, 0x74,
	0x68, 0x65, 0x20, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x52,
	0x0a, 0x62, 0x69, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x3a, 0x5e, 0x92, 0x41, 0x5b,
	0x0a, 0x59, 0x2a, 0x12, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x20, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x32, 0x43, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x20, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x20, 0x66, 0x72, 0x6f, 0x6d, 0x20,
	0x74, 0x68, 0x65, 0x20, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x20, 0x74, 0x6f, 0x20,
	0x74, 0x68, 0x65, 0x20, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x20, 0x6d, 0x65, 0x76, 0x2d, 0x63,
	0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x20, 0x6e, 0x6f, 0x64, 0x65, 0x2e, 0x22, 0xbe, 0x02, 0x0a, 0x11,
	0x47, 0x65, 0x74, 0x42, 0x69, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x9f, 0x01, 0x0a, 0x0c, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x7c, 0x92, 0x41, 0x79, 0x32, 0x77, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x20, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x20, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x20, 0x66, 0x6f, 0x72, 0x20, 0x71, 0x75, 0x65, 0x72, 0x79, 0x69, 0x6e,
	0x67, 0x20, 0x62, 0x69, 0x64, 0x20, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x20, 0x49, 0x66, 0x20, 0x6e,
	0x6f, 0x74, 0x20, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x2c, 0x20, 0x61, 0x6c,
	0x6c, 0x20, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x20, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x20, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x20, 0x61, 0x72, 0x65, 0x20, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e,
	0x65, 0x64, 0x20, 0x69, 0x6e, 0x20, 0x61, 0x73, 0x63, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x20,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x0b, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x34, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x20, 0x92, 0x41, 0x1d, 0x32, 0x1b, 0x50, 0x61, 0x67, 0x65, 0x20, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x20, 0x66, 0x6f, 0x72, 0x20, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x51, 0x0a, 0x05, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x3b, 0x92, 0x41, 0x38, 0x32, 0x36, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x20, 0x6f, 0x66, 0x20, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x20, 0x70,
	0x65, 0x72, 0x20, 0x70, 0x61, 0x67, 0x65, 0x20, 0x66, 0x6f, 0x72, 0x20, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x20, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x20,
	0x69, 0x73, 0x20, 0x35, 0x30, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x22, 0xcb, 0x12, 0x0a,
	0x12, 0x47, 0x65, 0x74, 0x42, 0x69, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x97, 0x01, 0x0a, 0x0e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x62, 0x69,
	0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x62,
	0x69, 0x64, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x42,
	0x69, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42,
	0x6c, 0x6f, 0x63, 0x6b, 0x42, 0x69, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x42, 0x92, 0x41, 0x3f,
	0x32, 0x3d, 0x4c, 0x69, 0x73, 0x74, 0x20, 0x6f, 0x66, 0x20, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x20,
	0x62, 0x69, 0x64, 0x20, 0x69, 0x6e, 0x66, 0x6f, 0x20, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e,
	0x69, 0x6e, 0x67, 0x20, 0x62, 0x69, 0x64, 0x73, 0x20, 0x61, 0x6e, 0x64, 0x20, 0x74, 0x68, 0x65,
	0x69, 0x72, 0x20, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x52,
	0x0c, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x42, 0x69, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0xf4, 0x04,
	0x0a, 0x14, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x57, 0x69, 0x74, 0x68,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x7e, 0x0a, 0x10, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64,
	0x65, 0x72, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x53, 0x92, 0x41, 0x50, 0x32, 0x4e, 0x48, 0x65, 0x78, 0x20, 0x73, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x20, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x20, 0x6f, 0x66, 0x20, 0x74, 0x68,
	0x65, 0x20, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x20, 0x6f, 0x66, 0x20, 0x74, 0x68, 0x65,
	0x20, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x20, 0x74, 0x68, 0x61, 0x74, 0x20, 0x73,
	0x69, 0x67, 0x6e, 0x65, 0x64, 0x20, 0x74, 0x68, 0x65, 0x20, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x0f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x63, 0x0a, 0x12, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74,
	0x63, 0x68, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x34, 0x92, 0x41, 0x31, 0x32, 0x2f, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x20, 0x61, 0x74, 0x20, 0x77, 0x68, 0x69, 0x63, 0x68, 0x20, 0x74, 0x68, 0x65, 0x20,
	0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x20, 0x69, 0x73, 0x20, 0x70, 0x75,
	0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x64, 0x2e, 0x52, 0x11, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74,
	0x63, 0x68, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x86, 0x01, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x6e, 0x92, 0x41,
	0x6b, 0x32, 0x69, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x20, 0x6f, 0x66, 0x20, 0x74, 0x68, 0x65,
	0x20, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x20, 0x50, 0x6f, 0x73,
	0x73, 0x69, 0x62, 0x6c, 0x65, 0x20, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x3a, 0x20, 0x27, 0x70,
	0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x27, 0x2c, 0x20, 0x27, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x64,
	0x27, 0x2c, 0x20, 0x27, 0x6f, 0x70, 0x65, 0x6e, 0x65, 0x64, 0x27, 0x2c, 0x20, 0x27, 0x73, 0x65,
	0x74, 0x74, 0x6c, 0x65, 0x64, 0x27, 0x2c, 0x20, 0x27, 0x73, 0x6c, 0x61, 0x73, 0x68, 0x65, 0x64,
	0x27, 0x2c, 0x20, 0x27, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x27, 0x2e, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x4e, 0x0a, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x34, 0x92, 0x41, 0x31, 0x32, 0x2f, 0x41, 0x64, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x20, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x20, 0x61,
	0x62, 0x6f, 0x75, 0x74, 0x20, 0x74, 0x68, 0x65, 0x20, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x20, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x52, 0x07, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x48, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2e, 0x92, 0x41, 0x2b, 0x32, 0x29, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x20, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x20, 0x69, 0x6e, 0x20, 0x77, 0x65,
	0x69, 0x20, 0x66, 0x6f, 0x72, 0x20, 0x74, 0x68, 0x65, 0x20, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x07, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x54,
	0x0a, 0x06, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x3c,
	0x92, 0x41, 0x39, 0x32, 0x37, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x20, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x20, 0x69, 0x6e, 0x20, 0x77, 0x65, 0x69, 0x20, 0x66, 0x6f, 0x72, 0x20, 0x74, 0x68,
	0x65, 0x20, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2c, 0x20, 0x69, 0x66,
	0x20, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x52, 0x06, 0x72, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x1a, 0xc6, 0x0a, 0x0a, 0x07, 0x42, 0x69, 0x64, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x9a, 0x01, 0x0a, 0x0a, 0x74, 0x78, 0x6e, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x65, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0x7b, 0x92, 0x41, 0x78, 0x32, 0x64, 0x48, 0x65, 0x78, 0x20,
	0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x20, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x20,
	0x6f, 0x66, 0x20, 0x74, 0x68, 0x65, 0x20, 0x68, 0x61, 0x73, 0x68, 0x65, 0x73, 0x20, 0x6f, 0x66,
	0x20, 0x74, 0x68, 0x65, 0x20, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x20, 0x74, 0x68, 0x61, 0x74, 0x20, 0x74, 0x68, 0x65, 0x20, 0x62, 0x69, 0x64, 0x64, 0x65,
	0x72, 0x20, 0x77, 0x61, 0x6e, 0x74, 0x73, 0x20, 0x74, 0x6f, 0x20, 0x69, 0x6e, 0x63, 0x6c, 0x75,
	0x64, 0x65, 0x20, 0x69, 0x6e, 0x20, 0x74, 0x68, 0x65, 0x20, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x2e,
	0x8a, 0x01, 0x0f, 0x5b, 0x61, 0x2d, 0x66, 0x41, 0x2d, 0x46, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x36,
	0x34, 0x7d, 0x52, 0x09, 0x74, 0x78, 0x6e, 0x48, 0x61, 0x73, 0x68, 0x65, 0x73, 0x12, 0x92, 0x01,
	0x0a, 0x15, 0x72, 0x65, 0x76, 0x65, 0x72, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x74, 0x78, 0x6e,
	0x5f, 0x68, 0x61, 0x73, 0x68, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x42, 0x5e, 0x92,
	0x41, 0x5b, 0x32, 0x47, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x20, 0x61, 0x72, 0x72,
	0x61, 0x79, 0x20, 0x6f, 0x66, 0x20, 0x74, 0x78, 0x20, 0x68, 0x61, 0x73, 0x68, 0x65, 0x73, 0x20,
	0x74, 0x68, 0x61, 0x74, 0x20, 0x61, 0x72, 0x65, 0x20, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64,
	0x20, 0x74, 0x6f, 0x20, 0x72, 0x65, 0x76, 0x65, 0x72, 0x74, 0x20, 0x6f, 0x72, 0x20, 0x62, 0x65,
	0x20, 0x64, 0x69, 0x73, 0x63, 0x61, 0x72, 0x64, 0x65, 0x64, 0x2e, 0x8a, 0x01, 0x0f, 0x5b, 0x61,
	0x2d, 0x66, 0x41, 0x2d, 0x46, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x36, 0x34, 0x7d, 0x52, 0x13, 0x72,
	0x65, 0x76, 0x65, 0x72, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x78, 0x6e, 0x48, 0x61, 0x73, 0x68,
	0x65, 0x73, 0x12, 0x69, 0x0a, 0x0c, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x46, 0x92, 0x41, 0x43, 0x32, 0x41, 0x42,
	0x6c, 0x6f, 0x63, 0x6b, 0x20, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x20, 0x74, 0x68, 0x61, 0x74,
	0x20, 0x74, 0x68, 0x65, 0x20, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x20, 0x77, 0x61, 0x6e, 0x74,
	0x73, 0x20, 0x74, 0x6f, 0x20, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x20, 0x74, 0x68, 0x65,
	0x20, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x69, 0x6e, 0x2e,
	0x52, 0x0b, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x98, 0x01,
	0x0a, 0x0a, 0x62, 0x69, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x79, 0x92, 0x41, 0x76, 0x32, 0x6b, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x20,
	0x6f, 0x66, 0x20, 0x45, 0x54, 0x48, 0x20, 0x74, 0x68, 0x61, 0x74, 0x20, 0x74, 0x68, 0x65, 0x20,
	0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x20, 0x69, 0x73, 0x20, 0x77, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x20, 0x74, 0x6f, 0x20, 0x70, 0x61, 0x79, 0x20, 0x74, 0x6f, 0x20, 0x74, 0x68, 0x65, 0x20,
	0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x20, 0x66, 0x6f, 0x72, 0x20, 0x69, 0x6e, 0x63,
	0x6c, 0x75, 0x64, 0x69, 0x6e, 0x67, 0x20, 0x74, 0x68, 0x65, 0x20, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x69, 0x6e, 0x20, 0x74, 0x68, 0x65, 0x20, 0x62, 0x6c,
	0x6f, 0x63, 0x6b, 0x2e, 0x8a, 0x01, 0x06, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x2b, 0x52, 0x09, 0x62,
	0x69, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x64, 0x0a, 0x15, 0x64, 0x65, 0x63, 0x61,
	0x79, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x30, 0x92, 0x41, 0x2d, 0x32, 0x2b, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x20, 0x61, 0x74, 0x20, 0x77, 0x68, 0x69, 0x63, 0x68,
	0x20, 0x74, 0x68, 0x65, 0x20, 0x62, 0x69, 0x64, 0x20, 0x73, 0x74, 0x61, 0x72, 0x74, 0x73, 0x20,
	0x64, 0x65, 0x63, 0x61, 0x79, 0x69, 0x6e, 0x67, 0x2e, 0x52, 0x13, 0x64, 0x65, 0x63, 0x61, 0x79,
	0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x5e,
	0x0a, 0x13, 0x64, 0x65, 0x63, 0x61, 0x79, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x42, 0x2e, 0x92, 0x41, 0x2b,
	0x32, 0x29, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x20, 0x61, 0x74, 0x20, 0x77,
	0x68, 0x69, 0x63, 0x68, 0x20, 0x74, 0x68, 0x65, 0x20, 0x62, 0x69, 0x64, 0x20, 0x65, 0x6e, 0x64,
	0x73, 0x20, 0x64, 0x65, 0x63, 0x61, 0x79, 0x69, 0x6e, 0x67, 0x2e, 0x52, 0x11, 0x64, 0x65, 0x63,
	0x61, 0x79, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x6a,
	0x0a, 0x0a, 0x62, 0x69, 0x64, 0x5f, 0x64, 0x69, 0x67, 0x65, 0x73, 0x74, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x4b, 0x92, 0x41, 0x48, 0x32, 0x46, 0x48, 0x65, 0x78, 0x20, 0x73, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x20, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x20, 0x6f, 0x66, 0x20,
	0x64, 0x69, 0x67, 0x65, 0x73, 0x74, 0x20, 0x6f, 0x66, 0x20, 0x74, 0x68, 0x65, 0x20, 0x62, 0x69,
	0x64, 0x20, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x20, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64,
	0x20, 0x62, 0x79, 0x20, 0x74, 0x68, 0x65, 0x20, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x2e, 0x52,
	0x09, 0x62, 0x69, 0x64, 0x44, 0x69, 0x67, 0x65, 0x73, 0x74, 0x12, 0xc7, 0x01, 0x0a, 0x0c, 0x73,
	0x6c, 0x61, 0x73, 0x68, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x42, 0xa3, 0x01, 0x92, 0x41, 0x9f, 0x01, 0x32, 0x93, 0x01, 0x41, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x20, 0x6f, 0x66, 0x20, 0x45, 0x54, 0x48, 0x20, 0x74, 0x68, 0x61, 0x74, 0x20, 0x77, 0x69,
	0x6c, 0x6c, 0x20, 0x62, 0x65, 0x20, 0x73, 0x6c, 0x61, 0x73, 0x68, 0x65, 0x64, 0x20, 0x66, 0x72,
	0x6f, 0x6d, 0x20, 0x74, 0x68, 0x65, 0x20, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x20,
	0x69, 0x66, 0x20, 0x74, 0x68, 0x65, 0x79, 0x20, 0x66, 0x61, 0x69, 0x6c, 0x20, 0x74, 0x6f, 0x20,
	0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x20, 0x74, 0x68, 0x65, 0x20, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x20, 0x49, 0x66, 0x20, 0x7a, 0x65, 0x72, 0x6f,
	0x2c, 0x20, 0x74, 0x68, 0x65, 0x20, 0x64, 0x65, 0x63, 0x61, 0x79, 0x65, 0x64, 0x20, 0x62, 0x69,
	0x64, 0x20, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x20, 0x69, 0x73, 0x20, 0x75, 0x73, 0x65, 0x64,
	0x20, 0x66, 0x6f, 0x72, 0x20, 0x73, 0x6c, 0x61, 0x73, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x8a, 0x01,
	0x06, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x2b, 0x52, 0x0b, 0x73, 0x6c, 0x61, 0x73, 0x68, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x57, 0x0a, 0x0b, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x62, 0x69, 0x64, 0x64,
	0x65, 0x72, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x69, 0x64, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x6d, 0x6d,
	0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x57, 0x69, 0x74, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x0b, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x69, 0x0a,
	0x0b, 0x62, 0x69, 0x64, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x2e, 0x76,
	0x31, 0x2e, 0x42, 0x69, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x42, 0x2e, 0x92, 0x41,
	0x2b, 0x32, 0x29, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x20, 0x62, 0x69, 0x64, 0x20,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x20, 0x66, 0x6f, 0x72, 0x20, 0x74, 0x68, 0x65, 0x20,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x52, 0x0a, 0x62, 0x69,
	0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x3a, 0x43, 0x92, 0x41, 0x40, 0x0a, 0x3e, 0x2a,
	0x08, 0x42, 0x69, 0x64, 0x20, 0x49, 0x6e, 0x66, 0x6f, 0x32, 0x32, 0x49, 0x6e, 0x66, 0x6f, 0x72,
	0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x61, 0x62, 0x6f, 0x75, 0x74, 0x20, 0x61, 0x20, 0x62,
	0x69, 0x64, 0x20, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x69, 0x6e, 0x67, 0x20, 0x69, 0x74, 0x73,
	0x20, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x1a, 0xda, 0x01,
	0x0a, 0x0c, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x42, 0x69, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x59,
	0x0a, 0x0c, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x36, 0x92, 0x41, 0x33, 0x32, 0x31, 0x42, 0x6c, 0x6f, 0x63, 0x6b,
	0x20, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x20, 0x66, 0x6f, 0x72, 0x20, 0x77, 0x68, 0x69, 0x63,
	0x68, 0x20, 0x74, 0x68, 0x65, 0x20, 0x62, 0x69, 0x64, 0x20, 0x69, 0x6e, 0x66, 0x6f, 0x20, 0x69,
	0x73, 0x20, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x65, 0x64, 0x2e, 0x52, 0x0b, 0x62, 0x6c,
	0x6f, 0x63, 0x6b, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x6f, 0x0a, 0x04, 0x62, 0x69, 0x64,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x69, 0x64, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x69, 0x64, 0x49, 0x6e, 0x66,
	0x6f, 0x42, 0x31, 0x92, 0x41, 0x2e, 0x32, 0x2c, 0x4c, 0x69, 0x73, 0x74, 0x20, 0x6f, 0x66, 0x20,
	0x62, 0x69, 0x64, 0x73, 0x20, 0x66, 0x6f, 0x72, 0x20, 0x74, 0x68, 0x65, 0x20, 0x73, 0x70, 0x65,
	0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x20, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x20, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x2e, 0x52, 0x04, 0x62, 0x69, 0x64, 0x73, 0x32, 0x97, 0x0e, 0x0a, 0x06, 0x42,
	0x69, 0x64, 0x64, 0x65, 0x72, 0x12, 0x53, 0x0a, 0x07, 0x53, 0x65, 0x6e, 0x64, 0x42, 0x69, 0x64,
	0x12, 0x11, 0x2e, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e,
	0x42, 0x69, 0x64, 0x1a, 0x18, 0x2e, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x19, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x13, 0x3a, 0x01, 0x2a, 0x22, 0x0e, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x69,
	0x64, 0x64, 0x65, 0x72, 0x2f, 0x62, 0x69, 0x64, 0x30, 0x01, 0x12, 0x6b, 0x0a, 0x07, 0x44, 0x65,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x12, 0x1c, 0x2e, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x23, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x22, 0x1b, 0x2f, 0x76, 0x31, 0x2f,
	0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x2f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x2f, 0x7b,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x7d, 0x12, 0x7b, 0x0a, 0x0d, 0x44, 0x65, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x6c, 0x79, 0x12, 0x22, 0x2e, 0x62, 0x69, 0x64, 0x64, 0x65,
	0x72, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x45,
	0x76, 0x65, 0x6e, 0x6c, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x62,
	0x69, 0x64, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x6c, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x21, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1b, 0x22, 0x19, 0x2f, 0x76, 0x31, 0x2f, 0x62,
	0x69, 0x64, 0x64, 0x65, 0x72, 0x2f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x65, 0x76,
	0x65, 0x6e, 0x6c, 0x79, 0x12, 0x98, 0x01, 0x0a, 0x14, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x44,
	0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x12, 0x29, 0x2e,
	0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x62, 0x69, 0x64, 0x64, 0x65,
	0x72, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x65,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x29, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x23, 0x22, 0x21, 0x2f, 0x76,
	0x31, 0x2f, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x2f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f,
	0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x12,
	0x9c, 0x01, 0x0a, 0x15, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x65, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x12, 0x2a, 0x2e, 0x62, 0x69, 0x64, 0x64,
	0x65, 0x72, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65,
	0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x65, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x2a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x24, 0x22, 0x22, 0x2f, 0x76, 0x31, 0x2f,
	0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x2f, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x64,
	0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x12, 0x8f,
	0x01, 0x0a, 0x11, 0x53, 0x65, 0x74, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x44, 0x65, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x73, 0x12, 0x26, 0x2e, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x44, 0x65, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x62,
	0x69, 0x64, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x54,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x29, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x23, 0x3a, 0x01, 0x2a,
	0x22, 0x1e, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x2f, 0x73, 0x65, 0x74,
	0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x73,
	0x12, 0x98, 0x01, 0x0a, 0x14, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x4d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x29, 0x2e, 0x62, 0x69, 0x64, 0x64,
	0x65, 0x72, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x29, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x23, 0x12, 0x21, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x69,
	0x64, 0x64, 0x65, 0x72, 0x2f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x92, 0x01, 0x0a, 0x12,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x57, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x61,
	0x6c, 0x73, 0x12, 0x27, 0x2e, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x2e, 0x76,
	0x31, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x57, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61,
	0x77, 0x61, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x62, 0x69,
	0x64, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x57, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x61, 0x6c, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x29, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x23, 0x3a, 0x01, 0x2a,
	0x22, 0x1e, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x2f, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x61, 0x6c, 0x73,
	0x12, 0x8c, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x50, 0x72, 0x6f,
	0x76, 0x69, 0x64, 0x65, 0x72, 0x73, 0x12, 0x26, 0x2e, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x61,
	0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x50, 0x72,
	0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27,
	0x2e, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x26, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20, 0x12,
	0x1e, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x2f, 0x67, 0x65, 0x74, 0x5f,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x73, 0x12,
	0x6c, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x12, 0x1f, 0x2e,
	0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d,
	0x2e, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x1e, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x18, 0x12, 0x16, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x69, 0x64, 0x64, 0x65,
	0x72, 0x2f, 0x67, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x12, 0x80, 0x01,
	0x0a, 0x0e, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x73,
	0x12, 0x23, 0x2e, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x44, 0x65, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x23, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x1d, 0x12, 0x1b, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x2f,
	0x67, 0x65, 0x74, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x73,
	0x12, 0x69, 0x0a, 0x08, 0x57, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x12, 0x1d, 0x2e, 0x62,
	0x69, 0x64, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x69, 0x74, 0x68,
	0x64, 0x72, 0x61, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x62, 0x69,
	0x64, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x69, 0x74, 0x68, 0x64,
	0x72, 0x61, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x1e, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x18, 0x3a, 0x01, 0x2a, 0x22, 0x13, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x69, 0x64, 0x64,
	0x65, 0x72, 0x2f, 0x77, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x12, 0x70, 0x0a, 0x0a, 0x47,
	0x65, 0x74, 0x42, 0x69, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1f, 0x2e, 0x62, 0x69, 0x64, 0x64,
	0x65, 0x72, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x69, 0x64, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x62, 0x69, 0x64,
	0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x69, 0x64,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x1f, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x19, 0x12, 0x17, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72,
	0x2f, 0x67, 0x65, 0x74, 0x5f, 0x62, 0x69, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x75, 0x0a,
	0x11, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x53, 0x6c, 0x61, 0x73, 0x68, 0x65, 0x64, 0x46, 0x75, 0x6e,
	0x64, 0x73, 0x12, 0x1a, 0x2e, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x2e, 0x76,
	0x31, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0x1c,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x26, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x20, 0x22, 0x1e, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72,
	0x2f, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x5f, 0x73, 0x6c, 0x61, 0x73, 0x68, 0x65, 0x64, 0x5f, 0x66,
	0x75, 0x6e, 0x64, 0x73, 0x42, 0xaa, 0x02, 0x92, 0x41, 0x72, 0x12, 0x70, 0x0a, 0x0a, 0x42, 0x69,
	0x64, 0x64, 0x65, 0x72, 0x20, 0x41, 0x50, 0x49, 0x2a, 0x55, 0x0a, 0x1b, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x20, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x20, 0x4c, 0x69, 0x63, 0x65,
	0x6e, 0x73, 0x65, 0x20, 0x31, 0x2e, 0x31, 0x12, 0x36, 0x68, 0x74, 0x74, 0x70, 0x73, 0x3a, 0x2f,
	0x2f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x72, 0x69, 0x6d,
	0x65, 0x76, 0x2f, 0x6d, 0x65, 0x76, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x2f, 0x62, 0x6c,
	0x6f, 0x62, 0x2f, 0x6d, 0x61, 0x69, 0x6e, 0x2f, 0x4c, 0x49, 0x43, 0x45, 0x4e, 0x53, 0x45, 0x32,
	0x0b, 0x31, 0x2e, 0x30, 0x2e, 0x30, 0x2d, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x0a, 0x10, 0x63, 0x6f,
	0x6d, 0x2e, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x42, 0x0e,
	0x42, 0x69, 0x64, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01,
	0x5a, 0x40, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x72, 0x69,
	0x6d, 0x65, 0x76, 0x2f, 0x6d, 0x65, 0x76, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x2f, 0x70,
	0x32, 0x70, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x67, 0x6f, 0x2f, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x3b, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69,
	0x76, 0x31, 0xa2, 0x02, 0x03, 0x42, 0x58, 0x58, 0xaa, 0x02, 0x0c, 0x42, 0x69, 0x64, 0x64, 0x65,
	0x72, 0x61, 0x70, 0x69, 0x2e, 0x56, 0x31, 0xca, 0x02, 0x0c, 0x42, 0x69, 0x64, 0x64, 0x65, 0x72,
	0x61, 0x70, 0x69, 0x5c, 0x56, 0x31, 0xe2, 0x02, 0x18, 0x42, 0x69, 0x64, 0x64, 0x65, 0x72, 0x61,
	0x70, 0x69, 0x5c, 0x56, 0x31, 0x5c, 0x47, 0x50, 0x42, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0xea, 0x02, 0x0d, 0x42, 0x69, 0x64, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x3a, 0x3a, 0x56,
	0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_bidderapi_v1_bidderapi_proto_rawDescOnce sync.Once
	file_bidderapi_v1_bidderapi_proto_rawDescData = file_bidderapi_v1_bidderapi_proto_rawDesc
)

func file_bidderapi_v1_bidderapi_proto_rawDescGZIP() []byte {
	file_bidderapi_v1_bidderapi_proto_rawDescOnce.Do(func() {
		file_bidderapi_v1_bidderapi_proto_rawDescData = protoimpl.X.CompressGZIP(file_bidderapi_v1_bidderapi_proto_rawDescData)
	})
	return file_bidderapi_v1_bidderapi_proto_rawDescData
}

var file_bidderapi_v1_bidderapi_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_bidderapi_v1_bidderapi_proto_msgTypes = make([]protoimpl.MessageInfo, 34)
var file_bidderapi_v1_bidderapi_proto_goTypes = []interface{}{
	(PositionConstraint_Anchor)(0),                  // 0: bidderapi.v1.PositionConstraint.Anchor
	(PositionConstraint_Basis)(0),                   // 1: bidderapi.v1.PositionConstraint.Basis
	(*DepositRequest)(nil),                          // 2: bidderapi.v1.DepositRequest
	(*DepositResponse)(nil),                         // 3: bidderapi.v1.DepositResponse
	(*DepositEvenlyRequest)(nil),                    // 4: bidderapi.v1.DepositEvenlyRequest
	(*DepositEvenlyResponse)(nil),                   // 5: bidderapi.v1.DepositEvenlyResponse
	(*EnableDepositManagerRequest)(nil),             // 6: bidderapi.v1.EnableDepositManagerRequest
	(*EnableDepositManagerResponse)(nil),            // 7: bidderapi.v1.EnableDepositManagerResponse
	(*DisableDepositManagerRequest)(nil),            // 8: bidderapi.v1.DisableDepositManagerRequest
	(*DisableDepositManagerResponse)(nil),           // 9: bidderapi.v1.DisableDepositManagerResponse
	(*TargetDeposit)(nil),                           // 10: bidderapi.v1.TargetDeposit
	(*SetTargetDepositsRequest)(nil),                // 11: bidderapi.v1.SetTargetDepositsRequest
	(*SetTargetDepositsResponse)(nil),               // 12: bidderapi.v1.SetTargetDepositsResponse
	(*DepositManagerStatusRequest)(nil),             // 13: bidderapi.v1.DepositManagerStatusRequest
	(*DepositManagerStatusResponse)(nil),            // 14: bidderapi.v1.DepositManagerStatusResponse
	(*EmptyMessage)(nil),                            // 15: bidderapi.v1.EmptyMessage
	(*GetDepositRequest)(nil),                       // 16: bidderapi.v1.GetDepositRequest
	(*GetAllDepositsRequest)(nil),                   // 17: bidderapi.v1.GetAllDepositsRequest
	(*DepositInfo)(nil),                             // 18: bidderapi.v1.DepositInfo
	(*GetAllDepositsResponse)(nil),                  // 19: bidderapi.v1.GetAllDepositsResponse
	(*RequestWithdrawalsRequest)(nil),               // 20: bidderapi.v1.RequestWithdrawalsRequest
	(*RequestWithdrawalsResponse)(nil),              // 21: bidderapi.v1.RequestWithdrawalsResponse
	(*WithdrawRequest)(nil),                         // 22: bidderapi.v1.WithdrawRequest
	(*WithdrawResponse)(nil),                        // 23: bidderapi.v1.WithdrawResponse
	(*GetValidProvidersRequest)(nil),                // 24: bidderapi.v1.GetValidProvidersRequest
	(*GetValidProvidersResponse)(nil),               // 25: bidderapi.v1.GetValidProvidersResponse
	(*PositionConstraint)(nil),                      // 26: bidderapi.v1.PositionConstraint
	(*BidOption)(nil),                               // 27: bidderapi.v1.BidOption
	(*BidOptions)(nil),                              // 28: bidderapi.v1.BidOptions
	(*Bid)(nil),                                     // 29: bidderapi.v1.Bid
	(*Commitment)(nil),                              // 30: bidderapi.v1.Commitment
	(*GetBidInfoRequest)(nil),                       // 31: bidderapi.v1.GetBidInfoRequest
	(*GetBidInfoResponse)(nil),                      // 32: bidderapi.v1.GetBidInfoResponse
	(*GetBidInfoResponse_CommitmentWithStatus)(nil), // 33: bidderapi.v1.GetBidInfoResponse.CommitmentWithStatus
	(*GetBidInfoResponse_BidInfo)(nil),              // 34: bidderapi.v1.GetBidInfoResponse.BidInfo
	(*GetBidInfoResponse_BlockBidInfo)(nil),         // 35: bidderapi.v1.GetBidInfoResponse.BlockBidInfo
	(*wrapperspb.StringValue)(nil),                  // 36: google.protobuf.StringValue
}
var file_bidderapi_v1_bidderapi_proto_depIdxs = []int32{
	10, // 0: bidderapi.v1.SetTargetDepositsRequest.target_deposits:type_name -> bidderapi.v1.TargetDeposit
	10, // 1: bidderapi.v1.SetTargetDepositsResponse.successfully_set_deposits:type_name -> bidderapi.v1.TargetDeposit
	10, // 2: bidderapi.v1.DepositManagerStatusResponse.target_deposits:type_name -> bidderapi.v1.TargetDeposit
	18, // 3: bidderapi.v1.GetAllDepositsResponse.deposits:type_name -> bidderapi.v1.DepositInfo
	0,  // 4: bidderapi.v1.PositionConstraint.anchor:type_name -> bidderapi.v1.PositionConstraint.Anchor
	1,  // 5: bidderapi.v1.PositionConstraint.basis:type_name -> bidderapi.v1.PositionConstraint.Basis
	26, // 6: bidderapi.v1.BidOption.position_constraint:type_name -> bidderapi.v1.PositionConstraint
	27, // 7: bidderapi.v1.BidOptions.options:type_name -> bidderapi.v1.BidOption
	28, // 8: bidderapi.v1.Bid.bid_options:type_name -> bidderapi.v1.BidOptions
	28, // 9: bidderapi.v1.Commitment.bid_options:type_name -> bidderapi.v1.BidOptions
	35, // 10: bidderapi.v1.GetBidInfoResponse.block_bid_info:type_name -> bidderapi.v1.GetBidInfoResponse.BlockBidInfo
	33, // 11: bidderapi.v1.GetBidInfoResponse.BidInfo.commitments:type_name -> bidderapi.v1.GetBidInfoResponse.CommitmentWithStatus
	28, // 12: bidderapi.v1.GetBidInfoResponse.BidInfo.bid_options:type_name -> bidderapi.v1.BidOptions
	34, // 13: bidderapi.v1.GetBidInfoResponse.BlockBidInfo.bids:type_name -> bidderapi.v1.GetBidInfoResponse.BidInfo
	29, // 14: bidderapi.v1.Bidder.SendBid:input_type -> bidderapi.v1.Bid
	2,  // 15: bidderapi.v1.Bidder.Deposit:input_type -> bidderapi.v1.DepositRequest
	4,  // 16: bidderapi.v1.Bidder.DepositEvenly:input_type -> bidderapi.v1.DepositEvenlyRequest
	6,  // 17: bidderapi.v1.Bidder.EnableDepositManager:input_type -> bidderapi.v1.EnableDepositManagerRequest
	8,  // 18: bidderapi.v1.Bidder.DisableDepositManager:input_type -> bidderapi.v1.DisableDepositManagerRequest
	11, // 19: bidderapi.v1.Bidder.SetTargetDeposits:input_type -> bidderapi.v1.SetTargetDepositsRequest
	13, // 20: bidderapi.v1.Bidder.DepositManagerStatus:input_type -> bidderapi.v1.DepositManagerStatusRequest
	20, // 21: bidderapi.v1.Bidder.RequestWithdrawals:input_type -> bidderapi.v1.RequestWithdrawalsRequest
	24, // 22: bidderapi.v1.Bidder.GetValidProviders:input_type -> bidderapi.v1.GetValidProvidersRequest
	16, // 23: bidderapi.v1.Bidder.GetDeposit:input_type -> bidderapi.v1.GetDepositRequest
	17, // 24: bidderapi.v1.Bidder.GetAllDeposits:input_type -> bidderapi.v1.GetAllDepositsRequest
	22, // 25: bidderapi.v1.Bidder.Withdraw:input_type -> bidderapi.v1.WithdrawRequest
	31, // 26: bidderapi.v1.Bidder.GetBidInfo:input_type -> bidderapi.v1.GetBidInfoRequest
	15, // 27: bidderapi.v1.Bidder.ClaimSlashedFunds:input_type -> bidderapi.v1.EmptyMessage
	30, // 28: bidderapi.v1.Bidder.SendBid:output_type -> bidderapi.v1.Commitment
	3,  // 29: bidderapi.v1.Bidder.Deposit:output_type -> bidderapi.v1.DepositResponse
	5,  // 30: bidderapi.v1.Bidder.DepositEvenly:output_type -> bidderapi.v1.DepositEvenlyResponse
	7,  // 31: bidderapi.v1.Bidder.EnableDepositManager:output_type -> bidderapi.v1.EnableDepositManagerResponse
	9,  // 32: bidderapi.v1.Bidder.DisableDepositManager:output_type -> bidderapi.v1.DisableDepositManagerResponse
	12, // 33: bidderapi.v1.Bidder.SetTargetDeposits:output_type -> bidderapi.v1.SetTargetDepositsResponse
	14, // 34: bidderapi.v1.Bidder.DepositManagerStatus:output_type -> bidderapi.v1.DepositManagerStatusResponse
	21, // 35: bidderapi.v1.Bidder.RequestWithdrawals:output_type -> bidderapi.v1.RequestWithdrawalsResponse
	25, // 36: bidderapi.v1.Bidder.GetValidProviders:output_type -> bidderapi.v1.GetValidProvidersResponse
	3,  // 37: bidderapi.v1.Bidder.GetDeposit:output_type -> bidderapi.v1.DepositResponse
	19, // 38: bidderapi.v1.Bidder.GetAllDeposits:output_type -> bidderapi.v1.GetAllDepositsResponse
	23, // 39: bidderapi.v1.Bidder.Withdraw:output_type -> bidderapi.v1.WithdrawResponse
	32, // 40: bidderapi.v1.Bidder.GetBidInfo:output_type -> bidderapi.v1.GetBidInfoResponse
	36, // 41: bidderapi.v1.Bidder.ClaimSlashedFunds:output_type -> google.protobuf.StringValue
	28, // [28:42] is the sub-list for method output_type
	14, // [14:28] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_bidderapi_v1_bidderapi_proto_init() }
func file_bidderapi_v1_bidderapi_proto_init() {
	if File_bidderapi_v1_bidderapi_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_bidderapi_v1_bidderapi_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DepositRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bidderapi_v1_bidderapi_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DepositResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bidderapi_v1_bidderapi_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DepositEvenlyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bidderapi_v1_bidderapi_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DepositEvenlyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bidderapi_v1_bidderapi_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnableDepositManagerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bidderapi_v1_bidderapi_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnableDepositManagerResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bidderapi_v1_bidderapi_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DisableDepositManagerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bidderapi_v1_bidderapi_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DisableDepositManagerResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bidderapi_v1_bidderapi_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TargetDeposit); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bidderapi_v1_bidderapi_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetTargetDepositsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bidderapi_v1_bidderapi_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetTargetDepositsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bidderapi_v1_bidderapi_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DepositManagerStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bidderapi_v1_bidderapi_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DepositManagerStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bidderapi_v1_bidderapi_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmptyMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bidderapi_v1_bidderapi_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDepositRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bidderapi_v1_bidderapi_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAllDepositsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bidderapi_v1_bidderapi_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DepositInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bidderapi_v1_bidderapi_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAllDepositsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bidderapi_v1_bidderapi_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RequestWithdrawalsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bidderapi_v1_bidderapi_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RequestWithdrawalsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bidderapi_v1_bidderapi_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WithdrawRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bidderapi_v1_bidderapi_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WithdrawResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bidderapi_v1_bidderapi_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetValidProvidersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bidderapi_v1_bidderapi_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetValidProvidersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bidderapi_v1_bidderapi_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PositionConstraint); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bidderapi_v1_bidderapi_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bidderapi_v1_bidderapi_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bidderapi_v1_bidderapi_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Bid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bidderapi_v1_bidderapi_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Commitment); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bidderapi_v1_bidderapi_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBidInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bidderapi_v1_bidderapi_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBidInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bidderapi_v1_bidderapi_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBidInfoResponse_CommitmentWithStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bidderapi_v1_bidderapi_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBidInfoResponse_BidInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bidderapi_v1_bidderapi_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBidInfoResponse_BlockBidInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_bidderapi_v1_bidderapi_proto_msgTypes[25].OneofWrappers = []interface{}{
		(*BidOption_PositionConstraint)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_bidderapi_v1_bidderapi_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   34,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_bidderapi_v1_bidderapi_proto_goTypes,
		DependencyIndexes: file_bidderapi_v1_bidderapi_proto_depIdxs,
		EnumInfos:         file_bidderapi_v1_bidderapi_proto_enumTypes,
		MessageInfos:      file_bidderapi_v1_bidderapi_proto_msgTypes,
	}.Build()
	File_bidderapi_v1_bidderapi_proto = out.File
	file_bidderapi_v1_bidderapi_proto_rawDesc = nil
	file_bidderapi_v1_bidderapi_proto_goTypes = nil
	file_bidderapi_v1_bidderapi_proto_depIdxs = nil
}

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: bidderapi/v1/bidderapi.proto

package bidderapiv1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Bidder_SendBid_FullMethodName               = "/bidderapi.v1.Bidder/SendBid"
	Bidder_Deposit_FullMethodName               = "/bidderapi.v1.Bidder/Deposit"
	Bidder_DepositEvenly_FullMethodName         = "/bidderapi.v1.Bidder/DepositEvenly"
	Bidder_EnableDepositManager_FullMethodName  = "/bidderapi.v1.Bidder/EnableDepositManager"
	Bidder_DisableDepositManager_FullMethodName = "/bidderapi.v1.Bidder/DisableDepositManager"
	Bidder_SetTargetDeposits_FullMethodName     = "/bidderapi.v1.Bidder/SetTargetDeposits"
	Bidder_DepositManagerStatus_FullMethodName  = "/bidderapi.v1.Bidder/DepositManagerStatus"
	Bidder_RequestWithdrawals_FullMethodName    = "/bidderapi.v1.Bidder/RequestWithdrawals"
	Bidder_GetValidProviders_FullMethodName     = "/bidderapi.v1.Bidder/GetValidProviders"
	Bidder_GetDeposit_FullMethodName            = "/bidderapi.v1.Bidder/GetDeposit"
	Bidder_GetAllDeposits_FullMethodName        = "/bidderapi.v1.Bidder/GetAllDeposits"
	Bidder_Withdraw_FullMethodName              = "/bidderapi.v1.Bidder/Withdraw"
	Bidder_GetBidInfo_FullMethodName            = "/bidderapi.v1.Bidder/GetBidInfo"
	Bidder_ClaimSlashedFunds_FullMethodName     = "/bidderapi.v1.Bidder/ClaimSlashedFunds"
)

// BidderClient is the client API for Bidder service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BidderClient interface {
	// SendBid
	//
	// Send a bid to the bidder mev-commit node. The bid is a message from the bidder to the provider
	// with the transaction hashes and the amount of ETH that the bidder is willing to pay to the provider
	// for including the transaction in the block. The bid also includes the block number that the bidder
	// wants to include the transaction in, the start and end timestamps for the bid decay. The bidder can
	// optionally include the raw transaction payloads (hex encoded RLP) instead of transaction hashes.
	SendBid(ctx context.Context, in *Bid, opts ...grpc.CallOption) (grpc.ServerStreamingClient[Commitment], error)
	// Deposit
	//
	// Deposit is called by the bidder node to add deposit in the bidder registry, specific to a provider.
	Deposit(ctx context.Context, in *DepositRequest, opts ...grpc.CallOption) (*DepositResponse, error)
	// DepositEvenly
	//
	// DepositEvenly is called by the bidder node to deposit a total amount evenly across multiple providers.
	DepositEvenly(ctx context.Context, in *DepositEvenlyRequest, opts ...grpc.CallOption) (*DepositEvenlyResponse, error)
	// EnableDepositManager
	//
	// EnableDepositManager is called by the bidder node to enable the deposit manager via eip 7702.
	EnableDepositManager(ctx context.Context, in *EnableDepositManagerRequest, opts ...grpc.CallOption) (*EnableDepositManagerResponse, error)
	// DisableDepositManager
	//
	// DisableDepositManager is called by the bidder node to disable the deposit manager
	// by setting the bidder EOA's code to zero address.
	DisableDepositManager(ctx context.Context, in *DisableDepositManagerRequest, opts ...grpc.CallOption) (*DisableDepositManagerResponse, error)
	// SetTargetDeposits
	//
	// SetTargetDeposits is called by the bidder node to set target deposits per provider
	// within the deposit manager. During this call, the bidder node will also attempt to top-up
	// deposits for each new target deposit.
	SetTargetDeposits(ctx context.Context, in *SetTargetDepositsRequest, opts ...grpc.CallOption) (*SetTargetDepositsResponse, error)
	// DepositManagerStatus
	//
	// DepositManagerStatus is called by the bidder node to query whether the bidder
	// has enabled the deposit manager via eip 7702.
	DepositManagerStatus(ctx context.Context, in *DepositManagerStatusRequest, opts ...grpc.CallOption) (*DepositManagerStatusResponse, error)
	// RequestWithdrawals
	//
	// RequestWithdrawals is called by the bidder node to request withdrawals from provider(s)
	RequestWithdrawals(ctx context.Context, in *RequestWithdrawalsRequest, opts ...grpc.CallOption) (*RequestWithdrawalsResponse, error)
	// GetValidProviders
	//
	// GetValidProviders is called by the bidder node to get a list of all valid providers.
	// Each provider returned by this RPC must:
	// - Be "registered" in the provider registry
	// - Have deposit >= minStake in provider registry
	// - Have no pending withdrawal request with provider registry
	// - Have at least one BLS key registered with provider registry
	GetValidProviders(ctx context.Context, in *GetValidProvidersRequest, opts ...grpc.CallOption) (*GetValidProvidersResponse, error)
	// GetDeposit
	//
	// GetDeposit is called by the bidder to get its deposit specific to a provider in the bidder registry.
	GetDeposit(ctx context.Context, in *GetDepositRequest, opts ...grpc.CallOption) (*DepositResponse, error)
	// GetAllDeposits
	//
	// GetAllDeposits is called by the bidder to get all its deposits in the bidder registry,
	// and the balance of the bidder EOA itself.
	GetAllDeposits(ctx context.Context, in *GetAllDepositsRequest, opts ...grpc.CallOption) (*GetAllDepositsResponse, error)
	// Withdraw
	//
	// Withdraw is called by the bidder to withdraw their deposit to a provider.
	Withdraw(ctx context.Context, in *WithdrawRequest, opts ...grpc.CallOption) (*WithdrawResponse, error)
	// GetBidInfo
	//
	// GetBidInfo is called by the bidder to get the bid information. If block number is not specified,
	// all known block numbers are returned in the ascending order.
	GetBidInfo(ctx context.Context, in *GetBidInfoRequest, opts ...grpc.CallOption) (*GetBidInfoResponse, error)
	// ClaimSlashedFunds
	//
	// ClaimSlashedFunds is called by the bidder to claim slashed funds from the provider. The response
	// will show the amount claimed if any in wei.
	ClaimSlashedFunds(ctx context.Context, in *EmptyMessage, opts ...grpc.CallOption) (*wrapperspb.StringValue, error)
}

type bidderClient struct {
	cc grpc.ClientConnInterface
}

func NewBidderClient(cc grpc.ClientConnInterface) BidderClient {
	return &bidderClient{cc}
}

func (c *bidderClient) SendBid(ctx context.Context, in *Bid, opts ...grpc.CallOption) (grpc.ServerStreamingClient[Commitment], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &Bidder_ServiceDesc.Streams[0], Bidder_SendBid_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[Bid, Commitment]{ClientStream: stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type Bidder_SendBidClient = grpc.ServerStreamingClient[Commitment]

func (c *bidderClient) Deposit(ctx context.Context, in *DepositRequest, opts ...grpc.CallOption) (*DepositResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DepositResponse)
	err := c.cc.Invoke(ctx, Bidder_Deposit_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bidderClient) DepositEvenly(ctx context.Context, in *DepositEvenlyRequest, opts ...grpc.CallOption) (*DepositEvenlyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DepositEvenlyResponse)
	err := c.cc.Invoke(ctx, Bidder_DepositEvenly_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bidderClient) EnableDepositManager(ctx context.Context, in *EnableDepositManagerRequest, opts ...grpc.CallOption) (*EnableDepositManagerResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EnableDepositManagerResponse)
	err := c.cc.Invoke(ctx, Bidder_EnableDepositManager_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bidderClient) DisableDepositManager(ctx context.Context, in *DisableDepositManagerRequest, opts ...grpc.CallOption) (*DisableDepositManagerResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DisableDepositManagerResponse)
	err := c.cc.Invoke(ctx, Bidder_DisableDepositManager_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bidderClient) SetTargetDeposits(ctx context.Context, in *SetTargetDepositsRequest, opts ...grpc.CallOption) (*SetTargetDepositsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SetTargetDepositsResponse)
	err := c.cc.Invoke(ctx, Bidder_SetTargetDeposits_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bidderClient) DepositManagerStatus(ctx context.Context, in *DepositManagerStatusRequest, opts ...grpc.CallOption) (*DepositManagerStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DepositManagerStatusResponse)
	err := c.cc.Invoke(ctx, Bidder_DepositManagerStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bidderClient) RequestWithdrawals(ctx context.Context, in *RequestWithdrawalsRequest, opts ...grpc.CallOption) (*RequestWithdrawalsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RequestWithdrawalsResponse)
	err := c.cc.Invoke(ctx, Bidder_RequestWithdrawals_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bidderClient) GetValidProviders(ctx context.Context, in *GetValidProvidersRequest, opts ...grpc.CallOption) (*GetValidProvidersResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetValidProvidersResponse)
	err := c.cc.Invoke(ctx, Bidder_GetValidProviders_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bidderClient) GetDeposit(ctx context.Context, in *GetDepositRequest, opts ...grpc.CallOption) (*DepositResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DepositResponse)
	err := c.cc.Invoke(ctx, Bidder_GetDeposit_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bidderClient) GetAllDeposits(ctx context.Context, in *GetAllDepositsRequest, opts ...grpc.CallOption) (*GetAllDepositsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAllDepositsResponse)
	err := c.cc.Invoke(ctx, Bidder_GetAllDeposits_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bidderClient) Withdraw(ctx context.Context, in *WithdrawRequest, opts ...grpc.CallOption) (*WithdrawResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(WithdrawResponse)
	err := c.cc.Invoke(ctx, Bidder_Withdraw_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bidderClient) GetBidInfo(ctx context.Context, in *GetBidInfoRequest, opts ...grpc.CallOption) (*GetBidInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBidInfoResponse)
	err := c.cc.Invoke(ctx, Bidder_GetBidInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bidderClient) ClaimSlashedFunds(ctx context.Context, in *EmptyMessage, opts ...grpc.CallOption) (*wrapperspb.StringValue, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(wrapperspb.StringValue)
	err := c.cc.Invoke(ctx, Bidder_ClaimSlashedFunds_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BidderServer is the server API for Bidder service.
// All implementations must embed UnimplementedBidderServer
// for forward compatibility.
type BidderServer interface {
	// SendBid
	//
	// Send a bid to the bidder mev-commit node. The bid is a message from the bidder to the provider
	// with the transaction hashes and the amount of ETH that the bidder is willing to pay to the provider
	// for including the transaction in the block. The bid also includes the block number that the bidder
	// wants to include the transaction in, the start and end timestamps for the bid decay. The bidder can
	// optionally include the raw transaction payloads (hex encoded RLP) instead of transaction hashes.
	SendBid(*Bid, grpc.ServerStreamingServer[Commitment]) error
	// Deposit
	//
	// Deposit is called by the bidder node to add deposit in the bidder registry, specific to a provider.
	Deposit(context.Context, *DepositRequest) (*DepositResponse, error)
	// DepositEvenly
	//
	// DepositEvenly is called by the bidder node to deposit a total amount evenly across multiple providers.
	DepositEvenly(context.Context, *DepositEvenlyRequest) (*DepositEvenlyResponse, error)
	// EnableDepositManager
	//
	// EnableDepositManager is called by the bidder node to enable the deposit manager via eip 7702.
	EnableDepositManager(context.Context, *EnableDepositManagerRequest) (*EnableDepositManagerResponse, error)
	// DisableDepositManager
	//
	// DisableDepositManager is called by the bidder node to disable the deposit manager
	// by setting the bidder EOA's code to zero address.
	DisableDepositManager(context.Context, *DisableDepositManagerRequest) (*DisableDepositManagerResponse, error)
	// SetTargetDeposits
	//
	// SetTargetDeposits is called by the bidder node to set target deposits per provider
	// within the deposit manager. During this call, the bidder node will also attempt to top-up
	// deposits for each new target deposit.
	SetTargetDeposits(context.Context, *SetTargetDepositsRequest) (*SetTargetDepositsResponse, error)
	// DepositManagerStatus
	//
	// DepositManagerStatus is called by the bidder node to query whether the bidder
	// has enabled the deposit manager via eip 7702.
	DepositManagerStatus(context.Context, *DepositManagerStatusRequest) (*DepositManagerStatusResponse, error)
	// RequestWithdrawals
	//
	// RequestWithdrawals is called by the bidder node to request withdrawals from provider(s)
	RequestWithdrawals(context.Context, *RequestWithdrawalsRequest) (*RequestWithdrawalsResponse, error)
	// GetValidProviders
	//
	// GetValidProviders is called by the bidder node to get a list of all valid providers.
	// Each provider returned by this RPC must:
	// - Be "registered" in the provider registry
	// - Have deposit >= minStake in provider registry
	// - Have no pending withdrawal request with provider registry
	// - Have at least one BLS key registered with provider registry
	GetValidProviders(context.Context, *GetValidProvidersRequest) (*GetValidProvidersResponse, error)
	// GetDeposit
	//
	// GetDeposit is called by the bidder to get its deposit specific to a provider in the bidder registry.
	GetDeposit(context.Context, *GetDepositRequest) (*DepositResponse, error)
	// GetAllDeposits
	//
	// GetAllDeposits is called by the bidder to get all its deposits in the bidder registry,
	// and the balance of the bidder EOA itself.
	GetAllDeposits(context.Context, *GetAllDepositsRequest) (*GetAllDepositsResponse, error)
	// Withdraw
	//
	// Withdraw is called by the bidder to withdraw their deposit to a provider.
	Withdraw(context.Context, *WithdrawRequest) (*WithdrawResponse, error)
	// GetBidInfo
	//
	// GetBidInfo is called by the bidder to get the bid information. If block number is not specified,
	// all known block numbers are returned in the ascending order.
	GetBidInfo(context.Context, *GetBidInfoRequest) (*GetBidInfoResponse, error)
	// ClaimSlashedFunds
	//
	// ClaimSlashedFunds is called by the bidder to claim slashed funds from the provider. The response
	// will show the amount claimed if any in wei.
	ClaimSlashedFunds(context.Context, *EmptyMessage) (*wrapperspb.StringValue, error)
	mustEmbedUnimplementedBidderServer()
}

// UnimplementedBidderServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedBidderServer struct{}

func (UnimplementedBidderServer) SendBid(*Bid, grpc.ServerStreamingServer[Commitment]) error {
	return status.Errorf(codes.Unimplemented, "method SendBid not implemented")
}
func (UnimplementedBidderServer) Deposit(context.Context, *DepositRequest) (*DepositResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Deposit not implemented")
}
func (UnimplementedBidderServer) DepositEvenly(context.Context, *DepositEvenlyRequest) (*DepositEvenlyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DepositEvenly not implemented")
}
func (UnimplementedBidderServer) EnableDepositManager(context.Context, *EnableDepositManagerRequest) (*EnableDepositManagerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EnableDepositManager not implemented")
}
func (UnimplementedBidderServer) DisableDepositManager(context.Context, *DisableDepositManagerRequest) (*DisableDepositManagerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DisableDepositManager not implemented")
}
func (UnimplementedBidderServer) SetTargetDeposits(context.Context, *SetTargetDepositsRequest) (*SetTargetDepositsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetTargetDeposits not implemented")
}
func (UnimplementedBidderServer) DepositManagerStatus(context.Context, *DepositManagerStatusRequest) (*DepositManagerStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DepositManagerStatus not implemented")
}
func (UnimplementedBidderServer) RequestWithdrawals(context.Context, *RequestWithdrawalsRequest) (*RequestWithdrawalsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RequestWithdrawals not implemented")
}
func (UnimplementedBidderServer) GetValidProviders(context.Context, *GetValidProvidersRequest) (*GetValidProvidersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetValidProviders not implemented")
}
func (UnimplementedBidderServer) GetDeposit(context.Context, *GetDepositRequest) (*DepositResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDeposit not implemented")
}
func (UnimplementedBidderServer) GetAllDeposits(context.Context, *GetAllDepositsRequest) (*GetAllDepositsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllDeposits not implemented")
}
func (UnimplementedBidderServer) Withdraw(context.Context, *WithdrawRequest) (*WithdrawResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Withdraw not implemented")
}
func (UnimplementedBidderServer) GetBidInfo(context.Context, *GetBidInfoRequest) (*GetBidInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBidInfo not implemented")
}
func (UnimplementedBidderServer) ClaimSlashedFunds(context.Context, *EmptyMessage) (*wrapperspb.StringValue, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ClaimSlashedFunds not implemented")
}
func (UnimplementedBidderServer) mustEmbedUnimplementedBidderServer() {}
func (UnimplementedBidderServer) testEmbeddedByValue()                {}

// UnsafeBidderServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BidderServer will
// result in compilation errors.
type UnsafeBidderServer interface {
	mustEmbedUnimplementedBidderServer()
}

func RegisterBidderServer(s grpc.ServiceRegistrar, srv BidderServer) {
	// If the following call pancis, it indicates UnimplementedBidderServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Bidder_ServiceDesc, srv)
}

func _Bidder_SendBid_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(Bid)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(BidderServer).SendBid(m, &grpc.GenericServerStream[Bid, Commitment]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type Bidder_SendBidServer = grpc.ServerStreamingServer[Commitment]

func _Bidder_Deposit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DepositRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BidderServer).Deposit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bidder_Deposit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BidderServer).Deposit(ctx, req.(*DepositRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bidder_DepositEvenly_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DepositEvenlyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BidderServer).DepositEvenly(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bidder_DepositEvenly_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BidderServer).DepositEvenly(ctx, req.(*DepositEvenlyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bidder_EnableDepositManager_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EnableDepositManagerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BidderServer).EnableDepositManager(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bidder_EnableDepositManager_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BidderServer).EnableDepositManager(ctx, req.(*EnableDepositManagerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bidder_DisableDepositManager_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DisableDepositManagerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BidderServer).DisableDepositManager(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bidder_DisableDepositManager_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BidderServer).DisableDepositManager(ctx, req.(*DisableDepositManagerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bidder_SetTargetDeposits_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetTargetDepositsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BidderServer).SetTargetDeposits(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bidder_SetTargetDeposits_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BidderServer).SetTargetDeposits(ctx, req.(*SetTargetDepositsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bidder_DepositManagerStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DepositManagerStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BidderServer).DepositManagerStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bidder_DepositManagerStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BidderServer).DepositManagerStatus(ctx, req.(*DepositManagerStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bidder_RequestWithdrawals_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RequestWithdrawalsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BidderServer).RequestWithdrawals(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bidder_RequestWithdrawals_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BidderServer).RequestWithdrawals(ctx, req.(*RequestWithdrawalsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bidder_GetValidProviders_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetValidProvidersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BidderServer).GetValidProviders(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bidder_GetValidProviders_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BidderServer).GetValidProviders(ctx, req.(*GetValidProvidersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bidder_GetDeposit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDepositRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BidderServer).GetDeposit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bidder_GetDeposit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BidderServer).GetDeposit(ctx, req.(*GetDepositRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bidder_GetAllDeposits_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllDepositsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BidderServer).GetAllDeposits(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bidder_GetAllDeposits_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BidderServer).GetAllDeposits(ctx, req.(*GetAllDepositsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bidder_Withdraw_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WithdrawRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BidderServer).Withdraw(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bidder_Withdraw_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BidderServer).Withdraw(ctx, req.(*WithdrawRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bidder_GetBidInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBidInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BidderServer).GetBidInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bidder_GetBidInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BidderServer).GetBidInfo(ctx, req.(*GetBidInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bidder_ClaimSlashedFunds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EmptyMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BidderServer).ClaimSlashedFunds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bidder_ClaimSlashedFunds_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BidderServer).ClaimSlashedFunds(ctx, req.(*EmptyMessage))
	}
	return interceptor(ctx, in, info, handler)
}

// Bidder_ServiceDesc is the grpc.ServiceDesc for Bidder service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Bidder_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "bidderapi.v1.Bidder",
	HandlerType: (*BidderServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Deposit",
			Handler:    _Bidder_Deposit_Handler,
		},
		{
			MethodName: "DepositEvenly",
			Handler:    _Bidder_DepositEvenly_Handler,
		},
		{
			MethodName: "EnableDepositManager",
			Handler:    _Bidder_EnableDepositManager_Handler,
		},
		{
			MethodName: "DisableDepositManager",
			Handler:    _Bidder_DisableDepositManager_Handler,
		},
		{
			MethodName: "SetTargetDeposits",
			Handler:    _Bidder_SetTargetDeposits_Handler,
		},
		{
			MethodName: "DepositManagerStatus",
			Handler:    _Bidder_DepositManagerStatus_Handler,
		},
		{
			MethodName: "RequestWithdrawals",
			Handler:    _Bidder_RequestWithdrawals_Handler,
		},
		{
			MethodName: "GetValidProviders",
			Handler:    _Bidder_GetValidProviders_Handler,
		},
		{
			MethodName: "GetDeposit",
			Handler:    _Bidder_GetDeposit_Handler,
		},
		{
			MethodName: "GetAllDeposits",
			Handler:    _Bidder_GetAllDeposits_Handler,
		},
		{
			MethodName: "Withdraw",
			Handler:    _Bidder_Withdraw_Handler,
		},
		{
			MethodName: "GetBidInfo",
			Handler:    _Bidder_GetBidInfo_Handler,
		},
		{
			MethodName: "ClaimSlashedFunds",
			Handler:    _Bidder_ClaimSlashedFunds_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "SendBid",
			Handler:       _Bidder_SendBid_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "bidderapi/v1/bidderapi.proto",
}

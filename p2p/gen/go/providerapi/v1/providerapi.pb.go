// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        (unknown)
// source: providerapi/v1/providerapi.proto

package providerapiv1

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PositionConstraint_Anchor int32

const (
	PositionConstraint_ANCHOR_UNSPECIFIED PositionConstraint_Anchor = 0
	PositionConstraint_ANCHOR_TOP         PositionConstraint_Anchor = 1 // Position is at the top of the block
	PositionConstraint_ANCHOR_BOTTOM      PositionConstraint_Anchor = 2 // Position is at the bottom of the block
)

// Enum value maps for PositionConstraint_Anchor.
var (
	PositionConstraint_Anchor_name = map[int32]string{
		0: "ANCHOR_UNSPECIFIED",
		1: "ANCHOR_TOP",
		2: "ANCHOR_BOTTOM",
	}
	PositionConstraint_Anchor_value = map[string]int32{
		"ANCHOR_UNSPECIFIED": 0,
		"ANCHOR_TOP":         1,
		"ANCHOR_BOTTOM":      2,
	}
)

func (x PositionConstraint_Anchor) Enum() *PositionConstraint_Anchor {
	p := new(PositionConstraint_Anchor)
	*p = x
	return p
}

func (x PositionConstraint_Anchor) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PositionConstraint_Anchor) Descriptor() protoreflect.EnumDescriptor {
	return file_providerapi_v1_providerapi_proto_enumTypes[0].Descriptor()
}

func (PositionConstraint_Anchor) Type() protoreflect.EnumType {
	return &file_providerapi_v1_providerapi_proto_enumTypes[0]
}

func (x PositionConstraint_Anchor) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PositionConstraint_Anchor.Descriptor instead.
func (PositionConstraint_Anchor) EnumDescriptor() ([]byte, []int) {
	return file_providerapi_v1_providerapi_proto_rawDescGZIP(), []int{5, 0}
}

type PositionConstraint_Basis int32

const (
	PositionConstraint_BASIS_UNSPECIFIED    PositionConstraint_Basis = 0
	PositionConstraint_BASIS_PERCENTILE     PositionConstraint_Basis = 1 // Position is a percentile of the block size
	PositionConstraint_BASIS_ABSOLUTE       PositionConstraint_Basis = 2 // Position is an absolute position in the block
	PositionConstraint_BASIS_GAS_PERCENTILE PositionConstraint_Basis = 3 // Position is a percentile of the gas used in the block
)

// Enum value maps for PositionConstraint_Basis.
var (
	PositionConstraint_Basis_name = map[int32]string{
		0: "BASIS_UNSPECIFIED",
		1: "BASIS_PERCENTILE",
		2: "BASIS_ABSOLUTE",
		3: "BASIS_GAS_PERCENTILE",
	}
	PositionConstraint_Basis_value = map[string]int32{
		"BASIS_UNSPECIFIED":    0,
		"BASIS_PERCENTILE":     1,
		"BASIS_ABSOLUTE":       2,
		"BASIS_GAS_PERCENTILE": 3,
	}
)

func (x PositionConstraint_Basis) Enum() *PositionConstraint_Basis {
	p := new(PositionConstraint_Basis)
	*p = x
	return p
}

func (x PositionConstraint_Basis) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PositionConstraint_Basis) Descriptor() protoreflect.EnumDescriptor {
	return file_providerapi_v1_providerapi_proto_enumTypes[1].Descriptor()
}

func (PositionConstraint_Basis) Type() protoreflect.EnumType {
	return &file_providerapi_v1_providerapi_proto_enumTypes[1]
}

func (x PositionConstraint_Basis) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PositionConstraint_Basis.Descriptor instead.
func (PositionConstraint_Basis) EnumDescriptor() ([]byte, []int) {
	return file_providerapi_v1_providerapi_proto_rawDescGZIP(), []int{5, 1}
}

type BidResponse_Status int32

const (
	BidResponse_STATUS_UNSPECIFIED BidResponse_Status = 0
	BidResponse_STATUS_ACCEPTED    BidResponse_Status = 1
	BidResponse_STATUS_REJECTED    BidResponse_Status = 2
)

// Enum value maps for BidResponse_Status.
var (
	BidResponse_Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "STATUS_ACCEPTED",
		2: "STATUS_REJECTED",
	}
	BidResponse_Status_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"STATUS_ACCEPTED":    1,
		"STATUS_REJECTED":    2,
	}
)

func (x BidResponse_Status) Enum() *BidResponse_Status {
	p := new(BidResponse_Status)
	*p = x
	return p
}

func (x BidResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_providerapi_v1_providerapi_proto_enumTypes[2].Descriptor()
}

func (BidResponse_Status) Type() protoreflect.EnumType {
	return &file_providerapi_v1_providerapi_proto_enumTypes[2]
}

func (x BidResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BidResponse_Status.Descriptor instead.
func (BidResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_providerapi_v1_providerapi_proto_rawDescGZIP(), []int{9, 0}
}

type StakeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Amount        string   `protobuf:"bytes,1,opt,name=amount,proto3" json:"amount,omitempty"`
	BlsPublicKeys []string `protobuf:"bytes,2,rep,name=bls_public_keys,json=blsPublicKeys,proto3" json:"bls_public_keys,omitempty"`
	BlsSignatures []string `protobuf:"bytes,3,rep,name=bls_signatures,json=blsSignatures,proto3" json:"bls_signatures,omitempty"`
}

func (x *StakeRequest) Reset() {
	*x = StakeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_providerapi_v1_providerapi_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StakeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StakeRequest) ProtoMessage() {}

func (x *StakeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_providerapi_v1_providerapi_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StakeRequest.ProtoReflect.Descriptor instead.
func (*StakeRequest) Descriptor() ([]byte, []int) {
	return file_providerapi_v1_providerapi_proto_rawDescGZIP(), []int{0}
}

func (x *StakeRequest) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

func (x *StakeRequest) GetBlsPublicKeys() []string {
	if x != nil {
		return x.BlsPublicKeys
	}
	return nil
}

func (x *StakeRequest) GetBlsSignatures() []string {
	if x != nil {
		return x.BlsSignatures
	}
	return nil
}

type StakeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Amount        string   `protobuf:"bytes,1,opt,name=amount,proto3" json:"amount,omitempty"`
	BlsPublicKeys []string `protobuf:"bytes,2,rep,name=bls_public_keys,json=blsPublicKeys,proto3" json:"bls_public_keys,omitempty"`
}

func (x *StakeResponse) Reset() {
	*x = StakeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_providerapi_v1_providerapi_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StakeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StakeResponse) ProtoMessage() {}

func (x *StakeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_providerapi_v1_providerapi_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StakeResponse.ProtoReflect.Descriptor instead.
func (*StakeResponse) Descriptor() ([]byte, []int) {
	return file_providerapi_v1_providerapi_proto_rawDescGZIP(), []int{1}
}

func (x *StakeResponse) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

func (x *StakeResponse) GetBlsPublicKeys() []string {
	if x != nil {
		return x.BlsPublicKeys
	}
	return nil
}

type WithdrawalResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Amount string `protobuf:"bytes,1,opt,name=amount,proto3" json:"amount,omitempty"`
}

func (x *WithdrawalResponse) Reset() {
	*x = WithdrawalResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_providerapi_v1_providerapi_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WithdrawalResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WithdrawalResponse) ProtoMessage() {}

func (x *WithdrawalResponse) ProtoReflect() protoreflect.Message {
	mi := &file_providerapi_v1_providerapi_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WithdrawalResponse.ProtoReflect.Descriptor instead.
func (*WithdrawalResponse) Descriptor() ([]byte, []int) {
	return file_providerapi_v1_providerapi_proto_rawDescGZIP(), []int{2}
}

func (x *WithdrawalResponse) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

type RewardResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Amount string `protobuf:"bytes,1,opt,name=amount,proto3" json:"amount,omitempty"`
}

func (x *RewardResponse) Reset() {
	*x = RewardResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_providerapi_v1_providerapi_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardResponse) ProtoMessage() {}

func (x *RewardResponse) ProtoReflect() protoreflect.Message {
	mi := &file_providerapi_v1_providerapi_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardResponse.ProtoReflect.Descriptor instead.
func (*RewardResponse) Descriptor() ([]byte, []int) {
	return file_providerapi_v1_providerapi_proto_rawDescGZIP(), []int{3}
}

func (x *RewardResponse) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

type EmptyMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EmptyMessage) Reset() {
	*x = EmptyMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_providerapi_v1_providerapi_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmptyMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmptyMessage) ProtoMessage() {}

func (x *EmptyMessage) ProtoReflect() protoreflect.Message {
	mi := &file_providerapi_v1_providerapi_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmptyMessage.ProtoReflect.Descriptor instead.
func (*EmptyMessage) Descriptor() ([]byte, []int) {
	return file_providerapi_v1_providerapi_proto_rawDescGZIP(), []int{4}
}

type PositionConstraint struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Anchor PositionConstraint_Anchor `protobuf:"varint,1,opt,name=anchor,proto3,enum=providerapi.v1.PositionConstraint_Anchor" json:"anchor,omitempty"`
	Basis  PositionConstraint_Basis  `protobuf:"varint,2,opt,name=basis,proto3,enum=providerapi.v1.PositionConstraint_Basis" json:"basis,omitempty"`
	Value  int32                     `protobuf:"varint,3,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *PositionConstraint) Reset() {
	*x = PositionConstraint{}
	if protoimpl.UnsafeEnabled {
		mi := &file_providerapi_v1_providerapi_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PositionConstraint) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PositionConstraint) ProtoMessage() {}

func (x *PositionConstraint) ProtoReflect() protoreflect.Message {
	mi := &file_providerapi_v1_providerapi_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PositionConstraint.ProtoReflect.Descriptor instead.
func (*PositionConstraint) Descriptor() ([]byte, []int) {
	return file_providerapi_v1_providerapi_proto_rawDescGZIP(), []int{5}
}

func (x *PositionConstraint) GetAnchor() PositionConstraint_Anchor {
	if x != nil {
		return x.Anchor
	}
	return PositionConstraint_ANCHOR_UNSPECIFIED
}

func (x *PositionConstraint) GetBasis() PositionConstraint_Basis {
	if x != nil {
		return x.Basis
	}
	return PositionConstraint_BASIS_UNSPECIFIED
}

func (x *PositionConstraint) GetValue() int32 {
	if x != nil {
		return x.Value
	}
	return 0
}

type BidOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Opt:
	//
	//	*BidOption_PositionConstraint
	Opt isBidOption_Opt `protobuf_oneof:"opt"`
}

func (x *BidOption) Reset() {
	*x = BidOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_providerapi_v1_providerapi_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidOption) ProtoMessage() {}

func (x *BidOption) ProtoReflect() protoreflect.Message {
	mi := &file_providerapi_v1_providerapi_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidOption.ProtoReflect.Descriptor instead.
func (*BidOption) Descriptor() ([]byte, []int) {
	return file_providerapi_v1_providerapi_proto_rawDescGZIP(), []int{6}
}

func (m *BidOption) GetOpt() isBidOption_Opt {
	if m != nil {
		return m.Opt
	}
	return nil
}

func (x *BidOption) GetPositionConstraint() *PositionConstraint {
	if x, ok := x.GetOpt().(*BidOption_PositionConstraint); ok {
		return x.PositionConstraint
	}
	return nil
}

type isBidOption_Opt interface {
	isBidOption_Opt()
}

type BidOption_PositionConstraint struct {
	PositionConstraint *PositionConstraint `protobuf:"bytes,1,opt,name=position_constraint,json=positionConstraint,proto3,oneof"`
}

func (*BidOption_PositionConstraint) isBidOption_Opt() {}

type BidOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Options []*BidOption `protobuf:"bytes,1,rep,name=options,proto3" json:"options,omitempty"`
}

func (x *BidOptions) Reset() {
	*x = BidOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_providerapi_v1_providerapi_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidOptions) ProtoMessage() {}

func (x *BidOptions) ProtoReflect() protoreflect.Message {
	mi := &file_providerapi_v1_providerapi_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidOptions.ProtoReflect.Descriptor instead.
func (*BidOptions) Descriptor() ([]byte, []int) {
	return file_providerapi_v1_providerapi_proto_rawDescGZIP(), []int{7}
}

func (x *BidOptions) GetOptions() []*BidOption {
	if x != nil {
		return x.Options
	}
	return nil
}

type Bid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TxHashes            []string    `protobuf:"bytes,1,rep,name=tx_hashes,json=txHashes,proto3" json:"tx_hashes,omitempty"`
	BidAmount           string      `protobuf:"bytes,2,opt,name=bid_amount,json=bidAmount,proto3" json:"bid_amount,omitempty"`
	BlockNumber         int64       `protobuf:"varint,3,opt,name=block_number,json=blockNumber,proto3" json:"block_number,omitempty"`
	BidDigest           []byte      `protobuf:"bytes,4,opt,name=bid_digest,json=bidDigest,proto3" json:"bid_digest,omitempty"`
	DecayStartTimestamp int64       `protobuf:"varint,5,opt,name=decay_start_timestamp,json=decayStartTimestamp,proto3" json:"decay_start_timestamp,omitempty"`
	DecayEndTimestamp   int64       `protobuf:"varint,6,opt,name=decay_end_timestamp,json=decayEndTimestamp,proto3" json:"decay_end_timestamp,omitempty"`
	RevertingTxHashes   []string    `protobuf:"bytes,7,rep,name=reverting_tx_hashes,json=revertingTxHashes,proto3" json:"reverting_tx_hashes,omitempty"`
	RawTransactions     []string    `protobuf:"bytes,8,rep,name=raw_transactions,json=rawTransactions,proto3" json:"raw_transactions,omitempty"`
	SlashAmount         string      `protobuf:"bytes,9,opt,name=slash_amount,json=slashAmount,proto3" json:"slash_amount,omitempty"`
	BidOptions          *BidOptions `protobuf:"bytes,10,opt,name=bid_options,json=bidOptions,proto3" json:"bid_options,omitempty"`
}

func (x *Bid) Reset() {
	*x = Bid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_providerapi_v1_providerapi_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Bid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Bid) ProtoMessage() {}

func (x *Bid) ProtoReflect() protoreflect.Message {
	mi := &file_providerapi_v1_providerapi_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Bid.ProtoReflect.Descriptor instead.
func (*Bid) Descriptor() ([]byte, []int) {
	return file_providerapi_v1_providerapi_proto_rawDescGZIP(), []int{8}
}

func (x *Bid) GetTxHashes() []string {
	if x != nil {
		return x.TxHashes
	}
	return nil
}

func (x *Bid) GetBidAmount() string {
	if x != nil {
		return x.BidAmount
	}
	return ""
}

func (x *Bid) GetBlockNumber() int64 {
	if x != nil {
		return x.BlockNumber
	}
	return 0
}

func (x *Bid) GetBidDigest() []byte {
	if x != nil {
		return x.BidDigest
	}
	return nil
}

func (x *Bid) GetDecayStartTimestamp() int64 {
	if x != nil {
		return x.DecayStartTimestamp
	}
	return 0
}

func (x *Bid) GetDecayEndTimestamp() int64 {
	if x != nil {
		return x.DecayEndTimestamp
	}
	return 0
}

func (x *Bid) GetRevertingTxHashes() []string {
	if x != nil {
		return x.RevertingTxHashes
	}
	return nil
}

func (x *Bid) GetRawTransactions() []string {
	if x != nil {
		return x.RawTransactions
	}
	return nil
}

func (x *Bid) GetSlashAmount() string {
	if x != nil {
		return x.SlashAmount
	}
	return ""
}

func (x *Bid) GetBidOptions() *BidOptions {
	if x != nil {
		return x.BidOptions
	}
	return nil
}

type BidResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BidDigest         []byte             `protobuf:"bytes,1,opt,name=bid_digest,json=bidDigest,proto3" json:"bid_digest,omitempty"`
	Status            BidResponse_Status `protobuf:"varint,2,opt,name=status,proto3,enum=providerapi.v1.BidResponse_Status" json:"status,omitempty"`
	DispatchTimestamp int64              `protobuf:"varint,3,opt,name=dispatch_timestamp,json=dispatchTimestamp,proto3" json:"dispatch_timestamp,omitempty"`
}

func (x *BidResponse) Reset() {
	*x = BidResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_providerapi_v1_providerapi_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse) ProtoMessage() {}

func (x *BidResponse) ProtoReflect() protoreflect.Message {
	mi := &file_providerapi_v1_providerapi_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse.ProtoReflect.Descriptor instead.
func (*BidResponse) Descriptor() ([]byte, []int) {
	return file_providerapi_v1_providerapi_proto_rawDescGZIP(), []int{9}
}

func (x *BidResponse) GetBidDigest() []byte {
	if x != nil {
		return x.BidDigest
	}
	return nil
}

func (x *BidResponse) GetStatus() BidResponse_Status {
	if x != nil {
		return x.Status
	}
	return BidResponse_STATUS_UNSPECIFIED
}

func (x *BidResponse) GetDispatchTimestamp() int64 {
	if x != nil {
		return x.DispatchTimestamp
	}
	return 0
}

type GetCommitmentInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BlockNumber int64 `protobuf:"varint,1,opt,name=block_number,json=blockNumber,proto3" json:"block_number,omitempty"`
	Page        int32 `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	Limit       int32 `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
}

func (x *GetCommitmentInfoRequest) Reset() {
	*x = GetCommitmentInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_providerapi_v1_providerapi_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCommitmentInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCommitmentInfoRequest) ProtoMessage() {}

func (x *GetCommitmentInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_providerapi_v1_providerapi_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCommitmentInfoRequest.ProtoReflect.Descriptor instead.
func (*GetCommitmentInfoRequest) Descriptor() ([]byte, []int) {
	return file_providerapi_v1_providerapi_proto_rawDescGZIP(), []int{10}
}

func (x *GetCommitmentInfoRequest) GetBlockNumber() int64 {
	if x != nil {
		return x.BlockNumber
	}
	return 0
}

func (x *GetCommitmentInfoRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetCommitmentInfoRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

type CommitmentInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Commitments []*CommitmentInfoResponse_BlockCommitments `protobuf:"bytes,1,rep,name=commitments,proto3" json:"commitments,omitempty"`
}

func (x *CommitmentInfoResponse) Reset() {
	*x = CommitmentInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_providerapi_v1_providerapi_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommitmentInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommitmentInfoResponse) ProtoMessage() {}

func (x *CommitmentInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_providerapi_v1_providerapi_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommitmentInfoResponse.ProtoReflect.Descriptor instead.
func (*CommitmentInfoResponse) Descriptor() ([]byte, []int) {
	return file_providerapi_v1_providerapi_proto_rawDescGZIP(), []int{11}
}

func (x *CommitmentInfoResponse) GetCommitments() []*CommitmentInfoResponse_BlockCommitments {
	if x != nil {
		return x.Commitments
	}
	return nil
}

type CommitmentInfoResponse_Commitment struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TxnHashes           []string    `protobuf:"bytes,1,rep,name=txn_hashes,json=txnHashes,proto3" json:"txn_hashes,omitempty"`
	RevertableTxnHashes []string    `protobuf:"bytes,2,rep,name=revertable_txn_hashes,json=revertableTxnHashes,proto3" json:"revertable_txn_hashes,omitempty"`
	Amount              string      `protobuf:"bytes,3,opt,name=amount,proto3" json:"amount,omitempty"`
	BlockNumber         int64       `protobuf:"varint,4,opt,name=block_number,json=blockNumber,proto3" json:"block_number,omitempty"`
	ProviderAddress     string      `protobuf:"bytes,5,opt,name=provider_address,json=providerAddress,proto3" json:"provider_address,omitempty"`
	DecayStartTimestamp int64       `protobuf:"varint,6,opt,name=decay_start_timestamp,json=decayStartTimestamp,proto3" json:"decay_start_timestamp,omitempty"`
	DecayEndTimestamp   int64       `protobuf:"varint,7,opt,name=decay_end_timestamp,json=decayEndTimestamp,proto3" json:"decay_end_timestamp,omitempty"`
	DispatchTimestamp   int64       `protobuf:"varint,8,opt,name=dispatch_timestamp,json=dispatchTimestamp,proto3" json:"dispatch_timestamp,omitempty"`
	SlashAmount         string      `protobuf:"bytes,9,opt,name=slash_amount,json=slashAmount,proto3" json:"slash_amount,omitempty"`
	Status              string      `protobuf:"bytes,10,opt,name=status,proto3" json:"status,omitempty"`
	Details             string      `protobuf:"bytes,11,opt,name=details,proto3" json:"details,omitempty"`
	Payment             string      `protobuf:"bytes,12,opt,name=payment,proto3" json:"payment,omitempty"`
	Refund              string      `protobuf:"bytes,13,opt,name=refund,proto3" json:"refund,omitempty"`
	BidOptions          *BidOptions `protobuf:"bytes,14,opt,name=bid_options,json=bidOptions,proto3" json:"bid_options,omitempty"`
}

func (x *CommitmentInfoResponse_Commitment) Reset() {
	*x = CommitmentInfoResponse_Commitment{}
	if protoimpl.UnsafeEnabled {
		mi := &file_providerapi_v1_providerapi_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommitmentInfoResponse_Commitment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommitmentInfoResponse_Commitment) ProtoMessage() {}

func (x *CommitmentInfoResponse_Commitment) ProtoReflect() protoreflect.Message {
	mi := &file_providerapi_v1_providerapi_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommitmentInfoResponse_Commitment.ProtoReflect.Descriptor instead.
func (*CommitmentInfoResponse_Commitment) Descriptor() ([]byte, []int) {
	return file_providerapi_v1_providerapi_proto_rawDescGZIP(), []int{11, 0}
}

func (x *CommitmentInfoResponse_Commitment) GetTxnHashes() []string {
	if x != nil {
		return x.TxnHashes
	}
	return nil
}

func (x *CommitmentInfoResponse_Commitment) GetRevertableTxnHashes() []string {
	if x != nil {
		return x.RevertableTxnHashes
	}
	return nil
}

func (x *CommitmentInfoResponse_Commitment) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

func (x *CommitmentInfoResponse_Commitment) GetBlockNumber() int64 {
	if x != nil {
		return x.BlockNumber
	}
	return 0
}

func (x *CommitmentInfoResponse_Commitment) GetProviderAddress() string {
	if x != nil {
		return x.ProviderAddress
	}
	return ""
}

func (x *CommitmentInfoResponse_Commitment) GetDecayStartTimestamp() int64 {
	if x != nil {
		return x.DecayStartTimestamp
	}
	return 0
}

func (x *CommitmentInfoResponse_Commitment) GetDecayEndTimestamp() int64 {
	if x != nil {
		return x.DecayEndTimestamp
	}
	return 0
}

func (x *CommitmentInfoResponse_Commitment) GetDispatchTimestamp() int64 {
	if x != nil {
		return x.DispatchTimestamp
	}
	return 0
}

func (x *CommitmentInfoResponse_Commitment) GetSlashAmount() string {
	if x != nil {
		return x.SlashAmount
	}
	return ""
}

func (x *CommitmentInfoResponse_Commitment) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *CommitmentInfoResponse_Commitment) GetDetails() string {
	if x != nil {
		return x.Details
	}
	return ""
}

func (x *CommitmentInfoResponse_Commitment) GetPayment() string {
	if x != nil {
		return x.Payment
	}
	return ""
}

func (x *CommitmentInfoResponse_Commitment) GetRefund() string {
	if x != nil {
		return x.Refund
	}
	return ""
}

func (x *CommitmentInfoResponse_Commitment) GetBidOptions() *BidOptions {
	if x != nil {
		return x.BidOptions
	}
	return nil
}

type CommitmentInfoResponse_BlockCommitments struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BlockNumber int64                                `protobuf:"varint,1,opt,name=block_number,json=blockNumber,proto3" json:"block_number,omitempty"`
	Commitments []*CommitmentInfoResponse_Commitment `protobuf:"bytes,2,rep,name=commitments,proto3" json:"commitments,omitempty"`
}

func (x *CommitmentInfoResponse_BlockCommitments) Reset() {
	*x = CommitmentInfoResponse_BlockCommitments{}
	if protoimpl.UnsafeEnabled {
		mi := &file_providerapi_v1_providerapi_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommitmentInfoResponse_BlockCommitments) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommitmentInfoResponse_BlockCommitments) ProtoMessage() {}

func (x *CommitmentInfoResponse_BlockCommitments) ProtoReflect() protoreflect.Message {
	mi := &file_providerapi_v1_providerapi_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommitmentInfoResponse_BlockCommitments.ProtoReflect.Descriptor instead.
func (*CommitmentInfoResponse_BlockCommitments) Descriptor() ([]byte, []int) {
	return file_providerapi_v1_providerapi_proto_rawDescGZIP(), []int{11, 1}
}

func (x *CommitmentInfoResponse_BlockCommitments) GetBlockNumber() int64 {
	if x != nil {
		return x.BlockNumber
	}
	return 0
}

func (x *CommitmentInfoResponse_BlockCommitments) GetCommitments() []*CommitmentInfoResponse_Commitment {
	if x != nil {
		return x.Commitments
	}
	return nil
}

var File_providerapi_v1_providerapi_proto protoreflect.FileDescriptor

var file_providerapi_v1_providerapi_proto_rawDesc = []byte{
	0x0a, 0x20, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31,
	0x2f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x2e,
	0x76, 0x31, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2d, 0x67, 0x65, 0x6e, 0x2d, 0x6f,
	0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x76, 0x32, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1b, 0x62, 0x75, 0x66, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc3, 0x07,
	0x0a, 0x0c, 0x53, 0x74, 0x61, 0x6b, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0xa5,
	0x01, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x8c, 0x01, 0x92, 0x41, 0x3b, 0x32, 0x30, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x20, 0x6f, 0x66,
	0x20, 0x45, 0x54, 0x48, 0x20, 0x74, 0x6f, 0x20, 0x73, 0x74, 0x61, 0x6b, 0x65, 0x20, 0x69, 0x6e,
	0x20, 0x74, 0x68, 0x65, 0x20, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x20, 0x72, 0x65,
	0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x2e, 0x8a, 0x01, 0x06, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x2b,
	0xba, 0x48, 0x4b, 0xba, 0x01, 0x48, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1f,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x20, 0x6d, 0x75, 0x73, 0x74, 0x20, 0x62, 0x65, 0x20, 0x61,
	0x20, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x20, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x65, 0x72, 0x2e, 0x1a,
	0x1d, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x28, 0x27, 0x5e,
	0x5b, 0x31, 0x2d, 0x39, 0x5d, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x2a, 0x24, 0x27, 0x29, 0x52, 0x06,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0xfe, 0x01, 0x0a, 0x0f, 0x62, 0x6c, 0x73, 0x5f, 0x70,
	0x75, 0x62, 0x6c, 0x69, 0x63, 0x5f, 0x6b, 0x65, 0x79, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09,
	0x42, 0xd5, 0x01, 0x92, 0x41, 0x3b, 0x32, 0x20, 0x42, 0x4c, 0x53, 0x20, 0x70, 0x75, 0x62, 0x6c,
	0x69, 0x63, 0x20, 0x6b, 0x65, 0x79, 0x73, 0x20, 0x6f, 0x66, 0x20, 0x74, 0x68, 0x65, 0x20, 0x70,
	0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x2e, 0x8a, 0x01, 0x16, 0x5e, 0x28, 0x30, 0x78, 0x29,
	0x3f, 0x5b, 0x61, 0x2d, 0x66, 0x41, 0x2d, 0x46, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x39, 0x36, 0x7d,
	0x24, 0xba, 0x48, 0x93, 0x01, 0xba, 0x01, 0x8f, 0x01, 0x0a, 0x0e, 0x62, 0x6c, 0x73, 0x5f, 0x70,
	0x75, 0x62, 0x6c, 0x69, 0x63, 0x5f, 0x6b, 0x65, 0x79, 0x12, 0x4b, 0x62, 0x6c, 0x73, 0x5f, 0x70,
	0x75, 0x62, 0x6c, 0x69, 0x63, 0x5f, 0x6b, 0x65, 0x79, 0x20, 0x6d, 0x75, 0x73, 0x74, 0x20, 0x62,
	0x65, 0x20, 0x61, 0x20, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x20, 0x34, 0x38, 0x2d, 0x62, 0x79, 0x74,
	0x65, 0x20, 0x68, 0x65, 0x78, 0x20, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x2c, 0x20, 0x77, 0x69,
	0x74, 0x68, 0x20, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x20, 0x30, 0x78, 0x20, 0x70,
	0x72, 0x65, 0x66, 0x69, 0x78, 0x2e, 0x1a, 0x30, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x61, 0x6c, 0x6c,
	0x28, 0x72, 0x2c, 0x20, 0x72, 0x2e, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x28, 0x27, 0x5e,
	0x28, 0x30, 0x78, 0x29, 0x3f, 0x5b, 0x61, 0x2d, 0x66, 0x41, 0x2d, 0x46, 0x30, 0x2d, 0x39, 0x5d,
	0x7b, 0x39, 0x36, 0x7d, 0x24, 0x27, 0x29, 0x29, 0x52, 0x0d, 0x62, 0x6c, 0x73, 0x50, 0x75, 0x62,
	0x6c, 0x69, 0x63, 0x4b, 0x65, 0x79, 0x73, 0x12, 0x93, 0x02, 0x0a, 0x0e, 0x62, 0x6c, 0x73, 0x5f,
	0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09,
	0x42, 0xeb, 0x01, 0x92, 0x41, 0x50, 0x32, 0x34, 0x42, 0x4c, 0x53, 0x20, 0x73, 0x69, 0x67, 0x6e,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x20, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x20, 0x74, 0x6f, 0x20, 0x74, 0x68, 0x65, 0x20, 0x42, 0x4c, 0x53, 0x20,
	0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x20, 0x6b, 0x65, 0x79, 0x73, 0x2e, 0x8a, 0x01, 0x17, 0x5e,
	0x28, 0x30, 0x78, 0x29, 0x3f, 0x5b, 0x61, 0x2d, 0x66, 0x41, 0x2d, 0x46, 0x30, 0x2d, 0x39, 0x5d,
	0x7b, 0x31, 0x39, 0x32, 0x7d, 0x24, 0xba, 0x48, 0x94, 0x01, 0xba, 0x01, 0x90, 0x01, 0x0a, 0x0e,
	0x62, 0x6c, 0x73, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x12, 0x4b,
	0x62, 0x6c, 0x73, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x20, 0x6d,
	0x75, 0x73, 0x74, 0x20, 0x62, 0x65, 0x20, 0x61, 0x20, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x20, 0x39,
	0x36, 0x2d, 0x62, 0x79, 0x74, 0x65, 0x20, 0x68, 0x65, 0x78, 0x20, 0x73, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x2c, 0x20, 0x77, 0x69, 0x74, 0x68, 0x20, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x20, 0x30, 0x78, 0x20, 0x70, 0x72, 0x65, 0x66, 0x69, 0x78, 0x2e, 0x1a, 0x31, 0x74, 0x68, 0x69,
	0x73, 0x2e, 0x61, 0x6c, 0x6c, 0x28, 0x72, 0x2c, 0x20, 0x72, 0x2e, 0x6d, 0x61, 0x74, 0x63, 0x68,
	0x65, 0x73, 0x28, 0x27, 0x5e, 0x28, 0x30, 0x78, 0x29, 0x3f, 0x5b, 0x61, 0x2d, 0x66, 0x41, 0x2d,
	0x46, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x39, 0x32, 0x7d, 0x24, 0x27, 0x29, 0x29, 0x52, 0x0d,
	0x62, 0x6c, 0x73, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x3a, 0xf3, 0x01,
	0x92, 0x41, 0xef, 0x01, 0x0a, 0x53, 0x2a, 0x0d, 0x53, 0x74, 0x61, 0x6b, 0x65, 0x20, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x32, 0x28, 0x53, 0x74, 0x61, 0x6b, 0x65, 0x20, 0x70, 0x72, 0x6f,
	0x76, 0x69, 0x64, 0x65, 0x72, 0x20, 0x69, 0x6e, 0x20, 0x74, 0x68, 0x65, 0x20, 0x70, 0x72, 0x6f,
	0x76, 0x69, 0x64, 0x65, 0x72, 0x20, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x2e, 0xd2,
	0x01, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0xd2, 0x01, 0x0e, 0x62, 0x6c, 0x73, 0x5f, 0x70,
	0x75, 0x62, 0x6c, 0x69, 0x63, 0x5f, 0x6b, 0x65, 0x79, 0x32, 0x97, 0x01, 0x7b, 0x22, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x22, 0x3a, 0x20, 0x22, 0x31, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x22, 0x2c, 0x20, 0x22, 0x62,
	0x6c, 0x73, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x5f, 0x6b, 0x65, 0x79, 0x22, 0x3a, 0x20,
	0x22, 0x38, 0x30, 0x30, 0x30, 0x30, 0x63, 0x64, 0x64, 0x65, 0x65, 0x63, 0x36, 0x36, 0x61, 0x38,
	0x30, 0x30, 0x65, 0x30, 0x30, 0x62, 0x30, 0x63, 0x63, 0x62, 0x62, 0x36, 0x32, 0x66, 0x31, 0x32,
	0x32, 0x39, 0x38, 0x30, 0x37, 0x33, 0x36, 0x30, 0x33, 0x66, 0x35, 0x32, 0x30, 0x39, 0x65, 0x38,
	0x31, 0x32, 0x61, 0x62, 0x62, 0x61, 0x63, 0x37, 0x65, 0x38, 0x37, 0x30, 0x34, 0x38, 0x32, 0x65,
	0x34, 0x38, 0x38, 0x64, 0x64, 0x31, 0x62, 0x62, 0x65, 0x35, 0x33, 0x33, 0x61, 0x39, 0x64, 0x34,
	0x34, 0x34, 0x39, 0x37, 0x62, 0x61, 0x38, 0x62, 0x37, 0x35, 0x36, 0x65, 0x31, 0x65, 0x38, 0x32,
	0x62, 0x22, 0x7d, 0x22, 0x9f, 0x03, 0x0a, 0x0d, 0x53, 0x74, 0x61, 0x6b, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x26, 0x0a,
	0x0f, 0x62, 0x6c, 0x73, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x5f, 0x6b, 0x65, 0x79, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x62, 0x6c, 0x73, 0x50, 0x75, 0x62, 0x6c, 0x69,
	0x63, 0x4b, 0x65, 0x79, 0x73, 0x3a, 0xcd, 0x02, 0x92, 0x41, 0xc9, 0x02, 0x0a, 0x4a, 0x2a, 0x0e,
	0x53, 0x74, 0x61, 0x6b, 0x65, 0x20, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x32, 0x38,
	0x47, 0x65, 0x74, 0x20, 0x73, 0x74, 0x61, 0x6b, 0x65, 0x64, 0x20, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x20, 0x66, 0x6f, 0x72, 0x20, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x20, 0x69,
	0x6e, 0x20, 0x74, 0x68, 0x65, 0x20, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x20, 0x72,
	0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x2e, 0x32, 0xfa, 0x01, 0x7b, 0x22, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x22, 0x3a, 0x20, 0x22, 0x32, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x22, 0x2c, 0x20, 0x22, 0x62, 0x6c,
	0x73, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x5f, 0x6b, 0x65, 0x79, 0x73, 0x22, 0x3a, 0x20,
	0x5b, 0x22, 0x39, 0x30, 0x30, 0x30, 0x30, 0x63, 0x64, 0x64, 0x65, 0x65, 0x63, 0x36, 0x36, 0x61,
	0x38, 0x30, 0x65, 0x30, 0x30, 0x62, 0x30, 0x63, 0x63, 0x62, 0x62, 0x36, 0x32, 0x66, 0x31, 0x32,
	0x32, 0x39, 0x38, 0x30, 0x37, 0x33, 0x36, 0x30, 0x33, 0x66, 0x35, 0x32, 0x30, 0x39, 0x65, 0x38,
	0x31, 0x32, 0x61, 0x62, 0x62, 0x61, 0x63, 0x37, 0x65, 0x38, 0x37, 0x30, 0x34, 0x38, 0x32, 0x65,
	0x34, 0x38, 0x38, 0x64, 0x64, 0x31, 0x62, 0x62, 0x65, 0x35, 0x33, 0x33, 0x61, 0x39, 0x64, 0x34,
	0x34, 0x39, 0x37, 0x62, 0x61, 0x38, 0x62, 0x37, 0x35, 0x36, 0x65, 0x31, 0x65, 0x38, 0x32, 0x62,
	0x22, 0x2c, 0x20, 0x22, 0x38, 0x30, 0x30, 0x30, 0x30, 0x63, 0x64, 0x64, 0x65, 0x65, 0x63, 0x36,
	0x36, 0x61, 0x38, 0x30, 0x65, 0x30, 0x30, 0x62, 0x30, 0x63, 0x63, 0x62, 0x62, 0x36, 0x32, 0x66,
	0x31, 0x32, 0x32, 0x39, 0x38, 0x30, 0x37, 0x33, 0x36, 0x30, 0x33, 0x66, 0x35, 0x32, 0x30, 0x39,
	0x65, 0x38, 0x31, 0x32, 0x61, 0x62, 0x62, 0x61, 0x63, 0x37, 0x65, 0x38, 0x37, 0x30, 0x34, 0x38,
	0x32, 0x65, 0x34, 0x38, 0x38, 0x64, 0x64, 0x31, 0x62, 0x62, 0x65, 0x35, 0x33, 0x33, 0x61, 0x39,
	0x64, 0x34, 0x34, 0x39, 0x37, 0x62, 0x61, 0x38, 0x62, 0x37, 0x35, 0x36, 0x65, 0x31, 0x65, 0x38,
	0x32, 0x62, 0x22, 0x5d, 0x7d, 0x22, 0xa5, 0x01, 0x0a, 0x12, 0x57, 0x69, 0x74, 0x68, 0x64, 0x72,
	0x61, 0x77, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x3a, 0x77, 0x92, 0x41, 0x74, 0x0a, 0x4f, 0x2a, 0x13, 0x57, 0x69, 0x74,
	0x68, 0x64, 0x72, 0x61, 0x77, 0x61, 0x6c, 0x20, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x32, 0x38, 0x57, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x61, 0x6c, 0x20, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x20, 0x66, 0x6f, 0x72, 0x20, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72,
	0x20, 0x69, 0x6e, 0x20, 0x74, 0x68, 0x65, 0x20, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72,
	0x20, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x2e, 0x32, 0x21, 0x7b, 0x22, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x22, 0x3a, 0x20, 0x22, 0x31, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x22, 0x7d, 0x22, 0x9e, 0x01,
	0x0a, 0x0e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x3a, 0x74, 0x92, 0x41, 0x71, 0x0a, 0x4d, 0x2a,
	0x0f, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x20, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x32, 0x3a, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x20, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x20, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x20, 0x66, 0x6f, 0x72, 0x20, 0x70, 0x72, 0x6f, 0x76,
	0x69, 0x64, 0x65, 0x72, 0x20, 0x69, 0x6e, 0x20, 0x74, 0x68, 0x65, 0x20, 0x62, 0x69, 0x64, 0x64,
	0x65, 0x72, 0x20, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x2e, 0x32, 0x20, 0x7b, 0x22,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x3a, 0x20, 0x22, 0x35, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x22, 0x7d, 0x22, 0x0e,
	0x0a, 0x0c, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0xc5,
	0x05, 0x0a, 0x12, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x73, 0x74,
	0x72, 0x61, 0x69, 0x6e, 0x74, 0x12, 0x78, 0x0a, 0x06, 0x61, 0x6e, 0x63, 0x68, 0x6f, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x43,
	0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x2e, 0x41, 0x6e, 0x63, 0x68, 0x6f, 0x72,
	0x42, 0x35, 0x92, 0x41, 0x32, 0x32, 0x30, 0x41, 0x6e, 0x63, 0x68, 0x6f, 0x72, 0x20, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x6f, 0x66, 0x20, 0x74, 0x68, 0x65, 0x20, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x69, 0x6e, 0x20, 0x74, 0x68, 0x65,
	0x20, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x2e, 0x52, 0x06, 0x61, 0x6e, 0x63, 0x68, 0x6f, 0x72, 0x12,
	0x67, 0x0a, 0x05, 0x62, 0x61, 0x73, 0x69, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28,
	0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e,
	0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69,
	0x6e, 0x74, 0x2e, 0x42, 0x61, 0x73, 0x69, 0x73, 0x42, 0x27, 0x92, 0x41, 0x24, 0x32, 0x22, 0x42,
	0x61, 0x73, 0x69, 0x73, 0x20, 0x66, 0x6f, 0x72, 0x20, 0x74, 0x68, 0x65, 0x20, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74,
	0x2e, 0x52, 0x05, 0x62, 0x61, 0x73, 0x69, 0x73, 0x12, 0xc9, 0x01, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0xb2, 0x01, 0x92, 0x41, 0xae, 0x01, 0x32,
	0xab, 0x01, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x20, 0x6f, 0x66, 0x20, 0x74, 0x68, 0x65, 0x20, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69,
	0x6e, 0x74, 0x2e, 0x20, 0x49, 0x66, 0x20, 0x61, 0x6e, 0x63, 0x68, 0x6f, 0x72, 0x20, 0x69, 0x73,
	0x20, 0x54, 0x4f, 0x50, 0x2c, 0x20, 0x74, 0x68, 0x69, 0x73, 0x20, 0x69, 0x73, 0x20, 0x74, 0x68,
	0x65, 0x20, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x66, 0x72, 0x6f, 0x6d, 0x20,
	0x74, 0x68, 0x65, 0x20, 0x74, 0x6f, 0x70, 0x20, 0x6f, 0x66, 0x20, 0x74, 0x68, 0x65, 0x20, 0x62,
	0x6c, 0x6f, 0x63, 0x6b, 0x2e, 0x20, 0x49, 0x66, 0x20, 0x61, 0x6e, 0x63, 0x68, 0x6f, 0x72, 0x20,
	0x69, 0x73, 0x20, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x2c, 0x20, 0x74, 0x68, 0x69, 0x73, 0x20,
	0x69, 0x73, 0x20, 0x74, 0x68, 0x65, 0x20, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x20,
	0x66, 0x72, 0x6f, 0x6d, 0x20, 0x74, 0x68, 0x65, 0x20, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x20,
	0x6f, 0x66, 0x20, 0x74, 0x68, 0x65, 0x20, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x2e, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x22, 0x43, 0x0a, 0x06, 0x41, 0x6e, 0x63, 0x68, 0x6f, 0x72, 0x12, 0x16,
	0x0a, 0x12, 0x41, 0x4e, 0x43, 0x48, 0x4f, 0x52, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x41, 0x4e, 0x43, 0x48, 0x4f, 0x52,
	0x5f, 0x54, 0x4f, 0x50, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x41, 0x4e, 0x43, 0x48, 0x4f, 0x52,
	0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x10, 0x02, 0x22, 0x62, 0x0a, 0x05, 0x42, 0x61, 0x73,
	0x69, 0x73, 0x12, 0x15, 0x0a, 0x11, 0x42, 0x41, 0x53, 0x49, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x42, 0x41, 0x53,
	0x49, 0x53, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x49, 0x4c, 0x45, 0x10, 0x01, 0x12,
	0x12, 0x0a, 0x0e, 0x42, 0x41, 0x53, 0x49, 0x53, 0x5f, 0x41, 0x42, 0x53, 0x4f, 0x4c, 0x55, 0x54,
	0x45, 0x10, 0x02, 0x12, 0x18, 0x0a, 0x14, 0x42, 0x41, 0x53, 0x49, 0x53, 0x5f, 0x47, 0x41, 0x53,
	0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x49, 0x4c, 0x45, 0x10, 0x03, 0x3a, 0x57, 0x92,
	0x41, 0x54, 0x0a, 0x52, 0x2a, 0x13, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x43,
	0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x32, 0x3b, 0x43, 0x6f, 0x6e, 0x73, 0x74,
	0x72, 0x61, 0x69, 0x6e, 0x74, 0x20, 0x6f, 0x6e, 0x20, 0x74, 0x68, 0x65, 0x20, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x6f, 0x66, 0x20, 0x74, 0x68, 0x65, 0x20, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x69, 0x6e, 0x20, 0x74, 0x68, 0x65, 0x20,
	0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x2e, 0x22, 0xa6, 0x01, 0x0a, 0x09, 0x42, 0x69, 0x64, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x91, 0x01, 0x0a, 0x13, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x73,
	0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x42, 0x3a, 0x92, 0x41, 0x37, 0x32, 0x35, 0x50, 0x6f, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74,
	0x20, 0x66, 0x6f, 0x72, 0x20, 0x74, 0x68, 0x65, 0x20, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x20, 0x69, 0x6e, 0x20, 0x74, 0x68, 0x65, 0x20, 0x62, 0x6c, 0x6f, 0x63,
	0x6b, 0x2e, 0x48, 0x00, 0x52, 0x12, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f,
	0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x42, 0x05, 0x0a, 0x03, 0x6f, 0x70, 0x74, 0x22,
	0xdd, 0x01, 0x0a, 0x0a, 0x42, 0x69, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0xa4,
	0x01, 0x0a, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x2e, 0x76,
	0x31, 0x2e, 0x42, 0x69, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x6f, 0x92, 0x41, 0x16,
	0x32, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x20, 0x6f, 0x66, 0x20, 0x62, 0x69, 0x64, 0x20, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0xba, 0x48, 0x53, 0xba, 0x01, 0x50, 0x0a, 0x07, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x2d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x20, 0x6d,
	0x75, 0x73, 0x74, 0x20, 0x62, 0x65, 0x20, 0x61, 0x20, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x20, 0x61,
	0x72, 0x72, 0x61, 0x79, 0x20, 0x6f, 0x66, 0x20, 0x62, 0x69, 0x64, 0x20, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x1a, 0x16, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x61, 0x6c, 0x6c, 0x28, 0x72,
	0x2c, 0x20, 0x72, 0x20, 0x21, 0x3d, 0x20, 0x6e, 0x75, 0x6c, 0x6c, 0x29, 0x52, 0x07, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x3a, 0x28, 0x92, 0x41, 0x25, 0x0a, 0x23, 0x2a, 0x0b, 0x42, 0x69,
	0x64, 0x20, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x32, 0x14, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x20, 0x66, 0x6f, 0x72, 0x20, 0x74, 0x68, 0x65, 0x20, 0x62, 0x69, 0x64, 0x2e, 0x22,
	0x8e, 0x15, 0x0a, 0x03, 0x42, 0x69, 0x64, 0x12, 0x8f, 0x02, 0x0a, 0x09, 0x74, 0x78, 0x5f, 0x68,
	0x61, 0x73, 0x68, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0xf1, 0x01, 0x92, 0x41,
	0x78, 0x32, 0x64, 0x48, 0x65, 0x78, 0x20, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x20, 0x65, 0x6e,
	0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x20, 0x6f, 0x66, 0x20, 0x74, 0x68, 0x65, 0x20, 0x68, 0x61,
	0x73, 0x68, 0x65, 0x73, 0x20, 0x6f, 0x66, 0x20, 0x74, 0x68, 0x65, 0x20, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x20, 0x74, 0x68, 0x61, 0x74, 0x20, 0x74, 0x68,
	0x65, 0x20, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x20, 0x77, 0x61, 0x6e, 0x74, 0x73, 0x20, 0x74,
	0x6f, 0x20, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x20, 0x69, 0x6e, 0x20, 0x74, 0x68, 0x65,
	0x20, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x2e, 0x8a, 0x01, 0x0f, 0x5b, 0x61, 0x2d, 0x66, 0x41, 0x2d,
	0x46, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x36, 0x34, 0x7d, 0xba, 0x48, 0x73, 0xba, 0x01, 0x70, 0x0a,
	0x09, 0x74, 0x78, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x65, 0x73, 0x12, 0x36, 0x74, 0x78, 0x5f, 0x68,
	0x61, 0x73, 0x68, 0x65, 0x73, 0x20, 0x6d, 0x75, 0x73, 0x74, 0x20, 0x62, 0x65, 0x20, 0x61, 0x20,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x20, 0x61, 0x72, 0x72, 0x61, 0x79, 0x20, 0x6f, 0x66, 0x20, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x68, 0x61, 0x73, 0x68, 0x65,
	0x73, 0x2e, 0x1a, 0x2b, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x61, 0x6c, 0x6c, 0x28, 0x72, 0x2c, 0x20,
	0x72, 0x2e, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x28, 0x27, 0x5e, 0x5b, 0x61, 0x2d, 0x66,
	0x41, 0x2d, 0x46, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x36, 0x34, 0x7d, 0x24, 0x27, 0x29, 0x29, 0x52,
	0x08, 0x74, 0x78, 0x48, 0x61, 0x73, 0x68, 0x65, 0x73, 0x12, 0xef, 0x01, 0x0a, 0x0a, 0x62, 0x69,
	0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0xcf,
	0x01, 0x92, 0x41, 0x76, 0x32, 0x6b, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x20, 0x6f, 0x66, 0x20,
	0x45, 0x54, 0x48, 0x20, 0x74, 0x68, 0x61, 0x74, 0x20, 0x74, 0x68, 0x65, 0x20, 0x62, 0x69, 0x64,
	0x64, 0x65, 0x72, 0x20, 0x69, 0x73, 0x20, 0x77, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x20, 0x74,
	0x6f, 0x20, 0x70, 0x61, 0x79, 0x20, 0x74, 0x6f, 0x20, 0x74, 0x68, 0x65, 0x20, 0x70, 0x72, 0x6f,
	0x76, 0x69, 0x64, 0x65, 0x72, 0x20, 0x66, 0x6f, 0x72, 0x20, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64,
	0x69, 0x6e, 0x67, 0x20, 0x74, 0x68, 0x65, 0x20, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x20, 0x69, 0x6e, 0x20, 0x74, 0x68, 0x65, 0x20, 0x62, 0x6c, 0x6f, 0x63, 0x6b,
	0x2e, 0x8a, 0x01, 0x06, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x2b, 0xba, 0x48, 0x53, 0xba, 0x01, 0x50,
	0x0a, 0x0a, 0x62, 0x69, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x23, 0x62, 0x69,
	0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x20, 0x6d, 0x75, 0x73, 0x74, 0x20, 0x62, 0x65,
	0x20, 0x61, 0x20, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x20, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x65, 0x72,
	0x2e, 0x1a, 0x1d, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x28,
	0x27, 0x5e, 0x5b, 0x31, 0x2d, 0x39, 0x5d, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x2a, 0x24, 0x27, 0x29,
	0x52, 0x09, 0x62, 0x69, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x74, 0x0a, 0x0c, 0x62,
	0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x51, 0x92, 0x41, 0x47, 0x32, 0x45, 0x4d, 0x61, 0x78, 0x20, 0x62, 0x6c, 0x6f, 0x63,
	0x6b, 0x20, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x20, 0x74, 0x68, 0x61, 0x74, 0x20, 0x74, 0x68,
	0x65, 0x20, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x20, 0x77, 0x61, 0x6e, 0x74, 0x73, 0x20, 0x74,
	0x6f, 0x20, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x20, 0x74, 0x68, 0x65, 0x20, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x69, 0x6e, 0x2e, 0xba, 0x48, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x12, 0x5c, 0x0a, 0x0a, 0x62, 0x69, 0x64, 0x5f, 0x64, 0x69, 0x67, 0x65, 0x73, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0c, 0x42, 0x3d, 0x92, 0x41, 0x31, 0x32, 0x2f, 0x44, 0x69, 0x67, 0x65,
	0x73, 0x74, 0x20, 0x6f, 0x66, 0x20, 0x74, 0x68, 0x65, 0x20, 0x62, 0x69, 0x64, 0x20, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x20, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x20, 0x62, 0x79, 0x20,
	0x74, 0x68, 0x65, 0x20, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x2e, 0xba, 0x48, 0x06, 0x7a, 0x04,
	0x10, 0x01, 0x18, 0x40, 0x52, 0x09, 0x62, 0x69, 0x64, 0x44, 0x69, 0x67, 0x65, 0x73, 0x74, 0x12,
	0xc2, 0x01, 0x0a, 0x15, 0x64, 0x65, 0x63, 0x61, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x8d, 0x01, 0x92, 0x41, 0x2d, 0x32, 0x2b, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x20, 0x61, 0x74, 0x20, 0x77, 0x68, 0x69, 0x63, 0x68, 0x20, 0x74, 0x68, 0x65, 0x20, 0x62, 0x69,
	0x64, 0x20, 0x73, 0x74, 0x61, 0x72, 0x74, 0x73, 0x20, 0x64, 0x65, 0x63, 0x61, 0x79, 0x69, 0x6e,
	0x67, 0x2e, 0xba, 0x48, 0x5a, 0xba, 0x01, 0x57, 0x0a, 0x15, 0x64, 0x65, 0x63, 0x61, 0x79, 0x5f,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12,
	0x2e, 0x64, 0x65, 0x63, 0x61, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x20, 0x6d, 0x75, 0x73, 0x74, 0x20, 0x62, 0x65, 0x20, 0x61,
	0x20, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x20, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x65, 0x72, 0x2e, 0x1a,
	0x0e, 0x75, 0x69, 0x6e, 0x74, 0x28, 0x74, 0x68, 0x69, 0x73, 0x29, 0x20, 0x3e, 0x20, 0x30, 0x52,
	0x13, 0x64, 0x65, 0x63, 0x61, 0x79, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x12, 0xb8, 0x01, 0x0a, 0x13, 0x64, 0x65, 0x63, 0x61, 0x79, 0x5f, 0x65,
	0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x87, 0x01, 0x92, 0x41, 0x2b, 0x32, 0x29, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x20, 0x61, 0x74, 0x20, 0x77, 0x68, 0x69, 0x63, 0x68, 0x20, 0x74, 0x68, 0x65,
	0x20, 0x62, 0x69, 0x64, 0x20, 0x65, 0x6e, 0x64, 0x73, 0x20, 0x64, 0x65, 0x63, 0x61, 0x79, 0x69,
	0x6e, 0x67, 0x2e, 0xba, 0x48, 0x56, 0xba, 0x01, 0x53, 0x0a, 0x13, 0x64, 0x65, 0x63, 0x61, 0x79,
	0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x2c,
	0x64, 0x65, 0x63, 0x61, 0x79, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x20, 0x6d, 0x75, 0x73, 0x74, 0x20, 0x62, 0x65, 0x20, 0x61, 0x20, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x20, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x65, 0x72, 0x2e, 0x1a, 0x0e, 0x75, 0x69,
	0x6e, 0x74, 0x28, 0x74, 0x68, 0x69, 0x73, 0x29, 0x20, 0x3e, 0x20, 0x30, 0x52, 0x11, 0x64, 0x65,
	0x63, 0x61, 0x79, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12,
	0x8a, 0x02, 0x0a, 0x13, 0x72, 0x65, 0x76, 0x65, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x78,
	0x5f, 0x68, 0x61, 0x73, 0x68, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x42, 0xd9, 0x01,
	0x92, 0x41, 0x49, 0x32, 0x47, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x20, 0x61, 0x72,
	0x72, 0x61, 0x79, 0x20, 0x6f, 0x66, 0x20, 0x74, 0x78, 0x20, 0x68, 0x61, 0x73, 0x68, 0x65, 0x73,
	0x20, 0x74, 0x68, 0x61, 0x74, 0x20, 0x61, 0x72, 0x65, 0x20, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65,
	0x64, 0x20, 0x74, 0x6f, 0x20, 0x72, 0x65, 0x76, 0x65, 0x72, 0x74, 0x20, 0x6f, 0x72, 0x20, 0x62,
	0x65, 0x20, 0x64, 0x69, 0x73, 0x63, 0x61, 0x72, 0x64, 0x65, 0x64, 0x2e, 0xba, 0x48, 0x89, 0x01,
	0xba, 0x01, 0x85, 0x01, 0x0a, 0x13, 0x72, 0x65, 0x76, 0x65, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x5f,
	0x74, 0x78, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x65, 0x73, 0x12, 0x41, 0x72, 0x65, 0x76, 0x65, 0x72,
	0x74, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x78, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x65, 0x73, 0x20, 0x6d,
	0x75, 0x73, 0x74, 0x20, 0x62, 0x65, 0x20, 0x61, 0x6e, 0x20, 0x61, 0x72, 0x72, 0x61, 0x79, 0x20,
	0x6f, 0x66, 0x20, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x20, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x20, 0x68, 0x61, 0x73, 0x68, 0x65, 0x73, 0x2e, 0x1a, 0x2b, 0x74, 0x68,
	0x69, 0x73, 0x2e, 0x61, 0x6c, 0x6c, 0x28, 0x72, 0x2c, 0x20, 0x72, 0x2e, 0x6d, 0x61, 0x74, 0x63,
	0x68, 0x65, 0x73, 0x28, 0x27, 0x5e, 0x5b, 0x61, 0x2d, 0x66, 0x41, 0x2d, 0x46, 0x30, 0x2d, 0x39,
	0x5d, 0x7b, 0x36, 0x34, 0x7d, 0x24, 0x27, 0x29, 0x29, 0x52, 0x11, 0x72, 0x65, 0x76, 0x65, 0x72,
	0x74, 0x69, 0x6e, 0x67, 0x54, 0x78, 0x48, 0x61, 0x73, 0x68, 0x65, 0x73, 0x12, 0x9d, 0x02, 0x0a,
	0x10, 0x72, 0x61, 0x77, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x42, 0xf1, 0x01, 0x92, 0x41, 0x6e, 0x32, 0x6c, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x20, 0x61, 0x72, 0x72, 0x61, 0x79, 0x20, 0x6f, 0x66,
	0x20, 0x52, 0x4c, 0x50, 0x20, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x64, 0x20, 0x72, 0x61, 0x77,
	0x20, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x20, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x20, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x73, 0x20, 0x74, 0x68, 0x61,
	0x74, 0x20, 0x74, 0x68, 0x65, 0x20, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x20, 0x77, 0x61, 0x6e,
	0x74, 0x73, 0x20, 0x74, 0x6f, 0x20, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x20, 0x69, 0x6e,
	0x20, 0x74, 0x68, 0x65, 0x20, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x2e, 0xba, 0x48, 0x7d, 0xba, 0x01,
	0x7a, 0x0a, 0x10, 0x72, 0x61, 0x77, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x3c, 0x72, 0x61, 0x77, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x20, 0x6d, 0x75, 0x73, 0x74, 0x20, 0x62, 0x65, 0x20, 0x61, 0x6e,
	0x20, 0x61, 0x72, 0x72, 0x61, 0x79, 0x20, 0x6f, 0x66, 0x20, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x20,
	0x72, 0x61, 0x77, 0x20, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x1a, 0x28, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x61, 0x6c, 0x6c, 0x28, 0x72, 0x2c, 0x20, 0x72,
	0x2e, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x28, 0x27, 0x5e, 0x5b, 0x61, 0x2d, 0x66, 0x41,
	0x2d, 0x46, 0x30, 0x2d, 0x39, 0x5d, 0x2b, 0x24, 0x27, 0x29, 0x29, 0x52, 0x0f, 0x72, 0x61, 0x77,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0xbf, 0x02, 0x0a,
	0x0c, 0x73, 0x6c, 0x61, 0x73, 0x68, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x9b, 0x02, 0x92, 0x41, 0x9f, 0x01, 0x32, 0x93, 0x01, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x20, 0x6f, 0x66, 0x20, 0x45, 0x54, 0x48, 0x20, 0x74, 0x68, 0x61, 0x74, 0x20,
	0x77, 0x69, 0x6c, 0x6c, 0x20, 0x62, 0x65, 0x20, 0x73, 0x6c, 0x61, 0x73, 0x68, 0x65, 0x64, 0x20,
	0x66, 0x72, 0x6f, 0x6d, 0x20, 0x74, 0x68, 0x65, 0x20, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65,
	0x72, 0x20, 0x69, 0x66, 0x20, 0x74, 0x68, 0x65, 0x79, 0x20, 0x66, 0x61, 0x69, 0x6c, 0x20, 0x74,
	0x6f, 0x20, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x20, 0x74, 0x68, 0x65, 0x20, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x20, 0x49, 0x66, 0x20, 0x7a, 0x65,
	0x72, 0x6f, 0x2c, 0x20, 0x74, 0x68, 0x65, 0x20, 0x64, 0x65, 0x63, 0x61, 0x79, 0x65, 0x64, 0x20,
	0x62, 0x69, 0x64, 0x20, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x20, 0x69, 0x73, 0x20, 0x75, 0x73,
	0x65, 0x64, 0x20, 0x66, 0x6f, 0x72, 0x20, 0x73, 0x6c, 0x61, 0x73, 0x68, 0x69, 0x6e, 0x67, 0x2e,
	0x8a, 0x01, 0x06, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x2b, 0xba, 0x48, 0x75, 0xba, 0x01, 0x72, 0x0a,
	0x0c, 0x73, 0x6c, 0x61, 0x73, 0x68, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x25, 0x73,
	0x6c, 0x61, 0x73, 0x68, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x20, 0x6d, 0x75, 0x73, 0x74,
	0x20, 0x62, 0x65, 0x20, 0x61, 0x20, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x20, 0x69, 0x6e, 0x74, 0x65,
	0x67, 0x65, 0x72, 0x2e, 0x1a, 0x3b, 0x74, 0x68, 0x69, 0x73, 0x20, 0x3d, 0x3d, 0x20, 0x27, 0x27,
	0x20, 0x7c, 0x7c, 0x20, 0x28, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65,
	0x73, 0x28, 0x27, 0x5e, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x2b, 0x24, 0x27, 0x29, 0x20, 0x26, 0x26,
	0x20, 0x75, 0x69, 0x6e, 0x74, 0x28, 0x74, 0x68, 0x69, 0x73, 0x29, 0x20, 0x3e, 0x3d, 0x20, 0x30,
	0x29, 0x52, 0x0b, 0x73, 0x6c, 0x61, 0x73, 0x68, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x6b,
	0x0a, 0x0b, 0x62, 0x69, 0x64, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x69, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x42,
	0x2e, 0x92, 0x41, 0x2b, 0x32, 0x29, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x20, 0x62,
	0x69, 0x64, 0x20, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x20, 0x66, 0x6f, 0x72, 0x20, 0x74,
	0x68, 0x65, 0x20, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x52,
	0x0a, 0x62, 0x69, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x3a, 0xd2, 0x04, 0x92, 0x41,
	0xce, 0x04, 0x0a, 0x70, 0x2a, 0x0b, 0x42, 0x69, 0x64, 0x20, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x32, 0x30, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x20, 0x62, 0x69, 0x64, 0x20, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x20, 0x66, 0x72, 0x6f, 0x6d, 0x20, 0x62, 0x69, 0x64, 0x64, 0x65,
	0x72, 0x73, 0x20, 0x74, 0x6f, 0x20, 0x74, 0x68, 0x65, 0x20, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64,
	0x65, 0x72, 0x2e, 0xd2, 0x01, 0x08, 0x74, 0x78, 0x48, 0x61, 0x73, 0x68, 0x65, 0x73, 0xd2, 0x01,
	0x09, 0x62, 0x69, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0xd2, 0x01, 0x0b, 0x62, 0x6c, 0x6f,
	0x63, 0x6b, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0xd2, 0x01, 0x09, 0x62, 0x69, 0x64, 0x44, 0x69,
	0x67, 0x65, 0x73, 0x74, 0x32, 0xd9, 0x03, 0x7b, 0x22, 0x74, 0x78, 0x48, 0x61, 0x73, 0x68, 0x65,
	0x73, 0x22, 0x3a, 0x20, 0x5b, 0x22, 0x66, 0x65, 0x34, 0x63, 0x62, 0x34, 0x37, 0x64, 0x62, 0x33,
	0x36, 0x33, 0x30, 0x35, 0x35, 0x31, 0x62, 0x65, 0x65, 0x64, 0x66, 0x62, 0x64, 0x30, 0x32, 0x61,
	0x37, 0x31, 0x65, 0x63, 0x63, 0x36, 0x39, 0x66, 0x64, 0x35, 0x39, 0x37, 0x35, 0x38, 0x65, 0x32,
	0x62, 0x61, 0x36, 0x39, 0x39, 0x36, 0x30, 0x36, 0x65, 0x32, 0x64, 0x35, 0x63, 0x37, 0x34, 0x32,
	0x38, 0x34, 0x66, 0x66, 0x61, 0x37, 0x22, 0x2c, 0x20, 0x22, 0x37, 0x31, 0x63, 0x31, 0x33, 0x34,
	0x38, 0x66, 0x32, 0x64, 0x37, 0x66, 0x66, 0x37, 0x65, 0x38, 0x31, 0x34, 0x66, 0x39, 0x63, 0x33,
	0x36, 0x31, 0x37, 0x39, 0x38, 0x33, 0x37, 0x30, 0x33, 0x34, 0x33, 0x35, 0x65, 0x61, 0x37, 0x34,
	0x34, 0x36, 0x64, 0x65, 0x34, 0x32, 0x30, 0x61, 0x65, 0x61, 0x63, 0x34, 0x38, 0x38, 0x62, 0x66,
	0x31, 0x64, 0x65, 0x33, 0x35, 0x37, 0x33, 0x37, 0x65, 0x38, 0x22, 0x5d, 0x2c, 0x20, 0x22, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x3a, 0x20, 0x22, 0x31, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x22, 0x2c, 0x20, 0x22,
	0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x3a, 0x20, 0x31, 0x32,
	0x33, 0x34, 0x35, 0x36, 0x2c, 0x20, 0x22, 0x62, 0x69, 0x64, 0x44, 0x69, 0x67, 0x65, 0x73, 0x74,
	0x22, 0x3a, 0x20, 0x22, 0x39, 0x64, 0x4a, 0x69, 0x6e, 0x77, 0x4c, 0x2b, 0x46, 0x5a, 0x36, 0x42,
	0x31, 0x78, 0x73, 0x49, 0x51, 0x51, 0x6f, 0x38, 0x74, 0x38, 0x42, 0x30, 0x5a, 0x58, 0x4a, 0x75,
	0x62, 0x4a, 0x77, 0x59, 0x38, 0x36, 0x6c, 0x2f, 0x59, 0x75, 0x37, 0x79, 0x41, 0x48, 0x31, 0x35,
	0x39, 0x51, 0x72, 0x50, 0x48, 0x55, 0x30, 0x71, 0x6a, 0x32, 0x50, 0x2b, 0x59, 0x46, 0x6a, 0x2b,
	0x6c, 0x6c, 0x62, 0x75, 0x49, 0x31, 0x5a, 0x79, 0x67, 0x64, 0x78, 0x47, 0x73, 0x58, 0x38, 0x2b,
	0x50, 0x33, 0x62, 0x79, 0x4d, 0x45, 0x41, 0x35, 0x69, 0x67, 0x3d, 0x3d, 0x22, 0x2c, 0x20, 0x22,
	0x64, 0x65, 0x63, 0x61, 0x79, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x22, 0x3a, 0x31, 0x37, 0x32, 0x35, 0x33, 0x36, 0x35, 0x33, 0x30, 0x31, 0x30,
	0x30, 0x30, 0x2c, 0x20, 0x22, 0x64, 0x65, 0x63, 0x61, 0x79, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x3a, 0x31, 0x37, 0x32, 0x35, 0x33, 0x36, 0x35, 0x33,
	0x30, 0x32, 0x30, 0x30, 0x30, 0x2c, 0x20, 0x22, 0x72, 0x65, 0x76, 0x65, 0x72, 0x74, 0x69, 0x6e,
	0x67, 0x54, 0x78, 0x48, 0x61, 0x73, 0x68, 0x65, 0x73, 0x22, 0x3a, 0x5b, 0x22, 0x66, 0x65, 0x34,
	0x63, 0x62, 0x34, 0x37, 0x64, 0x62, 0x33, 0x36, 0x33, 0x30, 0x35, 0x35, 0x31, 0x62, 0x65, 0x65,
	0x64, 0x66, 0x62, 0x64, 0x30, 0x32, 0x61, 0x37, 0x31, 0x65, 0x63, 0x63, 0x36, 0x39, 0x66, 0x64,
	0x35, 0x39, 0x37, 0x35, 0x38, 0x65, 0x32, 0x62, 0x61, 0x36, 0x39, 0x39, 0x36, 0x30, 0x36, 0x65,
	0x32, 0x64, 0x35, 0x63, 0x37, 0x34, 0x32, 0x38, 0x34, 0x66, 0x66, 0x61, 0x37, 0x22, 0x5d, 0x7d,
	0x22, 0x81, 0x06, 0x0a, 0x0b, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x53, 0x0a, 0x0a, 0x62, 0x69, 0x64, 0x5f, 0x64, 0x69, 0x67, 0x65, 0x73, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0c, 0x42, 0x34, 0x92, 0x41, 0x31, 0x32, 0x2f, 0x44, 0x69, 0x67, 0x65, 0x73,
	0x74, 0x20, 0x6f, 0x66, 0x20, 0x74, 0x68, 0x65, 0x20, 0x62, 0x69, 0x64, 0x20, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x20, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x20, 0x62, 0x79, 0x20, 0x74,
	0x68, 0x65, 0x20, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x09, 0x62, 0x69, 0x64, 0x44,
	0x69, 0x67, 0x65, 0x73, 0x74, 0x12, 0x5f, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x23, 0x92, 0x41, 0x14, 0x32, 0x12,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x20, 0x6f, 0x66, 0x20, 0x74, 0x68, 0x65, 0x20, 0x62, 0x69,
	0x64, 0x2e, 0xba, 0x48, 0x09, 0x82, 0x01, 0x06, 0x10, 0x01, 0x1a, 0x02, 0x01, 0x02, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0xb4, 0x01, 0x0a, 0x12, 0x64, 0x69, 0x73, 0x70, 0x61,
	0x74, 0x63, 0x68, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x84, 0x01, 0x92, 0x41, 0x80, 0x01, 0x32, 0x7e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x20, 0x61, 0x74, 0x20, 0x77, 0x68, 0x69, 0x63, 0x68, 0x20, 0x74,
	0x68, 0x65, 0x20, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x20, 0x69, 0x73,
	0x20, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x20, 0x62, 0x79, 0x20, 0x70, 0x72, 0x6f,
	0x76, 0x69, 0x64, 0x65, 0x72, 0x20, 0x61, 0x6e, 0x64, 0x20, 0x69, 0x73, 0x20, 0x75, 0x73, 0x65,
	0x64, 0x20, 0x74, 0x6f, 0x20, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x20, 0x74, 0x68, 0x65,
	0x20, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x20, 0x72, 0x65, 0x76, 0x65, 0x6e, 0x75,
	0x65, 0x20, 0x66, 0x72, 0x6f, 0x6d, 0x20, 0x74, 0x68, 0x65, 0x20, 0x70, 0x72, 0x65, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x11, 0x64, 0x69, 0x73, 0x70,
	0x61, 0x74, 0x63, 0x68, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x4a, 0x0a,
	0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x13, 0x0a, 0x0f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x50, 0x54,
	0x45, 0x44, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52,
	0x45, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x02, 0x3a, 0xb8, 0x02, 0x92, 0x41, 0xb4, 0x02,
	0x0a, 0x82, 0x01, 0x2a, 0x0c, 0x42, 0x69, 0x64, 0x20, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x32, 0x44, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x20, 0x73, 0x65, 0x6e, 0x74,
	0x20, 0x62, 0x79, 0x20, 0x74, 0x68, 0x65, 0x20, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72,
	0x20, 0x77, 0x69, 0x74, 0x68, 0x20, 0x74, 0x68, 0x65, 0x20, 0x64, 0x65, 0x63, 0x69, 0x73, 0x69,
	0x6f, 0x6e, 0x20, 0x6f, 0x6e, 0x20, 0x74, 0x68, 0x65, 0x20, 0x62, 0x69, 0x64, 0x20, 0x72, 0x65,
	0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x2e, 0xd2, 0x01, 0x09, 0x62, 0x69, 0x64, 0x44, 0x69, 0x67,
	0x65, 0x73, 0x74, 0xd2, 0x01, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0xd2, 0x01, 0x16, 0x64,
	0x65, 0x63, 0x61, 0x79, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x32, 0xac, 0x01, 0x7b, 0x22, 0x62, 0x69, 0x64, 0x44, 0x69, 0x67,
	0x65, 0x73, 0x74, 0x22, 0x3a, 0x20, 0x22, 0x39, 0x64, 0x4a, 0x69, 0x6e, 0x77, 0x4c, 0x2b, 0x46,
	0x5a, 0x36, 0x42, 0x31, 0x78, 0x73, 0x49, 0x51, 0x51, 0x6f, 0x38, 0x74, 0x38, 0x42, 0x30, 0x5a,
	0x58, 0x4a, 0x75, 0x62, 0x4a, 0x77, 0x59, 0x38, 0x36, 0x6c, 0x2f, 0x59, 0x75, 0x37, 0x79, 0x41,
	0x48, 0x31, 0x35, 0x39, 0x51, 0x72, 0x50, 0x48, 0x55, 0x30, 0x71, 0x6a, 0x32, 0x50, 0x2b, 0x59,
	0x46, 0x6a, 0x2b, 0x6c, 0x6c, 0x62, 0x75, 0x49, 0x31, 0x5a, 0x79, 0x67, 0x64, 0x78, 0x47, 0x73,
	0x58, 0x38, 0x2b, 0x50, 0x33, 0x62, 0x79, 0x4d, 0x45, 0x41, 0x35, 0x69, 0x67, 0x3d, 0x3d, 0x22,
	0x2c, 0x20, 0x22, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x3a, 0x20, 0x22, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x50, 0x54, 0x45, 0x44, 0x22, 0x2c, 0x20, 0x22,
	0x64, 0x65, 0x63, 0x61, 0x79, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x3a, 0x20, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37,
	0x38, 0x39, 0x30, 0x7d, 0x22, 0xd9, 0x03, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x6d,
	0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0xb0, 0x01, 0x0a, 0x0c, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x8c, 0x01, 0x92, 0x41, 0x88, 0x01, 0x32,
	0x85, 0x01, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x20, 0x62, 0x6c, 0x6f, 0x63, 0x6b,
	0x20, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x20, 0x66, 0x6f, 0x72, 0x20, 0x77, 0x68, 0x69, 0x63,
	0x68, 0x20, 0x74, 0x6f, 0x20, 0x67, 0x65, 0x74, 0x20, 0x74, 0x68, 0x65, 0x20, 0x63, 0x6f, 0x6d,
	0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x20, 0x69, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x20, 0x49, 0x66, 0x20, 0x6e, 0x6f, 0x74, 0x20, 0x73, 0x70, 0x65, 0x63,
	0x69, 0x66, 0x69, 0x65, 0x64, 0x20, 0x61, 0x6c, 0x6c, 0x20, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x20,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x20, 0x61, 0x72, 0x65, 0x20, 0x72, 0x65, 0x74, 0x75,
	0x72, 0x6e, 0x65, 0x64, 0x20, 0x69, 0x6e, 0x20, 0x61, 0x73, 0x63, 0x65, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x20, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x0b, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x12, 0x4c, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x38, 0x92, 0x41, 0x35, 0x32, 0x33, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x20, 0x70, 0x61, 0x67, 0x65, 0x20, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x20, 0x66, 0x6f,
	0x72, 0x20, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x20, 0x44, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x73, 0x20, 0x74, 0x6f, 0x20, 0x30, 0x2e, 0x52, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x12, 0x6c, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x56, 0x92, 0x41, 0x53, 0x32, 0x51, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x20, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x20, 0x66, 0x6f, 0x72, 0x20, 0x74, 0x68, 0x65, 0x20, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x20, 0x6f, 0x66, 0x20, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x20, 0x74, 0x6f, 0x20, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x20, 0x70,
	0x65, 0x72, 0x20, 0x70, 0x61, 0x67, 0x65, 0x2e, 0x20, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x73, 0x20, 0x74, 0x6f, 0x20, 0x31, 0x30, 0x30, 0x2e, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x3a, 0x4e, 0x92, 0x41, 0x4b, 0x0a, 0x49, 0x2a, 0x1b, 0x47, 0x65, 0x74, 0x20, 0x63, 0x6f, 0x6d,
	0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x20, 0x69, 0x6e, 0x66, 0x6f, 0x20, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x32, 0x2a, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x20, 0x74, 0x6f,
	0x20, 0x67, 0x65, 0x74, 0x20, 0x74, 0x68, 0x65, 0x20, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x20, 0x69, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x22, 0xc9, 0x0f, 0x0a, 0x16, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x86, 0x01, 0x0a, 0x0b,
	0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x37, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x43,
	0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x42, 0x2b, 0x92, 0x41, 0x28, 0x32,
	0x26, 0x4c, 0x69, 0x73, 0x74, 0x20, 0x6f, 0x66, 0x20, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x20, 0x6d, 0x61, 0x64, 0x65, 0x20, 0x69, 0x6e, 0x20, 0x74, 0x68, 0x65,
	0x20, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x2e, 0x52, 0x0b, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x1a, 0xd3, 0x0b, 0x0a, 0x0a, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x12, 0x71, 0x0a, 0x0a, 0x74, 0x78, 0x6e, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x65,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0x52, 0x92, 0x41, 0x4f, 0x32, 0x3b, 0x4c, 0x69,
	0x73, 0x74, 0x20, 0x6f, 0x66, 0x20, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x20, 0x68, 0x61, 0x73, 0x68, 0x65, 0x73, 0x20, 0x74, 0x68, 0x61, 0x74, 0x20, 0x61, 0x72,
	0x65, 0x20, 0x70, 0x61, 0x72, 0x74, 0x20, 0x6f, 0x66, 0x20, 0x74, 0x68, 0x65, 0x20, 0x63, 0x6f,
	0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x8a, 0x01, 0x0f, 0x5b, 0x61, 0x2d, 0x66,
	0x41, 0x2d, 0x46, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x36, 0x34, 0x7d, 0x52, 0x09, 0x74, 0x78, 0x6e,
	0x48, 0x61, 0x73, 0x68, 0x65, 0x73, 0x12, 0x81, 0x01, 0x0a, 0x15, 0x72, 0x65, 0x76, 0x65, 0x72,
	0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x74, 0x78, 0x6e, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x65, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x42, 0x4d, 0x92, 0x41, 0x4a, 0x32, 0x36, 0x4c, 0x69, 0x73,
	0x74, 0x20, 0x6f, 0x66, 0x20, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x20, 0x68, 0x61, 0x73, 0x68, 0x65, 0x73, 0x20, 0x74, 0x68, 0x61, 0x74, 0x20, 0x61, 0x72, 0x65,
	0x20, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x20, 0x74, 0x6f, 0x20, 0x72, 0x65, 0x76, 0x65,
	0x72, 0x74, 0x2e, 0x8a, 0x01, 0x0f, 0x5b, 0x61, 0x2d, 0x66, 0x41, 0x2d, 0x46, 0x30, 0x2d, 0x39,
	0x5d, 0x7b, 0x36, 0x34, 0x7d, 0x52, 0x13, 0x72, 0x65, 0x76, 0x65, 0x72, 0x74, 0x61, 0x62, 0x6c,
	0x65, 0x54, 0x78, 0x6e, 0x48, 0x61, 0x73, 0x68, 0x65, 0x73, 0x12, 0x53, 0x0a, 0x06, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x3b, 0x92, 0x41, 0x38, 0x32,
	0x2d, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x20, 0x6f, 0x66, 0x20, 0x45, 0x54, 0x48, 0x20, 0x69,
	0x6e, 0x20, 0x77, 0x65, 0x69, 0x20, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x20,
	0x62, 0x79, 0x20, 0x74, 0x68, 0x65, 0x20, 0x62, 0x69, 0x64, 0x64, 0x65, 0x72, 0x2e, 0x8a, 0x01,
	0x06, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x2b, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x55, 0x0a, 0x0c, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x32, 0x92, 0x41, 0x2f, 0x32, 0x2d, 0x42, 0x6c, 0x6f, 0x63,
	0x6b, 0x20, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x20, 0x61, 0x74, 0x20, 0x77, 0x68, 0x69, 0x63,
	0x68, 0x20, 0x74, 0x68, 0x65, 0x20, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x20, 0x69, 0x73, 0x20, 0x6d, 0x61, 0x64, 0x65, 0x2e, 0x52, 0x0b, 0x62, 0x6c, 0x6f, 0x63, 0x6b,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x88, 0x01, 0x0a, 0x10, 0x70, 0x72, 0x6f, 0x76, 0x69,
	0x64, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x5d, 0x92, 0x41, 0x5a, 0x32, 0x58, 0x48, 0x65, 0x78, 0x20, 0x73, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x20, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x20, 0x6f, 0x66, 0x20, 0x74,
	0x68, 0x65, 0x20, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x20, 0x6f, 0x66, 0x20, 0x74, 0x68,
	0x65, 0x20, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x20, 0x74, 0x68, 0x61, 0x74, 0x20,
	0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x20, 0x74, 0x68, 0x65, 0x20, 0x63, 0x6f, 0x6d, 0x6d, 0x69,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x20, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x2e,
	0x52, 0x0f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x12, 0x64, 0x0a, 0x15, 0x64, 0x65, 0x63, 0x61, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x30, 0x92, 0x41, 0x2d, 0x32, 0x2b, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x20, 0x61, 0x74, 0x20, 0x77, 0x68, 0x69, 0x63, 0x68, 0x20, 0x74, 0x68, 0x65, 0x20, 0x62, 0x69,
	0x64, 0x20, 0x73, 0x74, 0x61, 0x72, 0x74, 0x73, 0x20, 0x64, 0x65, 0x63, 0x61, 0x79, 0x69, 0x6e,
	0x67, 0x2e, 0x52, 0x13, 0x64, 0x65, 0x63, 0x61, 0x79, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x5e, 0x0a, 0x13, 0x64, 0x65, 0x63, 0x61, 0x79,
	0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x2e, 0x92, 0x41, 0x2b, 0x32, 0x29, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x20, 0x61, 0x74, 0x20, 0x77, 0x68, 0x69, 0x63, 0x68, 0x20, 0x74, 0x68,
	0x65, 0x20, 0x62, 0x69, 0x64, 0x20, 0x65, 0x6e, 0x64, 0x73, 0x20, 0x64, 0x65, 0x63, 0x61, 0x79,
	0x69, 0x6e, 0x67, 0x2e, 0x52, 0x11, 0x64, 0x65, 0x63, 0x61, 0x79, 0x45, 0x6e, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x63, 0x0a, 0x12, 0x64, 0x69, 0x73, 0x70, 0x61,
	0x74, 0x63, 0x68, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x34, 0x92, 0x41, 0x31, 0x32, 0x2f, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x20, 0x61, 0x74, 0x20, 0x77, 0x68, 0x69, 0x63, 0x68, 0x20, 0x74, 0x68, 0x65,
	0x20, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x20, 0x69, 0x73, 0x20, 0x70,
	0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x64, 0x2e, 0x52, 0x11, 0x64, 0x69, 0x73, 0x70, 0x61,
	0x74, 0x63, 0x68, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x85, 0x01, 0x0a,
	0x0c, 0x73, 0x6c, 0x61, 0x73, 0x68, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x62, 0x92, 0x41, 0x5f, 0x32, 0x5d, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x20, 0x6f, 0x66, 0x20, 0x45, 0x54, 0x48, 0x20, 0x74, 0x68, 0x61, 0x74, 0x20, 0x77, 0x69, 0x6c,
	0x6c, 0x20, 0x62, 0x65, 0x20, 0x73, 0x6c, 0x61, 0x73, 0x68, 0x65, 0x64, 0x20, 0x66, 0x72, 0x6f,
	0x6d, 0x20, 0x74, 0x68, 0x65, 0x20, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x20, 0x69,
	0x66, 0x20, 0x74, 0x68, 0x65, 0x79, 0x20, 0x66, 0x61, 0x69, 0x6c, 0x20, 0x74, 0x6f, 0x20, 0x69,
	0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x20, 0x74, 0x68, 0x65, 0x20, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x52, 0x0b, 0x73, 0x6c, 0x61, 0x73, 0x68, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x86, 0x01, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x6e, 0x92, 0x41, 0x6b, 0x32, 0x69, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x20, 0x6f, 0x66, 0x20, 0x74, 0x68, 0x65, 0x20, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x20, 0x50, 0x6f, 0x73, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x20, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x73, 0x3a, 0x20, 0x27, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x27,
	0x2c, 0x20, 0x27, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x64, 0x27, 0x2c, 0x20, 0x27, 0x6f, 0x70, 0x65,
	0x6e, 0x65, 0x64, 0x27, 0x2c, 0x20, 0x27, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x64, 0x27, 0x2c,
	0x20, 0x27, 0x73, 0x6c, 0x61, 0x73, 0x68, 0x65, 0x64, 0x27, 0x2c, 0x20, 0x27, 0x66, 0x61, 0x69,
	0x6c, 0x65, 0x64, 0x27, 0x2e, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4e, 0x0a,
	0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x42, 0x34,
	0x92, 0x41, 0x31, 0x32, 0x2f, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x20,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x20, 0x61, 0x62, 0x6f, 0x75, 0x74, 0x20, 0x74, 0x68,
	0x65, 0x20, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x20, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x2e, 0x52, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x48, 0x0a,
	0x07, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2e,
	0x92, 0x41, 0x2b, 0x32, 0x29, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x20, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x20, 0x69, 0x6e, 0x20, 0x77, 0x65, 0x69, 0x20, 0x66, 0x6f, 0x72, 0x20, 0x74,
	0x68, 0x65, 0x20, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x07,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x54, 0x0a, 0x06, 0x72, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x42, 0x3c, 0x92, 0x41, 0x39, 0x32, 0x37, 0x52, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x20, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x20, 0x69, 0x6e, 0x20, 0x77,
	0x65, 0x69, 0x20, 0x66, 0x6f, 0x72, 0x20, 0x74, 0x68, 0x65, 0x20, 0x63, 0x6f, 0x6d, 0x6d, 0x69,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2c, 0x20, 0x69, 0x66, 0x20, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x62, 0x6c, 0x65, 0x2e, 0x52, 0x06, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x12, 0x6b, 0x0a,
	0x0b, 0x62, 0x69, 0x64, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x31, 0x2e, 0x42, 0x69, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x42, 0x2e,
	0x92, 0x41, 0x2b, 0x32, 0x29, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x20, 0x62, 0x69,
	0x64, 0x20, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x20, 0x66, 0x6f, 0x72, 0x20, 0x74, 0x68,
	0x65, 0x20, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x52, 0x0a,
	0x62, 0x69, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0xef, 0x01, 0x0a, 0x10, 0x42,
	0x6c, 0x6f, 0x63, 0x6b, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12,
	0x58, 0x0a, 0x0c, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x35, 0x92, 0x41, 0x32, 0x32, 0x30, 0x42, 0x6c, 0x6f, 0x63,
	0x6b, 0x20, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x20, 0x66, 0x6f, 0x72, 0x20, 0x77, 0x68, 0x69,
	0x63, 0x68, 0x20, 0x74, 0x68, 0x65, 0x20, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x20, 0x61, 0x72, 0x65, 0x20, 0x6d, 0x61, 0x64, 0x65, 0x2e, 0x52, 0x0b, 0x62, 0x6c,
	0x6f, 0x63, 0x6b, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x80, 0x01, 0x0a, 0x0b, 0x63, 0x6f,
	0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x31, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x42, 0x2b, 0x92, 0x41, 0x28, 0x32, 0x26, 0x4c, 0x69, 0x73, 0x74, 0x20, 0x6f, 0x66,
	0x20, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x20, 0x6d, 0x61, 0x64,
	0x65, 0x20, 0x69, 0x6e, 0x20, 0x74, 0x68, 0x65, 0x20, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x2e, 0x52,
	0x0b, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x3a, 0x5e, 0x92, 0x41,
	0x5b, 0x0a, 0x59, 0x2a, 0x18, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x20,
	0x69, 0x6e, 0x66, 0x6f, 0x20, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x32, 0x2f, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x20, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x69,
	0x6e, 0x67, 0x20, 0x74, 0x68, 0x65, 0x20, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x20, 0x69, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0xd2, 0x01,
	0x0b, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x32, 0xac, 0x09, 0x0a,
	0x08, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x12, 0x65, 0x0a, 0x0b, 0x52, 0x65, 0x63,
	0x65, 0x69, 0x76, 0x65, 0x42, 0x69, 0x64, 0x73, 0x12, 0x1c, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69,
	0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65,
	0x72, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x69, 0x64, 0x22, 0x21, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x1b, 0x12, 0x19, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65,
	0x72, 0x2f, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x5f, 0x62, 0x69, 0x64, 0x73, 0x30, 0x01,
	0x12, 0x7d, 0x0a, 0x11, 0x53, 0x65, 0x6e, 0x64, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x65,
	0x64, 0x42, 0x69, 0x64, 0x73, 0x12, 0x1b, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x1a, 0x1c, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x22, 0x2b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x25, 0x3a, 0x01, 0x2a, 0x22, 0x20, 0x2f, 0x76, 0x31,
	0x2f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x2f, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x65, 0x64, 0x5f, 0x62, 0x69, 0x64, 0x73, 0x28, 0x01, 0x12,
	0x69, 0x0a, 0x05, 0x53, 0x74, 0x61, 0x6b, 0x65, 0x12, 0x1c, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69,
	0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x6b, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65,
	0x72, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x6b, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x23, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x22, 0x1b, 0x2f,
	0x76, 0x31, 0x2f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x2f, 0x73, 0x74, 0x61, 0x6b,
	0x65, 0x2f, 0x7b, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x7d, 0x12, 0x67, 0x0a, 0x08, 0x47, 0x65,
	0x74, 0x53, 0x74, 0x61, 0x6b, 0x65, 0x12, 0x1c, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65,
	0x72, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x1a, 0x1d, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x61,
	0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x6b, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x1e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x18, 0x12, 0x16, 0x2f, 0x76, 0x31,
	0x2f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x2f, 0x67, 0x65, 0x74, 0x5f, 0x73, 0x74,
	0x61, 0x6b, 0x65, 0x12, 0x6e, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x4d, 0x69, 0x6e, 0x53, 0x74, 0x61,
	0x6b, 0x65, 0x12, 0x1c, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x1a, 0x1d, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x74, 0x61, 0x6b, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x22, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x12, 0x1a, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x6f,
	0x76, 0x69, 0x64, 0x65, 0x72, 0x2f, 0x67, 0x65, 0x74, 0x5f, 0x6d, 0x69, 0x6e, 0x5f, 0x73, 0x74,
	0x61, 0x6b, 0x65, 0x12, 0x76, 0x0a, 0x0d, 0x57, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x53,
	0x74, 0x61, 0x6b, 0x65, 0x12, 0x1c, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x61,
	0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x1a, 0x22, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x31, 0x2e, 0x57, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x61, 0x6c, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x23, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x22, 0x1b,
	0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x2f, 0x77, 0x69, 0x74,
	0x68, 0x64, 0x72, 0x61, 0x77, 0x5f, 0x73, 0x74, 0x61, 0x6b, 0x65, 0x12, 0x63, 0x0a, 0x07, 0x55,
	0x6e, 0x73, 0x74, 0x61, 0x6b, 0x65, 0x12, 0x1c, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65,
	0x72, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x1a, 0x1c, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x61,
	0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x22, 0x1c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16, 0x22, 0x14, 0x2f, 0x76, 0x31, 0x2f,
	0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x2f, 0x75, 0x6e, 0x73, 0x74, 0x61, 0x6b, 0x65,
	0x12, 0x7b, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x12, 0x1c, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x1a, 0x1e, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x12, 0x20, 0x2f, 0x76, 0x31,
	0x2f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x2f, 0x67, 0x65, 0x74, 0x5f, 0x70, 0x72,
	0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x12, 0x89, 0x01,
	0x0a, 0x16, 0x57, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64,
	0x65, 0x72, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x12, 0x1c, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69,
	0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0x22, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65,
	0x72, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77,
	0x61, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2d, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x27, 0x22, 0x25, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72,
	0x2f, 0x77, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64,
	0x65, 0x72, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x12, 0x8f, 0x01, 0x0a, 0x11, 0x47, 0x65,
	0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x28, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x70, 0x72, 0x6f, 0x76,
	0x69, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x69,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x12, 0x20, 0x2f, 0x76, 0x31, 0x2f, 0x70,
	0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x2f, 0x67, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x6d,
	0x69, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x42, 0xbc, 0x02, 0x92, 0x41,
	0x74, 0x12, 0x72, 0x0a, 0x0c, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x20, 0x41, 0x50,
	0x49, 0x2a, 0x55, 0x0a, 0x1b, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x20, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x20, 0x4c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x20, 0x31, 0x2e, 0x31,
	0x12, 0x36, 0x68, 0x74, 0x74, 0x70, 0x73, 0x3a, 0x2f, 0x2f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x72, 0x69, 0x6d, 0x65, 0x76, 0x2f, 0x6d, 0x65, 0x76, 0x2d,
	0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x2f, 0x62, 0x6c, 0x6f, 0x62, 0x2f, 0x6d, 0x61, 0x69, 0x6e,
	0x2f, 0x4c, 0x49, 0x43, 0x45, 0x4e, 0x53, 0x45, 0x32, 0x0b, 0x31, 0x2e, 0x30, 0x2e, 0x30, 0x2d,
	0x61, 0x6c, 0x70, 0x68, 0x61, 0x0a, 0x12, 0x63, 0x6f, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69,
	0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x42, 0x10, 0x50, 0x72, 0x6f, 0x76, 0x69,
	0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x44, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x72, 0x69, 0x6d, 0x65, 0x76,
	0x2f, 0x6d, 0x65, 0x76, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x2f, 0x70, 0x32, 0x70, 0x2f,
	0x67, 0x65, 0x6e, 0x2f, 0x67, 0x6f, 0x2f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x31, 0x3b, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x61, 0x70,
	0x69, 0x76, 0x31, 0xa2, 0x02, 0x03, 0x50, 0x58, 0x58, 0xaa, 0x02, 0x0e, 0x50, 0x72, 0x6f, 0x76,
	0x69, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x2e, 0x56, 0x31, 0xca, 0x02, 0x0e, 0x50, 0x72, 0x6f,
	0x76, 0x69, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x5c, 0x56, 0x31, 0xe2, 0x02, 0x1a, 0x50, 0x72,
	0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x5c, 0x56, 0x31, 0x5c, 0x47, 0x50, 0x42,
	0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0xea, 0x02, 0x0f, 0x50, 0x72, 0x6f, 0x76, 0x69,
	0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x3a, 0x3a, 0x56, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_providerapi_v1_providerapi_proto_rawDescOnce sync.Once
	file_providerapi_v1_providerapi_proto_rawDescData = file_providerapi_v1_providerapi_proto_rawDesc
)

func file_providerapi_v1_providerapi_proto_rawDescGZIP() []byte {
	file_providerapi_v1_providerapi_proto_rawDescOnce.Do(func() {
		file_providerapi_v1_providerapi_proto_rawDescData = protoimpl.X.CompressGZIP(file_providerapi_v1_providerapi_proto_rawDescData)
	})
	return file_providerapi_v1_providerapi_proto_rawDescData
}

var file_providerapi_v1_providerapi_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_providerapi_v1_providerapi_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_providerapi_v1_providerapi_proto_goTypes = []interface{}{
	(PositionConstraint_Anchor)(0),                  // 0: providerapi.v1.PositionConstraint.Anchor
	(PositionConstraint_Basis)(0),                   // 1: providerapi.v1.PositionConstraint.Basis
	(BidResponse_Status)(0),                         // 2: providerapi.v1.BidResponse.Status
	(*StakeRequest)(nil),                            // 3: providerapi.v1.StakeRequest
	(*StakeResponse)(nil),                           // 4: providerapi.v1.StakeResponse
	(*WithdrawalResponse)(nil),                      // 5: providerapi.v1.WithdrawalResponse
	(*RewardResponse)(nil),                          // 6: providerapi.v1.RewardResponse
	(*EmptyMessage)(nil),                            // 7: providerapi.v1.EmptyMessage
	(*PositionConstraint)(nil),                      // 8: providerapi.v1.PositionConstraint
	(*BidOption)(nil),                               // 9: providerapi.v1.BidOption
	(*BidOptions)(nil),                              // 10: providerapi.v1.BidOptions
	(*Bid)(nil),                                     // 11: providerapi.v1.Bid
	(*BidResponse)(nil),                             // 12: providerapi.v1.BidResponse
	(*GetCommitmentInfoRequest)(nil),                // 13: providerapi.v1.GetCommitmentInfoRequest
	(*CommitmentInfoResponse)(nil),                  // 14: providerapi.v1.CommitmentInfoResponse
	(*CommitmentInfoResponse_Commitment)(nil),       // 15: providerapi.v1.CommitmentInfoResponse.Commitment
	(*CommitmentInfoResponse_BlockCommitments)(nil), // 16: providerapi.v1.CommitmentInfoResponse.BlockCommitments
}
var file_providerapi_v1_providerapi_proto_depIdxs = []int32{
	0,  // 0: providerapi.v1.PositionConstraint.anchor:type_name -> providerapi.v1.PositionConstraint.Anchor
	1,  // 1: providerapi.v1.PositionConstraint.basis:type_name -> providerapi.v1.PositionConstraint.Basis
	8,  // 2: providerapi.v1.BidOption.position_constraint:type_name -> providerapi.v1.PositionConstraint
	9,  // 3: providerapi.v1.BidOptions.options:type_name -> providerapi.v1.BidOption
	10, // 4: providerapi.v1.Bid.bid_options:type_name -> providerapi.v1.BidOptions
	2,  // 5: providerapi.v1.BidResponse.status:type_name -> providerapi.v1.BidResponse.Status
	16, // 6: providerapi.v1.CommitmentInfoResponse.commitments:type_name -> providerapi.v1.CommitmentInfoResponse.BlockCommitments
	10, // 7: providerapi.v1.CommitmentInfoResponse.Commitment.bid_options:type_name -> providerapi.v1.BidOptions
	15, // 8: providerapi.v1.CommitmentInfoResponse.BlockCommitments.commitments:type_name -> providerapi.v1.CommitmentInfoResponse.Commitment
	7,  // 9: providerapi.v1.Provider.ReceiveBids:input_type -> providerapi.v1.EmptyMessage
	12, // 10: providerapi.v1.Provider.SendProcessedBids:input_type -> providerapi.v1.BidResponse
	3,  // 11: providerapi.v1.Provider.Stake:input_type -> providerapi.v1.StakeRequest
	7,  // 12: providerapi.v1.Provider.GetStake:input_type -> providerapi.v1.EmptyMessage
	7,  // 13: providerapi.v1.Provider.GetMinStake:input_type -> providerapi.v1.EmptyMessage
	7,  // 14: providerapi.v1.Provider.WithdrawStake:input_type -> providerapi.v1.EmptyMessage
	7,  // 15: providerapi.v1.Provider.Unstake:input_type -> providerapi.v1.EmptyMessage
	7,  // 16: providerapi.v1.Provider.GetProviderReward:input_type -> providerapi.v1.EmptyMessage
	7,  // 17: providerapi.v1.Provider.WithdrawProviderReward:input_type -> providerapi.v1.EmptyMessage
	13, // 18: providerapi.v1.Provider.GetCommitmentInfo:input_type -> providerapi.v1.GetCommitmentInfoRequest
	11, // 19: providerapi.v1.Provider.ReceiveBids:output_type -> providerapi.v1.Bid
	7,  // 20: providerapi.v1.Provider.SendProcessedBids:output_type -> providerapi.v1.EmptyMessage
	4,  // 21: providerapi.v1.Provider.Stake:output_type -> providerapi.v1.StakeResponse
	4,  // 22: providerapi.v1.Provider.GetStake:output_type -> providerapi.v1.StakeResponse
	4,  // 23: providerapi.v1.Provider.GetMinStake:output_type -> providerapi.v1.StakeResponse
	5,  // 24: providerapi.v1.Provider.WithdrawStake:output_type -> providerapi.v1.WithdrawalResponse
	7,  // 25: providerapi.v1.Provider.Unstake:output_type -> providerapi.v1.EmptyMessage
	6,  // 26: providerapi.v1.Provider.GetProviderReward:output_type -> providerapi.v1.RewardResponse
	5,  // 27: providerapi.v1.Provider.WithdrawProviderReward:output_type -> providerapi.v1.WithdrawalResponse
	14, // 28: providerapi.v1.Provider.GetCommitmentInfo:output_type -> providerapi.v1.CommitmentInfoResponse
	19, // [19:29] is the sub-list for method output_type
	9,  // [9:19] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_providerapi_v1_providerapi_proto_init() }
func file_providerapi_v1_providerapi_proto_init() {
	if File_providerapi_v1_providerapi_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_providerapi_v1_providerapi_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StakeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_providerapi_v1_providerapi_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StakeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_providerapi_v1_providerapi_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WithdrawalResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_providerapi_v1_providerapi_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_providerapi_v1_providerapi_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmptyMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_providerapi_v1_providerapi_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PositionConstraint); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_providerapi_v1_providerapi_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_providerapi_v1_providerapi_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_providerapi_v1_providerapi_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Bid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_providerapi_v1_providerapi_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_providerapi_v1_providerapi_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCommitmentInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_providerapi_v1_providerapi_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommitmentInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_providerapi_v1_providerapi_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommitmentInfoResponse_Commitment); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_providerapi_v1_providerapi_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommitmentInfoResponse_BlockCommitments); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_providerapi_v1_providerapi_proto_msgTypes[6].OneofWrappers = []interface{}{
		(*BidOption_PositionConstraint)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_providerapi_v1_providerapi_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_providerapi_v1_providerapi_proto_goTypes,
		DependencyIndexes: file_providerapi_v1_providerapi_proto_depIdxs,
		EnumInfos:         file_providerapi_v1_providerapi_proto_enumTypes,
		MessageInfos:      file_providerapi_v1_providerapi_proto_msgTypes,
	}.Build()
	File_providerapi_v1_providerapi_proto = out.File
	file_providerapi_v1_providerapi_proto_rawDesc = nil
	file_providerapi_v1_providerapi_proto_goTypes = nil
	file_providerapi_v1_providerapi_proto_depIdxs = nil
}

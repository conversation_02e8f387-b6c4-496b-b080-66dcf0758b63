syntax = "proto3";

package providerapi.v1;

import "protoc-gen-openapiv2/options/annotations.proto";
import "google/api/annotations.proto";
import "buf/validate/validate.proto";

option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_swagger) = {
  info: {
    title: "Provider API";
    version: "1.0.0-alpha";
    license: {
      name: "Business Source License 1.1";
      url: "https://github.com/primev/mev-commit/blob/main/LICENSE";
    };
  };
};

service Provider {
  // ReceiveBids
  //
  // ReceiveBids is called by the provider to receive bids from the mev-commit node.
  // The mev-commit node will stream bids to the provider as the response. The bid can optionally
  // have the raw transaction payload in it. The order of the transaction hashes will be the same
  // as the raw transaction payloads if included.
  rpc ReceiveBids(EmptyMessage) returns (stream Bid) {
    option (google.api.http) = {get: "/v1/provider/receive_bids"};
  }
  // SendProcessedBids
  //
  // SendProcessedBids is called by the provider to send processed bids to the mev-commit node.
  // The provider will stream processed bids to the mev-commit node.
  rpc SendProcessedBids(stream BidResponse) returns (EmptyMessage) {
    option (google.api.http) = {
      post: "/v1/provider/send_processed_bids"
      body: "*"
    };
  }
  // Stake
  //
  // Stake is called by the provider to register or add to its stake in the provider registry.
  rpc Stake(StakeRequest) returns (StakeResponse) {
    option (google.api.http) = {post: "/v1/provider/stake/{amount}"};
  }
  // GetStake
  //
  // GetStake is called by the provider to get its stake in the provider registry.
  rpc GetStake(EmptyMessage) returns (StakeResponse) {
    option (google.api.http) = {get: "/v1/provider/get_stake"};
  }
  // GetMinStake
  //
  // GetMinStake is called by the provider to get the minimum stake required to be in the provider registry.
  rpc GetMinStake(EmptyMessage) returns (StakeResponse) {
    option (google.api.http) = {get: "/v1/provider/get_min_stake"};
  }
  // WithdrawStake
  //
  // WithdrawStake is called by the provider to withdraw its stake from the provider registry.
  rpc WithdrawStake(EmptyMessage) returns (WithdrawalResponse) {
    option (google.api.http) = {post: "/v1/provider/withdraw_stake"};
  }
  // Unstake
  //
  // Unstake is called by the provider to request a unstake from the provider registry.
  rpc Unstake(EmptyMessage) returns (EmptyMessage) {
    option (google.api.http) = {post: "/v1/provider/unstake"};
  }
  // GetProviderReward
  //
  // GetProviderReward is called by the provider to retrieve their current reward balance
  // without withdrawing it from the bidder registry.
  rpc GetProviderReward(EmptyMessage) returns (RewardResponse) {
    option (google.api.http) = {get: "/v1/provider/get_provider_reward"};
  }
  // WithdrawProviderReward
  //
  // WithdrawProviderReward is called by the provider to withdraw their accumulated rewards
  // from the bidder registry contract.
  rpc WithdrawProviderReward(EmptyMessage) returns (WithdrawalResponse) {
    option (google.api.http) = {post: "/v1/provider/withdraw_provider_reward"};
  }
  // GetCommitmentInfo
  //
  // GetCommitmentInfo is called by the provider to retrieve the commitment information.
  rpc GetCommitmentInfo(GetCommitmentInfoRequest) returns (CommitmentInfoResponse) {
    option (google.api.http) = {get: "/v1/provider/get_commitment_info"};
  }
}

message StakeRequest {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {
    json_schema: {
      title: "Stake request"
      description: "Stake provider in the provider registry."
      required: ["amount", "bls_public_key"]
    }
    example: "{\"amount\": \"1000000000000000000\", \"bls_public_key\": \"80000cddeec66a800e00b0ccbb62f12298073603f5209e812abbac7e870482e488dd1bbe533a9d44497ba8b756e1e82b\"}"
  };
  string amount = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Amount of ETH to stake in the provider registry."
    pattern: "[0-9]+"
  }, (buf.validate.field).cel = {
      id: "amount",
      message: "amount must be a valid integer.",
      expression: "this.matches('^[1-9][0-9]*$')"
  }];
  repeated string bls_public_keys = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "BLS public keys of the provider."
    pattern: "^(0x)?[a-fA-F0-9]{96}$"
  }, (buf.validate.field).cel = {
      id: "bls_public_key",
      message: "bls_public_key must be a valid 48-byte hex string, with optional 0x prefix.",
      expression: "this.all(r, r.matches('^(0x)?[a-fA-F0-9]{96}$'))"
  }];
  repeated string bls_signatures = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "BLS signatures corresponding to the BLS public keys."
    pattern: "^(0x)?[a-fA-F0-9]{192}$"
  }, (buf.validate.field).cel = {
      id: "bls_signatures",
      message: "bls_signatures must be a valid 96-byte hex string, with optional 0x prefix.",
      expression: "this.all(r, r.matches('^(0x)?[a-fA-F0-9]{192}$'))"
  }];
};

message StakeResponse {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {
    json_schema: {
      title: "Stake response"
      description: "Get staked amount for provider in the provider registry."
    }
    example: "{\"amount\": \"2000000000000000000\", \"bls_public_keys\": [\"90000cddeec66a80e00b0ccbb62f12298073603f5209e812abbac7e870482e488dd1bbe533a9d4497ba8b756e1e82b\", \"80000cddeec66a80e00b0ccbb62f12298073603f5209e812abbac7e870482e488dd1bbe533a9d4497ba8b756e1e82b\"]}"
  };
  string amount = 1;
  repeated string bls_public_keys = 2;
};

message WithdrawalResponse {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {
    json_schema: {
      title: "Withdrawal response"
      description: "Withdrawal amount for provider in the provider registry."
    }
    example: "{\"amount\": \"1000000000000000000\"}"
  };
  string amount = 1;
};

message RewardResponse {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {
    json_schema: {
      title: "Reward response"
      description: "Current reward amount for provider in the bidder registry."
    }
    example: "{\"amount\": \"500000000000000000\"}"
  };
  string amount = 1;
};

message EmptyMessage {};

message PositionConstraint {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {
    json_schema: {
      title: "Position Constraint"
      description: "Constraint on the position of the transaction in the block."
    }
  };
  enum Anchor {
    ANCHOR_UNSPECIFIED = 0;
    ANCHOR_TOP = 1; // Position is at the top of the block
    ANCHOR_BOTTOM = 2; // Position is at the bottom of the block
  }
  enum Basis {
    BASIS_UNSPECIFIED = 0;
    BASIS_PERCENTILE = 1; // Position is a percentile of the block size
    BASIS_ABSOLUTE = 2; // Position is an absolute position in the block
    BASIS_GAS_PERCENTILE = 3; // Position is a percentile of the gas used in the block
  }
  Anchor anchor = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Anchor position of the transaction in the block."
  }];
  Basis basis = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Basis for the position constraint."
  }];
  int32 value = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Value of the position constraint. If anchor is TOP, this is the position from the top of the block. If anchor is BOTTOM, this is the position from the bottom of the block."
  }];
};

message BidOption {
  oneof opt {
    PositionConstraint position_constraint = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "Position constraint for the transaction in the block."
    }];
  }
};

message BidOptions {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {
    json_schema: {
      title: "Bid Options"
      description: "Options for the bid."
    }
  };
  repeated BidOption options = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "List of bid options."
  }, (buf.validate.field).cel = {
      id: "options",
      message: "options must be a valid array of bid options.",
      expression: "this.all(r, r != null)"
  }];
};

message Bid {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {
    json_schema: {
      title: "Bid message"
      description: "Signed bid message from bidders to the provider."
      required: ["txHashes", "bidAmount", "blockNumber", "bidDigest"]
    }
    example: "{\"txHashes\": [\"fe4cb47db3630551beedfbd02a71ecc69fd59758e2ba699606e2d5c74284ffa7\", \"71c1348f2d7ff7e814f9c3617983703435ea7446de420aeac488bf1de35737e8\"], \"amount\": \"1000000000000000000\", \"blockNumber\": 123456, \"bidDigest\": \"9dJinwL+FZ6B1xsIQQo8t8B0ZXJubJwY86l/Yu7yAH159QrPHU0qj2P+YFj+llbuI1ZygdxGsX8+P3byMEA5ig==\", \"decayStartTimestamp\":1725365301000, \"decayEndTimestamp\":1725365302000, \"revertingTxHashes\":[\"fe4cb47db3630551beedfbd02a71ecc69fd59758e2ba699606e2d5c74284ffa7\"]}"
  };
  repeated string tx_hashes = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Hex string encoding of the hashes of the transactions that the bidder wants to include in the block."
    pattern: "[a-fA-F0-9]{64}"
  }, (buf.validate.field).cel = {
      id: "tx_hashes",
      message: "tx_hashes must be a valid array of transaction hashes.",
      expression: "this.all(r, r.matches('^[a-fA-F0-9]{64}$'))"
  }];
  string bid_amount = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Amount of ETH that the bidder is willing to pay to the provider for including the transaction in the block."
    pattern: "[0-9]+"
  }, (buf.validate.field).cel = {
      id: "bid_amount",
      message: "bid_amount must be a valid integer.",
      expression: "this.matches('^[1-9][0-9]*$')"
  }];
  int64 block_number = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Max block number that the bidder wants to include the transaction in."
  }, (buf.validate.field).int64.gt = 0];
  bytes bid_digest = 4 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Digest of the bid message signed by the bidder."
  }, (buf.validate.field).bytes = {
      min_len: 1,
      max_len: 64
  }];
  int64 decay_start_timestamp = 5 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Timestamp at which the bid starts decaying."
  }, (buf.validate.field).cel = {
      id: "decay_start_timestamp",
      message: "decay_start_timestamp must be a valid integer.",
      expression: "uint(this) > 0"
  }];
  int64 decay_end_timestamp = 6 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Timestamp at which the bid ends decaying."
  }, (buf.validate.field).cel = {
      id: "decay_end_timestamp",
      message: "decay_end_timestamp must be a valid integer.",
      expression: "uint(this) > 0"
  }];
  repeated string reverting_tx_hashes = 7 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Optional array of tx hashes that are allowed to revert or be discarded."
  }, (buf.validate.field).cel = {
      id: "reverting_tx_hashes",
      message: "reverting_tx_hashes must be an array of valid transaction hashes.",
      expression: "this.all(r, r.matches('^[a-fA-F0-9]{64}$'))"
  }];
  repeated string raw_transactions = 8 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Optional array of RLP encoded raw signed transaction payloads that the bidder wants to include in the block."
  }, (buf.validate.field).cel = {
      id: "raw_transactions",
      message: "raw_transactions must be an array of valid raw transactions.",
      expression: "this.all(r, r.matches('^[a-fA-F0-9]+$'))"
  }];
  string slash_amount = 9 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Amount of ETH that will be slashed from the provider if they fail to include the transaction. If zero, the decayed bid amount is used for slashing."
    pattern: "[0-9]+"
  }, (buf.validate.field).cel = {
      id: "slash_amount",
      message: "slash_amount must be a valid integer.",
      expression: "this == '' || (this.matches('^[0-9]+$') && uint(this) >= 0)"
  }];
  BidOptions bid_options = 10 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Optional bid options for the transaction."
  }];
};

message BidResponse {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {
    json_schema: {
      title: "Bid response"
      description: "Response sent by the provider with the decision on the bid received."
      required: ["bidDigest", "status", "decayDispatchTimestamp"]
    }
    example: "{\"bidDigest\": \"9dJinwL+FZ6B1xsIQQo8t8B0ZXJubJwY86l/Yu7yAH159QrPHU0qj2P+YFj+llbuI1ZygdxGsX8+P3byMEA5ig==\", \"status\": \"STATUS_ACCEPTED\", \"decayDispatchTimestamp\": **********}"
  };
  bytes bid_digest = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Digest of the bid message signed by the bidder."
  }];
  enum Status {
    STATUS_UNSPECIFIED = 0;
    STATUS_ACCEPTED = 1;
    STATUS_REJECTED = 2;
  }
  Status status = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Status of the bid."
  }, (buf.validate.field).enum = {
      defined_only: true,
      in: [
        1,
        2
      ]
  }];
  int64 dispatch_timestamp = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Timestamp at which the commitment is accepted by provider and is used to compute the expected revenue from the preconfirmation"
  }];
};

message GetCommitmentInfoRequest {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {
    json_schema: {
      title: "Get commitment info request"
      description: "Request to get the commitment information."
    }
  };
  int64 block_number = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Optional block number for which to get the commitment information. If not specified all block numbers are returned in ascending order"
  }];
  int32 page = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Optional page number for pagination. Defaults to 0."
  }];
  int32 limit = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Optional limit for the number of commitments to return per page. Defaults to 100."
  }];
};

message CommitmentInfoResponse {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {
    json_schema: {
      title: "Commitment info response"
      description: "Response containing the commitment information."
      required: ["commitments"]
    }
  };
  message Commitment {
    repeated string txn_hashes = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "List of transaction hashes that are part of the commitment."
      pattern: "[a-fA-F0-9]{64}"
    }];
    repeated string revertable_txn_hashes = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "List of transaction hashes that are allowed to revert."
      pattern: "[a-fA-F0-9]{64}"
    }];
    string amount = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "Amount of ETH in wei committed by the bidder."
      pattern: "[0-9]+"
    }];
    int64 block_number = 4 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "Block number at which the commitment is made."
    }];
    string provider_address = 5 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "Hex string encoding of the address of the provider that signed the commitment signature."
    }];
    int64 decay_start_timestamp = 6 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "Timestamp at which the bid starts decaying."
    }];
    int64 decay_end_timestamp = 7 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "Timestamp at which the bid ends decaying."
    }];
    int64 dispatch_timestamp = 8 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "Timestamp at which the commitment is published."
    }];
    string slash_amount = 9 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "Amount of ETH that will be slashed from the provider if they fail to include the transaction."
    }];
    string status = 10 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "Status of the commitment. Possible values: 'pending', 'stored', 'opened', 'settled', 'slashed', 'failed'."
    }];
    string details = 11 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "Additional details about the commitment status."
    }];
    string payment = 12 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "Payment amount in wei for the commitment."
    }];
    string refund = 13 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "Refund amount in wei for the commitment, if applicable."
    }];
    BidOptions bid_options = 14 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "Optional bid options for the transaction."
    }];
  };
  message BlockCommitments {
    int64 block_number = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "Block number for which the commitments are made."
    }];
    repeated Commitment commitments = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "List of commitments made in the block."
    }];
  };
  repeated BlockCommitments commitments = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "List of commitments made in the block."
  }];
};

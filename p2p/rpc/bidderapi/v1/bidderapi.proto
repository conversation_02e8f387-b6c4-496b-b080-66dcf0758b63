syntax = "proto3";

package bidderapi.v1;

import "protoc-gen-openapiv2/options/annotations.proto";
import "google/api/annotations.proto";
import "buf/validate/validate.proto";
import "google/protobuf/wrappers.proto";

option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_swagger) = {
  info: {
    title: "Bidder API";
    version: "1.0.0-alpha";
    license: {
      name: "Business Source License 1.1";
      url: "https://github.com/primev/mev-commit/blob/main/LICENSE";
    };
  };
};

service Bidder {
  // SendBid
  //
  // Send a bid to the bidder mev-commit node. The bid is a message from the bidder to the provider 
  // with the transaction hashes and the amount of ETH that the bidder is willing to pay to the provider
  // for including the transaction in the block. The bid also includes the block number that the bidder
  // wants to include the transaction in, the start and end timestamps for the bid decay. The bidder can
  // optionally include the raw transaction payloads (hex encoded RLP) instead of transaction hashes.
  rpc SendBid(Bid) returns (stream Commitment) {
    option (google.api.http) = {
      post: "/v1/bidder/bid"
      body: "*"
    };
  }

  // Deposit
  //
  // Deposit is called by the bidder node to add deposit in the bidder registry, specific to a provider.
  rpc Deposit(DepositRequest) returns (DepositResponse) {
    option (google.api.http) = {post: "/v1/bidder/deposit/{amount}"};
  }

  // DepositEvenly
  //
  // DepositEvenly is called by the bidder node to deposit a total amount evenly across multiple providers.
  rpc DepositEvenly(DepositEvenlyRequest) returns (DepositEvenlyResponse) {
    option (google.api.http) = {post: "/v1/bidder/deposit_evenly"};
  }

  // EnableDepositManager
  //
  // EnableDepositManager is called by the bidder node to enable the deposit manager via eip 7702.
  rpc EnableDepositManager(EnableDepositManagerRequest) returns (EnableDepositManagerResponse) {
    option (google.api.http) = {post: "/v1/bidder/enable_deposit_manager"};
  }

  // DisableDepositManager
  //
  // DisableDepositManager is called by the bidder node to disable the deposit manager 
  // by setting the bidder EOA's code to zero address.
  rpc DisableDepositManager(DisableDepositManagerRequest) returns (DisableDepositManagerResponse) {
    option (google.api.http) = {post: "/v1/bidder/disable_deposit_manager"};
  }

  // SetTargetDeposits
  //
  // SetTargetDeposits is called by the bidder node to set target deposits per provider
  // within the deposit manager. During this call, the bidder node will also attempt to top-up
  // deposits for each new target deposit.
  rpc SetTargetDeposits(SetTargetDepositsRequest) returns (SetTargetDepositsResponse) {
    option (google.api.http) = {
      post: "/v1/bidder/set_target_deposits"
      body: "*"
    };
  }

  // DepositManagerStatus
  //
  // DepositManagerStatus is called by the bidder node to query whether the bidder
  // has enabled the deposit manager via eip 7702.
  rpc DepositManagerStatus(DepositManagerStatusRequest) returns (DepositManagerStatusResponse) {
    option (google.api.http) = {get: "/v1/bidder/deposit_manager_status"};
  }

  // RequestWithdrawals
  //
  // RequestWithdrawals is called by the bidder node to request withdrawals from provider(s)
  rpc RequestWithdrawals(RequestWithdrawalsRequest) returns (RequestWithdrawalsResponse) {
    option (google.api.http) = {
      post: "/v1/bidder/request_withdrawals"
      body: "*"
    };
  }

  // GetValidProviders
  //
  // GetValidProviders is called by the bidder node to get a list of all valid providers.
  // Each provider returned by this RPC must:
  // - Be "registered" in the provider registry
  // - Have deposit >= minStake in provider registry
  // - Have no pending withdrawal request with provider registry
  // - Have at least one BLS key registered with provider registry
  rpc GetValidProviders(GetValidProvidersRequest) returns (GetValidProvidersResponse) {
    option (google.api.http) = {get: "/v1/bidder/get_valid_providers"};
  }

  // GetDeposit
  //
  // GetDeposit is called by the bidder to get its deposit specific to a provider in the bidder registry.
  rpc GetDeposit(GetDepositRequest) returns (DepositResponse) {
    option (google.api.http) = {
      get: "/v1/bidder/get_deposit"
    };
  }

  // GetAllDeposits
  //
  // GetAllDeposits is called by the bidder to get all its deposits in the bidder registry,
  // and the balance of the bidder EOA itself.
  rpc GetAllDeposits(GetAllDepositsRequest) returns (GetAllDepositsResponse) {
    option (google.api.http) = {get: "/v1/bidder/get_all_deposits"};
  }

  // Withdraw
  //
  // Withdraw is called by the bidder to withdraw their deposit to a provider.
  rpc Withdraw(WithdrawRequest) returns (WithdrawResponse) {
    option (google.api.http) = {
      post: "/v1/bidder/withdraw"
      body: "*"
    };
  }

  // GetBidInfo
  //
  // GetBidInfo is called by the bidder to get the bid information. If block number is not specified,
  // all known block numbers are returned in the ascending order.
  rpc GetBidInfo(GetBidInfoRequest) returns (GetBidInfoResponse) {
    option (google.api.http) = {get: "/v1/bidder/get_bid_info"};
  }

  // ClaimSlashedFunds
  //
  // ClaimSlashedFunds is called by the bidder to claim slashed funds from the provider. The response
  // will show the amount claimed if any in wei.
  rpc ClaimSlashedFunds(EmptyMessage) returns (google.protobuf.StringValue) {
    option (google.api.http) = {post: "/v1/bidder/claim_slashed_funds"};
  }
}

message DepositRequest {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {
    json_schema: {
      title: "Deposit request"
      description: "Deposit for bids to be issued by the bidder in wei, specific to a provider."
      required: ["amount", "provider"]
    }
  };
  string amount = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Amount of ETH to be deposited in wei.",
    pattern: "[0-9]+",
    example: "1000000000000000000"
  }, (buf.validate.field).cel = {
      id: "amount",
      message: "amount must be a valid integer.",
      expression: "this.matches('^[1-9][0-9]*$')"
  }];
  string provider = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Provider Ethereum address.",
    example: "\"******************************************\""
  }, (buf.validate.field).cel = {
      id: "provider",
      message: "provider must be a valid Ethereum address.",
      expression: "this.matches('^(0x)?[a-fA-F0-9]{40}$')"
  }];
};

message DepositResponse {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {
    json_schema: {
      title: "Deposit response"
      description: "Deposit for bidder in the bidder registry for a particular provider."
    }
    example: "{\"amount\": \"1000000000000000000\", \"provider\": \"******************************************\"}"
  };
  string amount = 1;
  string provider = 2;
};

message DepositEvenlyRequest {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {
    json_schema: {
      title: "Deposit evenly request"
      description: "Deposits some amount of ETH evenly across multiple providers."
    }
  };
  string total_amount = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Total amount of ETH to be deposited in wei.",
    pattern: "[0-9]+",
    example: "1000000000000000000"
  }, (buf.validate.field).cel = {
      id: "total_amount",
      message: "total_amount must be a valid integer.",
      expression: "this.matches('^[1-9][0-9]*$')"
  }];
  repeated string providers = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = { description: "Provider Ethereum addresses." },
    (buf.validate.field).cel = {
      id: "providers",
      message: "providers must be a valid array of Ethereum addresses.",
      expression: "this.all(r, r.matches('^(0x)?[a-fA-F0-9]{40}$'))"
  }];
}

message DepositEvenlyResponse {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {
    json_schema: {
      title: "Deposit evenly response"
      description: "Deposits some amount of ETH evenly across multiple providers."
    }
  };
  repeated string providers = 1;
  repeated string amounts = 2;
}

message EnableDepositManagerRequest {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {
    json_schema: {
      title: "EnableDepositManager request"
      description: "EnableDepositManager request."
    }
  };
}

message EnableDepositManagerResponse {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {
    json_schema: {
      title: "EnableDepositManager response"
      description: "EnableDepositManager response."
    }
  };
  bool success = 1;
}

message DisableDepositManagerRequest {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {
    json_schema: {
      title: "DisableDepositManager request"
      description: "DisableDepositManager request."
    }
  };
}

message DisableDepositManagerResponse {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {
    json_schema: {
      title: "DisableDepositManager response"
      description: "DisableDepositManager response."
    }
  };
  bool success = 1;
}

message TargetDeposit {
  string provider = 1;
  string target_deposit = 2;
}

message SetTargetDepositsRequest {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {
    json_schema: {
      title: "OverrideTargetDepositsRequest request"
      description: "OverrideTargetDepositsRequest request."
    }
  };
  repeated TargetDeposit target_deposits = 1;
}

message SetTargetDepositsResponse {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {
    json_schema: {
      title: "OverrideTargetDepositsResponse response"
      description: "OverrideTargetDepositsResponse response."
    }
  };
  repeated TargetDeposit successfully_set_deposits = 1;
  repeated string successfully_topped_up_providers = 2;
}

message DepositManagerStatusRequest {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {
    json_schema: {
      title: "DepositManagerStatus request"
      description: "DepositManagerStatus request."
    }
  };
}

message DepositManagerStatusResponse {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {
    json_schema: {
      title: "DepositManagerStatus response"
      description: "DepositManagerStatus response."
    }
  };
  bool enabled = 1;
  repeated TargetDeposit target_deposits = 2;
}

message EmptyMessage {};

message GetDepositRequest {
  string provider = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "Provider Ethereum address."
    }, (buf.validate.field).cel = {
      id: "provider",
      message: "provider must be a valid Ethereum address.",
      expression: "this.matches('^(0x)?[a-fA-F0-9]{40}$')"
    }];
}

message GetAllDepositsRequest {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {
    json_schema: {
      title: "GetAllDeposits request"
      description: "GetAllDeposits request."
    }
  };
}

message DepositInfo {
  string provider = 1;
  string amount = 2;
}

message GetAllDepositsResponse {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {
    json_schema: {
      title: "GetAllDeposits response"
      description: "GetAllDeposits response."
    }
  };
  repeated DepositInfo deposits = 1;
  string bidder_balance = 2;
}

message RequestWithdrawalsRequest {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {
    json_schema: {
      title: "RequestWithdrawals request"
      description: "Request withdrawals from provider(s)."
    }
  };
  repeated string providers = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = { description: "Provider Ethereum addresses." },
    (buf.validate.field).cel = {
      id: "providers",
      message: "providers must be a valid array of Ethereum addresses.",
      expression: "this.all(r, r.matches('^(0x)?[a-fA-F0-9]{40}$'))"
  }];
}

message RequestWithdrawalsResponse {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {
    json_schema: {
      title: "RequestWithdrawals response"
      description: "Request withdrawals from provider(s)."
      example: "{\"providers\": [\"******************************************\"], \"amounts\": [\"1000000000000000000\"]}"
    }
  };
  repeated string providers = 1;
  repeated string amounts = 2;
}

message WithdrawRequest {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {
    json_schema: {
      title: "Withdraw request"
      description: "Withdraw deposits from provider(s)."
    }
  };
  repeated string providers = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "Provider Ethereum addresses."
    }, (buf.validate.field).cel = {
      id: "providers",
      message: "providers must be a valid array of Ethereum addresses.",
      expression: "this.all(r, r.matches('^(0x)?[a-fA-F0-9]{40}$'))"
    }];
};

message WithdrawResponse {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {
    json_schema: {
      title: "Withdraw response"
      description: "Withdrawn deposit from the bidder registry."
    }
    example: "{\"amounts\": [\"1000000000000000000\"], \"providers\": [\"******************************************\"]}"
  };
  repeated string amounts = 1;
  repeated string providers = 2;
};

message GetValidProvidersRequest {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {
    json_schema: {
      title: "GetValidProviders request"
      description: "GetValidProviders request."
    }
  };
}

message GetValidProvidersResponse {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {
    json_schema: {
      title: "GetValidProviders response"
      description: "GetValidProviders response."
    }
  };
  repeated string valid_providers = 1;
}

message PositionConstraint {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {
    json_schema: {
      title: "Position Constraint"
      description: "Constraint on the position of the transaction in the block."
    }
  };
  enum Anchor {
    ANCHOR_UNSPECIFIED = 0;
    ANCHOR_TOP = 1; // Position is at the top of the block
    ANCHOR_BOTTOM = 2; // Position is at the bottom of the block
  }
  enum Basis {
    BASIS_UNSPECIFIED = 0;
    BASIS_PERCENTILE = 1; // Position is a percentile of the block size
    BASIS_ABSOLUTE = 2; // Position is an absolute position in the block
    BASIS_GAS_PERCENTILE = 3; // Position is a percentile of the gas used in the block
  }
  Anchor anchor = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Anchor position of the transaction in the block."
  }];
  Basis basis = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Basis for the position constraint."
  }];
  int32 value = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Value of the position constraint. If anchor is TOP, this is the position from the top of the block. If anchor is BOTTOM, this is the position from the bottom of the block."
  }];
};

message BidOption {
  oneof opt {
    PositionConstraint position_constraint = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "Position constraint for the transaction in the block."
    }];
  }
};

message BidOptions {
  repeated BidOption options = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "List of bid options for the transaction."
  }, (buf.validate.field).cel = {
      id: "options",
      message: "options must be a valid array of bid options.",
      expression: "this.all(r, r != null)"
  }];
}

message Bid {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {
    json_schema: {
      title: "Bid message"
      description: "Unsigned bid message from bidders to the bidder mev-commit node."
      required: ["amount", "block_number", "decay_start_timestamp", "decay_end_timestamp"]
    }
    example: "{\"tx_hashes\": [\"fe4cb47db3630551beedfbd02a71ecc69fd59758e2ba699606e2d5c74284ffa7\", \"71c1348f2d7ff7e814f9c3617983703435ea7446de420aeac488bf1de35737e8\"], \"amount\": \"1000000000000000000\", \"block_number\": 123456, \"decay_start_timestamp\": 1630000000, \"decay_end_timestamp\": 1630000000, \"reverting_tx_hashes\": [\"fe4cb47db3630551beedfbd02a71ecc69fd59758e2ba699606e2d5c74284ffa7\"], \"slash_amount\": \"500000000000000000\"}"
  };
  repeated string tx_hashes = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Hex string encoding of the hashes of the transactions that the bidder wants to include in the block."
    pattern: "[a-fA-F0-9]{64}"
  }, (buf.validate.field).cel = {
        id: "tx_hashes",
        message: "tx_hashes must be a valid array of transaction hashes.",
        expression: "this.all(r, r.matches('^(0x)?[a-fA-F0-9]{64}$'))"
  }];
  string amount = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Amount of ETH that the bidder is willing to pay to the provider for including the transaction in the block."
    pattern: "[0-9]+"
  }, (buf.validate.field).cel = {
      id: "amount",
      message: "amount must be a valid integer.",
      expression: "this.matches('^[1-9][0-9]*$')"
  }];
  int64 block_number = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Max block number that the bidder wants to include the transaction in."
  }, (buf.validate.field).cel = {
      id: "block_number",
      message: "block_number must be a valid integer.",
      expression: "uint(this) > 0"
  }];
  int64 decay_start_timestamp = 4 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Timestamp at which the bid starts decaying."
  }, (buf.validate.field).cel = {
      id: "decay_start_timestamp",
      message: "decay_start_timestamp must be a valid integer.",
      expression: "uint(this) > 0"
  }];
  int64 decay_end_timestamp = 5 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Timestamp at which the bid ends decaying."
  }, (buf.validate.field).cel = {
      id: "decay_end_timestamp",
      message: "decay_end_timestamp must be a valid integer.",
      expression: "uint(this) > 0"
  }];
  repeated string reverting_tx_hashes = 6 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Optional array of tx hashes that are allowed to revert or be discarded."
  }, (buf.validate.field).cel = {
      id: "reverting_tx_hashes",
      message: "reverting_tx_hashes must be an array of valid transaction hashes.",
      expression: "this.all(r, r.matches('^(0x)?[a-fA-F0-9]{64}$'))"
  }];
  repeated string raw_transactions = 7 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Optional array of RLP encoded raw signed transaction payloads that the bidder wants to include in the block."
  }, (buf.validate.field).cel = {
      id: "raw_transactions",
      message: "raw_transactions must be an array of valid raw transactions.",
      expression: "this.all(r, r.matches('^(0x)?[a-fA-F0-9]*$'))"
  }];
  string slash_amount = 8 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Amount of ETH that will be slashed from the provider if they fail to include the transaction. If zero, the decayed bid amount is used for slashing."
    pattern: "[0-9]+"
  }, (buf.validate.field).cel = {
      id: "slash_amount",
      message: "slash_amount must be a valid integer.",
      expression: "this == '' || (this.matches('^[0-9]+$') && uint(this) >= 0)"
  }];
  BidOptions bid_options = 9 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Optional bid options for the transaction."
  }];
};

message Commitment {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {
    json_schema: {
      title: "Commitment message"
      description: "Commitment message from the provider to the bidder mev-commit node."
    }
  };
  repeated string tx_hashes = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Hex string encoding of the hash of the transaction that the bidder wants to include in the block."
    pattern: "[a-fA-F0-9]{64}"
  }];
  string bid_amount = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Amount of ETH that the bidder has agreed to pay to the provider for including the transaction in the block."
  }];
  int64 block_number = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Max block number that the bidder wants to include the transaction in."
  }];
  string received_bid_digest = 4 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Hex string encoding of digest of the bid message signed by the bidder."
  }];
  string received_bid_signature = 5 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Hex string encoding of signature of the bidder that sent this bid."
  }];
  string commitment_digest = 6 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Hex string encoding of digest of the commitment."
  }];
  string commitment_signature = 7 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Hex string encoding of signature of the commitment signed by the provider confirming this transaction."
  }];
  string provider_address = 8 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Hex string encoding of the address of the provider that signed the commitment signature."
  }];
  int64 decay_start_timestamp = 9 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Timestamp at which the bid starts decaying."
  }];
  int64 decay_end_timestamp = 10 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Timestamp at which the bid ends decaying."
  }];
  int64 dispatch_timestamp = 11 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Timestamp at which the commitment is published."
  }];
  repeated string reverting_tx_hashes = 12 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Optional array of tx hashes that are allowed to revert or be discarded."
  }];
  string slash_amount = 13 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Amount of ETH that will be slashed from the provider if they fail to include the transaction."
  }];
  BidOptions bid_options = 14 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Optional bid options for the transaction."
  }];
};

message GetBidInfoRequest {
  int64 block_number = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Optional block number for querying bid info. If not specified, all known block numbers are returned in ascending order."
  }];
  int32 page = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Page number for pagination."
  }];
  int32 limit = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "Number of items per page for pagination. Default is 50"
  }];
};

message GetBidInfoResponse {
  message CommitmentWithStatus {
    string provider_address = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "Hex string encoding of the address of the provider that signed the commitment."
    }];
    int64 dispatch_timestamp = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "Timestamp at which the commitment is published."
    }];
    string status = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "Status of the commitment. Possible values: 'pending', 'stored', 'opened', 'settled', 'slashed', 'failed'."
    }];
    string details = 4 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "Additional details about the commitment status."
    }];
    string payment = 5 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "Payment amount in wei for the commitment."
    }];
    string refund = 6 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "Refund amount in wei for the commitment, if applicable."
    }];
  };
  message BidInfo {
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {
      json_schema: {
        title: "Bid Info"
        description: "Information about a bid including its commitments."
      }
    };
    repeated string txn_hashes = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "Hex string encoding of the hashes of the transactions that the bidder wants to include in the block."
      pattern: "[a-fA-F0-9]{64}"
    }];
    repeated string revertable_txn_hashes = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "Optional array of tx hashes that are allowed to revert or be discarded."
      pattern: "[a-fA-F0-9]{64}"
    }];
    int64 block_number = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "Block number that the bidder wants to include the transaction in."
    }];
    string bid_amount = 4 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "Amount of ETH that the bidder is willing to pay to the provider for including the transaction in the block."
      pattern: "[0-9]+"
    }];
    int64 decay_start_timestamp = 5 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "Timestamp at which the bid starts decaying."
    }];
    int64 decay_end_timestamp = 6 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "Timestamp at which the bid ends decaying."
    }];
    string bid_digest = 7 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "Hex string encoding of digest of the bid message signed by the bidder."
    }];
    string slash_amount = 8 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "Amount of ETH that will be slashed from the provider if they fail to include the transaction. If zero, the decayed bid amount is used for slashing."
      pattern: "[0-9]+"
    }];
    repeated CommitmentWithStatus commitments = 9;
    BidOptions bid_options = 10 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "Optional bid options for the transaction."
    }];
  };
  message BlockBidInfo {
    int64 block_number = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "Block number for which the bid info is requested."
    }];
    repeated BidInfo bids = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "List of bids for the specified block number."
    }];
  };

  repeated BlockBidInfo block_bid_info = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    description: "List of block bid info containing bids and their commitments."
  }];
};


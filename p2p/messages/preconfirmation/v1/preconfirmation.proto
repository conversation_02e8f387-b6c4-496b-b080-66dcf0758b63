syntax = "proto3";

package preconfirmation.v1;

message Bid {
  string tx_hash = 1;
  string bid_amount = 2;
  int64 block_number = 3;
  bytes digest = 4;
  bytes signature = 5;
  int64 decay_start_timestamp = 6;
  int64 decay_end_timestamp = 7;
  bytes nike_public_key = 8;
  string reverting_tx_hashes = 9;
  repeated string raw_transactions = 10;
  string slash_amount = 11;
  bytes bid_options = 12;
};

message EncryptedBid {
  bytes ciphertext = 1;
}

message PreConfirmation {
  Bid bid = 1;
  bytes digest = 2;
  bytes signature = 3;
  bytes provider_address = 4;
  bytes shared_secret = 5;
  int64 dispatch_timestamp = 6;
};

message EncryptedPreConfirmation {
  bytes commitment = 1;
  bytes signature = 2;
  bytes commitment_index = 3;
  int64 dispatch_timestamp = 4;
}

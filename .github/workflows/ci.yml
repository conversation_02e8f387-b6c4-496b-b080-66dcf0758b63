name: ci

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main
  workflow_run:
    workflows:
      - pr
    types:
      - completed

permissions:
  contents: read

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: ${{ startsWith(github.ref, 'refs/pull/') }}

jobs:
  go-modules:
    name: Test and Build Go Modules
    runs-on: ubuntu-24.04
    timeout-minutes: 60

    steps:
      - name: Checkout Code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          submodules: recursive

      - name: Setup Cache
        uses: actions/cache@v4
        with:
          path: |
            ~/go/pkg/mod
            ~/.cache/go-build
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.work.sum') }}
          restore-keys: ${{ runner.os }}-go-

      - name: Setup Go
        uses: actions/setup-go@v5
        with:
          go-version: 1.23
          check-latest: true
          cache-dependency-path: go.work.sum

      - name: Determine Modules
        run: |
          WORKSPACE_MODULES=$(go list -f '{{.Dir}}' -m)

          ADDITIONAL_MODULES=(
          "${GITHUB_WORKSPACE}/external/geth"
          )

          ALL_MODULES=$(printf "%s\n" "${WORKSPACE_MODULES}" "${ADDITIONAL_MODULES[@]}")

          echo "GO_MODULES<<EOF" >> ${GITHUB_ENV}
          echo "${ALL_MODULES}" >> ${GITHUB_ENV}
          echo "EOF" >> ${GITHUB_ENV}
          echo "GO_LINT_MODULES=$(printf "%s " $(echo "${ALL_MODULES}" | sed 's|$|/...|' | grep -v '/external/geth'))" >> ${GITHUB_ENV}

      - name: Run Gofmt
        run: |
          GOFMT_OUTPUT=$(echo ${GO_MODULES} | tr ' ' '\n' | xargs gofmt -d -e -l)
          if [ -n "${GOFMT_OUTPUT}" ]; then
            echo "The following files are not formatted correctly:"
            echo "${GOFMT_OUTPUT}"
            exit 1
          fi

      - name: Run Tidy & Workspace Sync
        run: |
          echo ${GO_MODULES} | tr ' ' '\n' | xargs -L1 go mod tidy -C
          go work sync
          git checkout ${{ github.event.pull_request.head.ref }}
          git diff --name-only --exit-code . || (echo "Golang modules/workspace not in sync with go.mod/go.sum/go.work/go.work.sum files" && exit 1)
          git reset --hard HEAD

      - name: Run Lint
        uses: golangci/golangci-lint-action@v8
        with:
          version: v2.1
          args: --timeout 15m --verbose ${{ env.GO_LINT_MODULES }}

      - name: Run Build
        run: echo ${GO_MODULES} | tr ' ' '\n' | xargs -I {} sh -c 'go build -C {} -ldflags=-checklinkname=0 -v ./...' # The -ldflags=-checklinkname=0 is required for the https://github.com/fjl/memsize module and can be removed when the module is no longer used by external/geth: https://github.com/fjl/memsize/commit/09937c23818edfe26b4f6ede71d28126c7f9657c#diff-2b7814d3fca2e99e56c51b6ff2aa313ea6e9da6424804240aa8ad891fdfe0900R9
        env:
          GOWORK: off

      - name: Run Test
        run: echo ${GO_MODULES} | tr ' ' '\n' | grep -v '/external/geth' | xargs -I {} sh -c 'go test -short -race {}/...'

      - name: Setup Protobuf
        uses: bufbuild/buf-setup-action@v1.31.0

      - name: Protobuf Version
        run: buf --version

      - name: Check Protobuf Parity
        run: |
          make bufgen
          git checkout ${{ github.event.pull_request.head.ref }}
          git diff --name-only --exit-code . || (echo "Generated files not in parity with the source files." && exit 1)
          git reset --hard HEAD
        working-directory: p2p

  foundry:
    name: Foundry Checks and Reports
    runs-on: ubuntu-24.04
    timeout-minutes: 30
    defaults:
      run:
        working-directory: contracts

    steps:
      - name: Checkout Code
        uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: Install Node.js 20
        uses: actions/setup-node@v4
        with:
          node-version: 20.18.2

      - name: Install Foundry
        uses: foundry-rs/foundry-toolchain@v1
        with:
          version: v1.2.3

      - name: Print Versions
        run: |
          git --version
          node --version
          npm --version
          forge --version

      - name: Run Tests
        run: forge clean && forge test -vvv --via-ir

      - name: Run Snapshot
        run: forge clean && forge snapshot --via-ir

  contracts:
    name: Test and Build Contracts Scripts
    runs-on: ubuntu-24.04
    timeout-minutes: 30
    defaults:
      run:
        working-directory: contracts

    steps:
      - name: Checkout Code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Install Node.js 20
        uses: actions/setup-node@v4
        with:
          node-version: 20.18.2

      - name: Install Foundry
        uses: foundry-rs/foundry-toolchain@v1
        with:
          version: nightly-e649e62f125244a3ef116be25dfdc81a2afbaf2a

      - name: Install solhint
        run: npm install -g solhint

      - name: Print Versions
        run: |
          git --version
          node --version
          npm --version
          forge --version
          solhint --version

      - name: Install abigen
        run: |
          sudo add-apt-repository -y ppa:ethereum/ethereum
          sudo apt-get update
          sudo apt-get install -y ethereum
          abigen --version

      - name: Check ABI Parity
        run: |
          bash script.sh
          git checkout ${{ github.event.pull_request.head.ref }}
          git diff --name-only --exit-code . || (echo "Generated files not in parity with the source files." && exit 1)
          git reset --hard HEAD
        working-directory: contracts-abi

      - name: Run solhint solidity linter
        run: solhint '**/*.sol'
        working-directory: contracts

  infrastructure:
    uses: ./.github/workflows/infrastructure.yml
    secrets: inherit
    needs:
      - go-modules
      - foundry
      - contracts

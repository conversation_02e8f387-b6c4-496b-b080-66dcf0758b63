package updater_test

import (
	"bytes"
	"context"
	"database/sql"
	"fmt"
	"hash"
	"io"
	"log/slog"
	"math/big"
	"os"
	"strings"
	"sync"
	"testing"
	"time"

	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/ethereum/go-ethereum/trie"
	preconf "github.com/primev/mev-commit/contracts-abi/clients/PreconfManager"
	"github.com/primev/mev-commit/oracle/pkg/updater"
	bidderapiv1 "github.com/primev/mev-commit/p2p/gen/go/bidderapi/v1"
	"github.com/primev/mev-commit/x/contracts/events"
	"github.com/primev/mev-commit/x/contracts/txmonitor"
	"github.com/primev/mev-commit/x/util"
	"golang.org/x/crypto/sha3"
	"google.golang.org/protobuf/proto"
)

func getIdxBytes(idx int64) [32]byte {
	var idxBytes [32]byte
	big.NewInt(idx).FillBytes(idxBytes[:])
	return idxBytes
}

type testBatcher struct {
	failedReceipts map[common.Hash]bool
}

func (t *testBatcher) BatchReceipts(ctx context.Context, txns []common.Hash) ([]txmonitor.Result, error) {
	var results []txmonitor.Result
	for _, txn := range txns {
		status := types.ReceiptStatusSuccessful
		if t.failedReceipts[txn] {
			status = types.ReceiptStatusFailed
		}
		results = append(results, txmonitor.Result{
			Receipt: &types.Receipt{
				TxHash:  txn,
				Status:  status,
				GasUsed: 1000000,
			},
			Err: nil,
		})
	}
	return results, nil
}

type testHasher struct {
	hasher hash.Hash
}

// NewHasher returns a new testHasher instance.
func NewHasher() *testHasher {
	return &testHasher{hasher: sha3.NewLegacyKeccak256()}
}

// Reset resets the hash state.
func (h *testHasher) Reset() {
	h.hasher.Reset()
}

// Update updates the hash state with the given key and value.
func (h *testHasher) Update(key, val []byte) error {
	h.hasher.Write(key)
	h.hasher.Write(val)
	return nil
}

// Hash returns the hash value.
func (h *testHasher) Hash() common.Hash {
	return common.BytesToHash(h.hasher.Sum(nil))
}

func TestUpdater(t *testing.T) {
	t.Parallel()

	// timestamp of the First block commitment is X
	startTimestamp := time.UnixMilli(1615195200000)
	midTimestamp := startTimestamp.Add(time.Duration(2.5 * float64(time.Second)))
	endTimestamp := startTimestamp.Add(5 * time.Second)

	key, err := crypto.GenerateKey()
	if err != nil {
		t.Fatal(err)
	}

	builderAddr := common.HexToAddress("0xabcd")
	otherBuilderAddr := common.HexToAddress("0xabdc")

	signer := types.NewLondonSigner(big.NewInt(5))
	var txns []*types.Transaction
	for i := range 10 {
		txns = append(txns, types.MustSignNewTx(key, signer, &types.DynamicFeeTx{
			Nonce:     uint64(i + 1),
			Gas:       1000000,
			Value:     big.NewInt(1),
			GasTipCap: big.NewInt(500),
			GasFeeCap: big.NewInt(500),
		}))
	}

	commitments := make([]preconf.PreconfmanagerOpenedCommitmentStored, 0)

	for i, txn := range txns {
		idxBytes := getIdxBytes(int64(i))

		commitment := preconf.PreconfmanagerOpenedCommitmentStored{
			CommitmentIndex:     idxBytes,
			TxnHash:             strings.TrimPrefix(txn.Hash().Hex(), "0x"),
			BidAmt:              big.NewInt(10),
			SlashAmt:            big.NewInt(0),
			BlockNumber:         5,
			CommitmentDigest:    common.HexToHash(fmt.Sprintf("0x%02d", i)),
			DecayStartTimeStamp: uint64(startTimestamp.UnixMilli()),
			DecayEndTimeStamp:   uint64(endTimestamp.UnixMilli()),
			DispatchTimestamp:   uint64(midTimestamp.UnixMilli()),
			RevertingTxHashes:   "",
		}

		if i%2 == 0 {
			commitment.Committer = builderAddr
			commitments = append(commitments, commitment)
		} else {
			commitment.Committer = otherBuilderAddr
			commitments = append(commitments, commitment)
		}
	}

	// constructing bundles
	for i := range 10 {
		idxBytes := getIdxBytes(int64(i + 10))

		bundle := strings.TrimPrefix(txns[i].Hash().Hex(), "0x")
		for j := i + 1; j < 10; j++ {
			bundle += "," + strings.TrimPrefix(txns[j].Hash().Hex(), "0x")
		}

		commitment := preconf.PreconfmanagerOpenedCommitmentStored{
			CommitmentIndex:     idxBytes,
			Committer:           builderAddr,
			BidAmt:              big.NewInt(10),
			SlashAmt:            big.NewInt(0),
			TxnHash:             bundle,
			BlockNumber:         5,
			CommitmentDigest:    common.HexToHash(fmt.Sprintf("0x%02d", i)),
			DecayStartTimeStamp: uint64(startTimestamp.UnixMilli()),
			DecayEndTimeStamp:   uint64(endTimestamp.UnixMilli()),
			DispatchTimestamp:   uint64(midTimestamp.UnixMilli()),
			RevertingTxHashes:   "",
		}
		commitments = append(commitments, commitment)
	}

	register := &testWinnerRegister{
		winners: []testWinner{
			{
				blockNum: 5,
				winner: updater.Winner{
					Winner: builderAddr.Bytes(),
				},
			},
		},
		settlements: make(chan testSettlement, 1),
	}

	body := &types.Body{Transactions: txns, Uncles: nil}

	l1Client := &testEVMClient{
		blocks: map[int64]*types.Block{
			5: types.NewBlock(&types.Header{}, body, []*types.Receipt{}, trie.NewStackTrie(nil)),
		},
		receipts: make(map[string]*types.Receipt),
	}
	for _, txn := range txns {
		receipt := &types.Receipt{
			Status: types.ReceiptStatusSuccessful,
			TxHash: txn.Hash(),
		}
		l1Client.receipts[txn.Hash().Hex()] = receipt
	}

	pcABI, err := abi.JSON(strings.NewReader(preconf.PreconfmanagerABI))
	if err != nil {
		t.Fatal(err)
	}

	evtMgr := events.NewListener(
		util.NewTestLogger(io.Discard),
		&pcABI,
	)

	oracle := &testOracle{
		commitments: make(chan processedCommitment, 1),
	}

	updtr, err := updater.NewUpdater(
		slog.New(slog.NewTextHandler(os.Stdout, nil)),
		l1Client,
		register,
		evtMgr,
		oracle,
		&testBatcher{},
	)
	if err != nil {
		t.Fatal(err)
	}

	ctx, cancel := context.WithCancel(context.Background())
	done := updtr.Start(ctx)

	for _, c := range commitments {
		if err := publishOpenedCommitment(evtMgr, &pcABI, c); err != nil {
			t.Fatal(err)
		}

		if c.Committer.Cmp(otherBuilderAddr) == 0 {
			continue
		}

		select {
		case <-time.After(5 * time.Second):
			t.Fatal("timeout")
		case commitment := <-oracle.commitments:
			if !bytes.Equal(commitment.commitmentIdx[:], c.CommitmentIndex[:]) {
				t.Fatal("wrong commitment index")
			}
			if commitment.blockNum.Cmp(big.NewInt(5)) != 0 {
				t.Fatal("wrong block number")
			}
			if commitment.builder != c.Committer {
				t.Fatal("wrong builder")
			}
			if commitment.isSlash {
				t.Fatal("wrong isSlash")
			}
			if commitment.residualDecay.Cmp(big.NewInt(50*updater.PRECISION)) != 0 {
				t.Fatal("wrong residual decay")
			}
		}

		select {
		case <-time.After(5 * time.Second):
			t.Fatal("timeout")
		case settlement := <-register.settlements:
			if !bytes.Equal(settlement.commitmentIdx, c.CommitmentIndex[:]) {
				t.Fatal("wrong commitment index")
			}
			if settlement.txHash != c.TxnHash {
				t.Fatal("wrong txn hash")
			}
			if settlement.blockNum != 5 {
				t.Fatal("wrong block number")
			}
			if !bytes.Equal(settlement.builder, c.Committer.Bytes()) {
				t.Fatal("wrong builder")
			}
			if settlement.amount.Uint64() != 10 {
				t.Fatal("wrong amount")
			}
			if settlement.settlementType != updater.SettlementTypeReward {
				t.Fatal("wrong settlement type")
			}
			if settlement.decayPercentage != 50*updater.PRECISION {
				t.Fatal("wrong decay percentage")
			}
		}
	}

	cancel()
	select {
	case <-done:
	case <-time.After(5 * time.Second):
		t.Fatal("timeout")
	}
}

func TestUpdaterRevertedTxns(t *testing.T) {
	t.Parallel()

	// timestamp of the First block commitment is X
	startTimestamp := time.UnixMilli(1615195200000)
	midTimestamp := startTimestamp.Add(time.Duration(2.5 * float64(time.Second)))
	endTimestamp := startTimestamp.Add(5 * time.Second)

	key, err := crypto.GenerateKey()
	if err != nil {
		t.Fatal(err)
	}

	builderAddr := common.HexToAddress("0xabcd")
	otherBuilderAddr := common.HexToAddress("0xabdc")

	signer := types.NewLondonSigner(big.NewInt(5))
	var txns []*types.Transaction
	for i := range 10 {
		txns = append(txns, types.MustSignNewTx(key, signer, &types.DynamicFeeTx{
			Nonce:     uint64(i + 1),
			Gas:       1000000,
			Value:     big.NewInt(1),
			GasTipCap: big.NewInt(500),
			GasFeeCap: big.NewInt(500),
		}))
	}

	commitments := make([]preconf.PreconfmanagerOpenedCommitmentStored, 0)

	for i, txn := range txns {
		idxBytes := getIdxBytes(int64(i))

		commitment := preconf.PreconfmanagerOpenedCommitmentStored{
			CommitmentIndex:     idxBytes,
			TxnHash:             strings.TrimPrefix(txn.Hash().Hex(), "0x"),
			BidAmt:              big.NewInt(10),
			SlashAmt:            big.NewInt(0),
			BlockNumber:         5,
			CommitmentDigest:    common.HexToHash(fmt.Sprintf("0x%02d", i)),
			DecayStartTimeStamp: uint64(startTimestamp.UnixMilli()),
			DecayEndTimeStamp:   uint64(endTimestamp.UnixMilli()),
			DispatchTimestamp:   uint64(midTimestamp.UnixMilli()),
			RevertingTxHashes:   "",
		}

		if i%2 == 0 {
			commitment.Committer = builderAddr
			commitments = append(commitments, commitment)
		} else {
			commitment.Committer = otherBuilderAddr
			commitments = append(commitments, commitment)
		}
	}

	// constructing bundles
	for i := range 10 {
		idxBytes := getIdxBytes(int64(i + 10))

		bundle := strings.TrimPrefix(txns[i].Hash().Hex(), "0x")
		for j := i + 1; j < 10; j++ {
			bundle += "," + strings.TrimPrefix(txns[j].Hash().Hex(), "0x")
		}

		commitment := preconf.PreconfmanagerOpenedCommitmentStored{
			CommitmentIndex:     idxBytes,
			Committer:           builderAddr,
			BidAmt:              big.NewInt(10),
			SlashAmt:            big.NewInt(0),
			TxnHash:             bundle,
			BlockNumber:         5,
			CommitmentDigest:    common.HexToHash(fmt.Sprintf("0x%02d", i)),
			DecayStartTimeStamp: uint64(startTimestamp.UnixMilli()),
			DecayEndTimeStamp:   uint64(endTimestamp.UnixMilli()),
			DispatchTimestamp:   uint64(midTimestamp.UnixMilli()),
			RevertingTxHashes:   "",
		}
		commitments = append(commitments, commitment)
	}

	register := &testWinnerRegister{
		winners: []testWinner{
			{
				blockNum: 5,
				winner: updater.Winner{
					Winner: builderAddr.Bytes(),
				},
			},
		},
		settlements: make(chan testSettlement, 1),
	}

	body := &types.Body{Transactions: txns, Uncles: nil}

	l1Client := &testEVMClient{
		blocks: map[int64]*types.Block{
			5: types.NewBlock(&types.Header{}, body, []*types.Receipt{}, trie.NewStackTrie(nil)),
		},
		receipts: make(map[string]*types.Receipt),
	}
	for _, txn := range txns {
		receipt := &types.Receipt{
			Status: types.ReceiptStatusFailed,
			TxHash: txn.Hash(),
		}
		l1Client.receipts[txn.Hash().Hex()] = receipt
	}

	pcABI, err := abi.JSON(strings.NewReader(preconf.PreconfmanagerABI))
	if err != nil {
		t.Fatal(err)
	}

	evtMgr := events.NewListener(
		util.NewTestLogger(io.Discard),
		&pcABI,
	)

	oracle := &testOracle{
		commitments: make(chan processedCommitment, 1),
	}
	testBatcher := &testBatcher{
		failedReceipts: make(map[common.Hash]bool),
	}
	for _, txn := range txns {
		testBatcher.failedReceipts[txn.Hash()] = true
	}

	updtr, err := updater.NewUpdater(
		slog.New(slog.NewTextHandler(io.Discard, nil)),
		l1Client,
		register,
		evtMgr,
		oracle,
		testBatcher,
	)
	if err != nil {
		t.Fatal(err)
	}

	ctx, cancel := context.WithCancel(context.Background())
	done := updtr.Start(ctx)

	for _, c := range commitments {
		if err := publishOpenedCommitment(evtMgr, &pcABI, c); err != nil {
			t.Fatal(err)
		}

		if c.Committer.Cmp(otherBuilderAddr) == 0 {
			continue
		}

		select {
		case <-time.After(5 * time.Second):
			t.Fatal("timeout")
		case commitment := <-oracle.commitments:
			if !bytes.Equal(commitment.commitmentIdx[:], c.CommitmentIndex[:]) {
				t.Fatal("wrong commitment index")
			}
			if commitment.blockNum.Cmp(big.NewInt(5)) != 0 {
				t.Fatal("wrong block number")
			}
			if commitment.builder != c.Committer {
				t.Fatal("wrong builder")
			}
			if !commitment.isSlash {
				t.Fatal("wrong isSlash")
			}
			if commitment.residualDecay.Cmp(big.NewInt(50*updater.PRECISION)) != 0 {
				t.Fatal("wrong residual decay")
			}
		}

		select {
		case <-time.After(5 * time.Second):
			t.Fatal("timeout")
		case settlement := <-register.settlements:
			if !bytes.Equal(settlement.commitmentIdx, c.CommitmentIndex[:]) {
				t.Fatal("wrong commitment index")
			}
			if settlement.txHash != c.TxnHash {
				t.Fatal("wrong txn hash")
			}
			if settlement.blockNum != 5 {
				t.Fatal("wrong block number")
			}
			if !bytes.Equal(settlement.builder, c.Committer.Bytes()) {
				t.Fatal("wrong builder")
			}
			if settlement.amount.Uint64() != 10 {
				t.Fatal("wrong amount")
			}
			if settlement.settlementType != updater.SettlementTypeSlash {
				t.Fatal("wrong settlement type")
			}
			if settlement.decayPercentage != 50*updater.PRECISION {
				t.Fatal("wrong decay percentage")
			}
		}
	}

	cancel()
	select {
	case <-done:
	case <-time.After(5 * time.Second):
		t.Fatal("timeout")
	}
}

func TestUpdaterRevertedTxnsWithRevertingHashes(t *testing.T) {
	t.Parallel()

	// timestamp of the First block commitment is X
	startTimestamp := time.UnixMilli(1615195200000)
	midTimestamp := startTimestamp.Add(time.Duration(2.5 * float64(time.Second)))
	endTimestamp := startTimestamp.Add(5 * time.Second)

	key, err := crypto.GenerateKey()
	if err != nil {
		t.Fatal(err)
	}

	builderAddr := common.HexToAddress("0xabcd")
	otherBuilderAddr := common.HexToAddress("0xabdc")

	signer := types.NewLondonSigner(big.NewInt(5))
	var txns []*types.Transaction
	for i := range 10 {
		txns = append(txns, types.MustSignNewTx(key, signer, &types.DynamicFeeTx{
			Nonce:     uint64(i + 1),
			Gas:       1000000,
			Value:     big.NewInt(1),
			GasTipCap: big.NewInt(500),
			GasFeeCap: big.NewInt(500),
		}))
	}

	commitments := make([]preconf.PreconfmanagerOpenedCommitmentStored, 0)

	for i, txn := range txns {
		idxBytes := getIdxBytes(int64(i))

		commitment := preconf.PreconfmanagerOpenedCommitmentStored{
			CommitmentIndex:     idxBytes,
			TxnHash:             strings.TrimPrefix(txn.Hash().Hex(), "0x"),
			BidAmt:              big.NewInt(10),
			SlashAmt:            big.NewInt(0),
			BlockNumber:         5,
			CommitmentDigest:    common.HexToHash(fmt.Sprintf("0x%02d", i)),
			DecayStartTimeStamp: uint64(startTimestamp.UnixMilli()),
			DecayEndTimeStamp:   uint64(endTimestamp.UnixMilli()),
			DispatchTimestamp:   uint64(midTimestamp.UnixMilli()),
			RevertingTxHashes:   strings.TrimPrefix(txn.Hash().Hex(), "0x"),
		}

		if i%2 == 0 {
			commitment.Committer = builderAddr
			commitments = append(commitments, commitment)
		} else {
			commitment.Committer = otherBuilderAddr
			commitments = append(commitments, commitment)
		}
	}

	// constructing bundles
	for i := range 10 {
		idxBytes := getIdxBytes(int64(i + 10))

		bundle := strings.TrimPrefix(txns[i].Hash().Hex(), "0x")
		for j := i + 1; j < 10; j++ {
			bundle += "," + strings.TrimPrefix(txns[j].Hash().Hex(), "0x")
		}

		commitment := preconf.PreconfmanagerOpenedCommitmentStored{
			CommitmentIndex:     idxBytes,
			Committer:           builderAddr,
			BidAmt:              big.NewInt(10),
			SlashAmt:            big.NewInt(0),
			TxnHash:             bundle,
			BlockNumber:         5,
			CommitmentDigest:    common.HexToHash(fmt.Sprintf("0x%02d", i)),
			DecayStartTimeStamp: uint64(startTimestamp.UnixMilli()),
			DecayEndTimeStamp:   uint64(endTimestamp.UnixMilli()),
			DispatchTimestamp:   uint64(midTimestamp.UnixMilli()),
			RevertingTxHashes:   bundle,
		}
		commitments = append(commitments, commitment)
	}

	register := &testWinnerRegister{
		winners: []testWinner{
			{
				blockNum: 5,
				winner: updater.Winner{
					Winner: builderAddr.Bytes(),
				},
			},
		},
		settlements: make(chan testSettlement, 1),
	}

	body := &types.Body{Transactions: txns, Uncles: nil}

	l1Client := &testEVMClient{
		blocks: map[int64]*types.Block{
			5: types.NewBlock(&types.Header{}, body, []*types.Receipt{}, trie.NewStackTrie(nil)),
		},
		receipts: make(map[string]*types.Receipt),
	}
	for _, txn := range txns {
		receipt := &types.Receipt{
			Status: types.ReceiptStatusFailed,
			TxHash: txn.Hash(),
		}
		l1Client.receipts[txn.Hash().Hex()] = receipt
	}

	pcABI, err := abi.JSON(strings.NewReader(preconf.PreconfmanagerABI))
	if err != nil {
		t.Fatal(err)
	}

	evtMgr := events.NewListener(
		util.NewTestLogger(io.Discard),
		&pcABI,
	)

	oracle := &testOracle{
		commitments: make(chan processedCommitment, 1),
	}
	testBatcher := &testBatcher{
		failedReceipts: make(map[common.Hash]bool),
	}
	for _, txn := range txns {
		testBatcher.failedReceipts[txn.Hash()] = true
	}

	updtr, err := updater.NewUpdater(
		slog.New(slog.NewTextHandler(io.Discard, nil)),
		l1Client,
		register,
		evtMgr,
		oracle,
		testBatcher,
	)
	if err != nil {
		t.Fatal(err)
	}

	ctx, cancel := context.WithCancel(context.Background())
	done := updtr.Start(ctx)

	for _, c := range commitments {
		if err := publishOpenedCommitment(evtMgr, &pcABI, c); err != nil {
			t.Fatal(err)
		}

		if c.Committer.Cmp(otherBuilderAddr) == 0 {
			continue
		}

		select {
		case <-time.After(5 * time.Second):
			t.Fatal("timeout")
		case commitment := <-oracle.commitments:
			if !bytes.Equal(commitment.commitmentIdx[:], c.CommitmentIndex[:]) {
				t.Fatal("wrong commitment index")
			}
			if commitment.blockNum.Cmp(big.NewInt(5)) != 0 {
				t.Fatal("wrong block number")
			}
			if commitment.builder != c.Committer {
				t.Fatal("wrong builder")
			}
			if commitment.isSlash {
				t.Fatal("wrong isSlash")
			}
			if commitment.residualDecay.Cmp(big.NewInt(50*updater.PRECISION)) != 0 {
				t.Fatal("wrong residual decay")
			}
		}

		select {
		case <-time.After(5 * time.Second):
			t.Fatal("timeout")
		case settlement := <-register.settlements:
			if !bytes.Equal(settlement.commitmentIdx, c.CommitmentIndex[:]) {
				t.Fatal("wrong commitment index")
			}
			if settlement.txHash != c.TxnHash {
				t.Fatal("wrong txn hash")
			}
			if settlement.blockNum != 5 {
				t.Fatal("wrong block number")
			}
			if !bytes.Equal(settlement.builder, c.Committer.Bytes()) {
				t.Fatal("wrong builder")
			}
			if settlement.amount.Uint64() != 10 {
				t.Fatal("wrong amount")
			}
			if settlement.settlementType != updater.SettlementTypeReward {
				t.Fatal("wrong settlement type")
			}
			if settlement.decayPercentage != 50*updater.PRECISION {
				t.Fatal("wrong decay percentage")
			}
		}
	}

	cancel()
	select {
	case <-done:
	case <-time.After(5 * time.Second):
		t.Fatal("timeout")
	}
}

func TestUpdaterBundlesFailure(t *testing.T) {
	t.Parallel()

	key, err := crypto.GenerateKey()
	if err != nil {
		t.Fatal(err)
	}

	startTimestamp := time.UnixMilli(1615195200000)
	midTimestamp := startTimestamp.Add(time.Duration(2.5 * float64(time.Second)))
	endTimestamp := startTimestamp.Add(5 * time.Second)

	builderAddr := common.HexToAddress("0xabcd")

	signer := types.NewLondonSigner(big.NewInt(5))
	var txns []*types.Transaction
	for i := range 10 {
		txns = append(txns, types.MustSignNewTx(key, signer, &types.DynamicFeeTx{
			Nonce:     uint64(i + 1),
			Gas:       1000000,
			Value:     big.NewInt(1),
			GasTipCap: big.NewInt(500),
			GasFeeCap: big.NewInt(500),
		}))
	}

	commitments := make([]preconf.PreconfmanagerOpenedCommitmentStored, 0)

	// constructing bundles
	for i := 1; i < 10; i++ {
		idxBytes := getIdxBytes(int64(i))

		bundle := txns[i].Hash().Hex()
		for j := 10 - i; j > 0; j-- {
			bundle += "," + txns[j].Hash().Hex()
		}

		commitment := preconf.PreconfmanagerOpenedCommitmentStored{
			CommitmentIndex:     idxBytes,
			Committer:           builderAddr,
			BidAmt:              big.NewInt(10),
			SlashAmt:            big.NewInt(0),
			TxnHash:             bundle,
			BlockNumber:         5,
			CommitmentDigest:    common.HexToHash(fmt.Sprintf("0x%02d", i)),
			DecayStartTimeStamp: uint64(startTimestamp.UnixMilli()),
			DecayEndTimeStamp:   uint64(endTimestamp.UnixMilli()),
			DispatchTimestamp:   uint64(midTimestamp.UnixMilli()),
			RevertingTxHashes:   "",
		}

		commitments = append(commitments, commitment)
	}

	register := &testWinnerRegister{
		winners: []testWinner{
			{
				blockNum: 5,
				winner: updater.Winner{
					Winner: builderAddr.Bytes(),
				},
			},
		},
		settlements: make(chan testSettlement, 1),
	}

	body := &types.Body{Transactions: txns, Uncles: nil}

	l1Client := &testEVMClient{
		blocks: map[int64]*types.Block{
			5: types.NewBlock(&types.Header{}, body, []*types.Receipt{}, trie.NewStackTrie(nil)),
		},
		receipts: make(map[string]*types.Receipt),
	}
	for _, txn := range txns {
		receipt := &types.Receipt{
			Status: types.ReceiptStatusSuccessful,
			TxHash: txn.Hash(),
		}
		l1Client.receipts[txn.Hash().Hex()] = receipt
	}

	oracle := &testOracle{
		commitments: make(chan processedCommitment, 1),
	}

	pcABI, err := abi.JSON(strings.NewReader(preconf.PreconfmanagerABI))
	if err != nil {
		t.Fatal(err)
	}

	evtMgr := events.NewListener(
		util.NewTestLogger(io.Discard),
		&pcABI,
	)

	updtr, err := updater.NewUpdater(
		slog.New(slog.NewTextHandler(io.Discard, nil)),
		l1Client,
		register,
		evtMgr,
		oracle,
		&testBatcher{},
	)
	if err != nil {
		t.Fatal(err)
	}

	ctx, cancel := context.WithCancel(context.Background())
	done := updtr.Start(ctx)

	for _, c := range commitments {
		if err := publishOpenedCommitment(evtMgr, &pcABI, c); err != nil {
			t.Fatal(err)
		}

		select {
		case <-time.After(5 * time.Second):
			t.Fatal("timeout")
		case commitment := <-oracle.commitments:
			if !bytes.Equal(commitment.commitmentIdx[:], c.CommitmentIndex[:]) {
				t.Fatal("wrong commitment index")
			}
			if commitment.blockNum.Cmp(big.NewInt(5)) != 0 {
				t.Fatal("wrong block number")
			}
			if commitment.builder != c.Committer {
				t.Fatal("wrong builder")
			}
			if !commitment.isSlash {
				t.Fatal("wrong isSlash")
			}
			if commitment.residualDecay.Cmp(big.NewInt(50*updater.PRECISION)) != 0 {
				t.Fatal("wrong residual decay")
			}
		}

		select {
		case <-time.After(5 * time.Second):
			t.Fatal("timeout")
		case settlement := <-register.settlements:
			if !bytes.Equal(settlement.commitmentIdx, c.CommitmentIndex[:]) {
				t.Fatal("wrong commitment index")
			}
			if settlement.txHash != c.TxnHash {
				t.Fatal("wrong txn hash")
			}
			if settlement.blockNum != 5 {
				t.Fatal("wrong block number")
			}
			if !bytes.Equal(settlement.builder, c.Committer.Bytes()) {
				t.Fatal("wrong builder")
			}
			if settlement.amount.Uint64() != 10 {
				t.Fatal("wrong amount")
			}
			if settlement.settlementType != updater.SettlementTypeSlash {
				t.Fatal("wrong settlement type")
			}
			if settlement.decayPercentage != 50*updater.PRECISION {
				t.Fatal("wrong decay percentage")
			}
		}
	}

	cancel()
	select {
	case <-done:
	case <-time.After(5 * time.Second):
		t.Fatal("timeout")
	}
}

func TestUpdaterIgnoreCommitments(t *testing.T) {
	t.Parallel()

	// timestamp of the First block commitment is X
	startTimestamp := time.UnixMilli(1615195200000)
	midTimestamp := startTimestamp.Add(time.Duration(2.5 * float64(time.Second)))
	endTimestamp := startTimestamp.Add(5 * time.Second)

	key, err := crypto.GenerateKey()
	if err != nil {
		t.Fatal(err)
	}

	builderAddr := common.HexToAddress("0xabcd")

	signer := types.NewLondonSigner(big.NewInt(5))
	var txns []*types.Transaction
	for i := range 10 {
		txns = append(txns, types.MustSignNewTx(key, signer, &types.DynamicFeeTx{
			Nonce:     uint64(i + 1),
			Gas:       1000000,
			Value:     big.NewInt(1),
			GasTipCap: big.NewInt(500),
			GasFeeCap: big.NewInt(500),
		}))
	}

	commitments := make([]preconf.PreconfmanagerOpenedCommitmentStored, 0)

	for i, txn := range txns {
		idxBytes := getIdxBytes(int64(i))

		// block no 5 will not be settled, so we will ignore it
		// block no 8 will not be settled as no winner is registered for it
		// block no 10 will be settled
		blockNum := uint64(5)
		if i > 5 && i < 8 {
			blockNum = 8
		} else if i >= 8 {
			blockNum = 10
		}

		commitment := preconf.PreconfmanagerOpenedCommitmentStored{
			CommitmentIndex:     idxBytes,
			Committer:           builderAddr,
			BidAmt:              big.NewInt(10),
			SlashAmt:            big.NewInt(0),
			TxnHash:             strings.TrimPrefix(txn.Hash().Hex(), "0x"),
			RevertingTxHashes:   "",
			BlockNumber:         blockNum,
			CommitmentDigest:    common.HexToHash(fmt.Sprintf("0x%02d", i)),
			DecayStartTimeStamp: uint64(startTimestamp.UnixMilli()),
			DecayEndTimeStamp:   uint64(endTimestamp.UnixMilli()),
			DispatchTimestamp:   uint64(midTimestamp.UnixMilli()),
		}

		if i == 9 {
			// duplicate commitment
			commitment.CommitmentIndex = getIdxBytes(int64(i - 1))
		}

		commitments = append(commitments, commitment)
	}

	register := &testWinnerRegister{
		winners: []testWinner{
			{
				blockNum: 10,
				winner: updater.Winner{
					Winner: builderAddr.Bytes(),
				},
			},
		},
		settlements: make(chan testSettlement, 1),
	}

	body := &types.Body{Transactions: txns, Uncles: nil}

	l1Client := &testEVMClient{
		blocks: map[int64]*types.Block{
			5:  types.NewBlock(&types.Header{}, body, []*types.Receipt{}, trie.NewStackTrie(nil)),
			8:  types.NewBlock(&types.Header{}, body, []*types.Receipt{}, trie.NewStackTrie(nil)),
			10: types.NewBlock(&types.Header{}, body, []*types.Receipt{}, trie.NewStackTrie(nil)),
		},
		receipts: make(map[string]*types.Receipt),
	}
	for _, txn := range txns {
		receipt := &types.Receipt{
			Status: types.ReceiptStatusSuccessful,
			TxHash: txn.Hash(),
		}
		l1Client.receipts[txn.Hash().Hex()] = receipt
	}

	pcABI, err := abi.JSON(strings.NewReader(preconf.PreconfmanagerABI))
	if err != nil {
		t.Fatal(err)
	}

	evtMgr := events.NewListener(
		util.NewTestLogger(io.Discard),
		&pcABI,
	)

	oracle := &testOracle{
		commitments: make(chan processedCommitment, 1),
	}

	updtr, err := updater.NewUpdater(
		slog.New(slog.NewTextHandler(os.Stdout, nil)),
		l1Client,
		register,
		evtMgr,
		oracle,
		&testBatcher{},
	)
	if err != nil {
		t.Fatal(err)
	}

	ctx, cancel := context.WithCancel(context.Background())
	done := updtr.Start(ctx)

	for i, c := range commitments {
		if err := publishOpenedCommitment(evtMgr, &pcABI, c); err != nil {
			t.Fatal(err)
		}

		if i < 8 {
			// no winner
			continue
		}

		if i == 9 {
			// duplicate commitment
			continue
		}

		select {
		case <-time.After(5 * time.Second):
			t.Fatal("timeout")
		case commitment := <-oracle.commitments:
			if !bytes.Equal(commitment.commitmentIdx[:], c.CommitmentIndex[:]) {
				t.Fatal("wrong commitment index")
			}
			if commitment.blockNum.Cmp(big.NewInt(10)) != 0 && commitment.blockNum.Cmp(big.NewInt(5)) != 0 {
				t.Fatal("wrong block number", commitment.blockNum)
			}
			if commitment.builder != c.Committer {
				t.Fatal("wrong builder")
			}
			if commitment.isSlash {
				t.Fatal("wrong isSlash")
			}
			if commitment.residualDecay.Cmp(big.NewInt(50*updater.PRECISION)) != 0 {
				t.Fatal("wrong residual decay")
			}
		}

		select {
		case <-time.After(5 * time.Second):
			t.Fatal("timeout")
		case settlement := <-register.settlements:
			if !bytes.Equal(settlement.commitmentIdx, c.CommitmentIndex[:]) {
				t.Fatal("wrong commitment index")
			}
			if settlement.txHash != c.TxnHash {
				t.Fatal("wrong txn hash")
			}
			if settlement.blockNum != 10 && settlement.blockNum != 5 {
				t.Fatal("wrong block number")
			}
			if !bytes.Equal(settlement.builder, c.Committer.Bytes()) {
				t.Fatal("wrong builder")
			}
			if settlement.amount.Uint64() != 10 {
				t.Fatal("wrong amount")
			}
			if settlement.settlementType != updater.SettlementTypeReward {
				t.Fatal("wrong settlement type")
			}
			if settlement.decayPercentage != 50*updater.PRECISION {
				t.Fatal("wrong decay percentage")
			}
		}
	}

	cancel()
	select {
	case <-done:
	case <-time.After(5 * time.Second):
		t.Fatal("timeout")
	}
}

func TestComputeResidualAfterDecay(t *testing.T) {
	t.Parallel()

	discardLogger := slog.New(slog.NewTextHandler(io.Discard, nil))

	u, err := updater.NewUpdater(
		discardLogger,
		nil,
		nil,
		nil,
		nil,
		nil,
	)
	if err != nil {
		// The current NewUpdater only returns error on cache creation failure, unlikely here.
		t.Fatalf("Failed to create minimal updater instance for test: %v", err)
	}

	tests := []struct {
		name   string
		start  uint64
		end    uint64
		commit uint64
		want   *big.Int
	}{
		{
			name:   "Commit Before Start",
			start:  1000,
			end:    2000,
			commit: 500,
			want:   updater.BigOneHundredPercent,
		},
		{
			name:   "Commit At Start",
			start:  1000,
			end:    2000,
			commit: 1000,
			want:   updater.BigOneHundredPercent,
		},
		{
			name:   "Commit After End",
			start:  1000,
			end:    2000,
			commit: 2500,
			want:   big.NewInt(0),
		},
		{
			name:   "Commit At End",
			start:  1000,
			end:    2000,
			commit: 2000,
			want:   big.NewInt(0),
		},
		{
			name:   "Invalid Range: Start Equals End",
			start:  1000,
			end:    1000,
			commit: 1000,
			want:   big.NewInt(0),
		},
		{
			name:   "Invalid Range: Start Greater Than End",
			start:  2000,
			end:    1000,
			commit: 1500,
			want:   big.NewInt(0),
		},
		{
			name:   "Commit Exactly Midpoint",
			start:  1000,
			end:    2000,                               // duration 1000
			commit: 1500,                               // 500 passed
			want:   big.NewInt(50 * updater.PRECISION), // 1 - 500/1000 = 0.5 -> 50%
		},
		{
			name:   "Commit At 25% Time Passed",
			start:  1000,
			end:    2000,                               // duration 1000
			commit: 1250,                               // 250 passed
			want:   big.NewInt(75 * updater.PRECISION), // 1 - 250/1000 = 0.75 -> 75%
		},
		{
			name:   "Commit At 75% Time Passed",
			start:  1000,
			end:    2000,                               // duration 1000
			commit: 1750,                               // 750 passed
			want:   big.NewInt(25 * updater.PRECISION), // 1 - 750/1000 = 0.25 -> 25%
		},
		{
			name:   "Commit Very Close To Start",
			start:  100000,
			end:    200000, // duration 100000
			commit: 100001, // 1 passed
			// residual = 1 - 1/100000 = 0.99999
			// percentage = 0.99999 * 100 = 99.999
			// scaled = 99.999 * PRECISION
			// Expected float: (1.0 - (1.0 / 100000.0)) * ONE_HUNDRED_PERCENT
			// Expected float: 0.99999 * 100 * 1e16 = 99.999 * 1e16 = 999990000000000000
			want: big.NewInt(999990000000000000),
		},
		{
			name:   "Commit Very Close To End",
			start:  100000,
			end:    200000, // duration 100000
			commit: 199000, // 99000 passed
			// residual = 1 - 99000/100000 = 1 - 0.99 = 0.01
			// scaled = 0.01 * 100 * PRECISION = 1 * PRECISION
			want: big.NewInt(1 * int64(updater.PRECISION)),
		},
		{
			name:   "Zero Start Time",
			start:  0,
			end:    1000,                               // duration 1000
			commit: 500,                                // 500 passed
			want:   big.NewInt(50 * updater.PRECISION), // 1 - 500/1000 = 0.5 -> 50%
		},
		{
			name:   "Zero Start and Commit Time",
			start:  0,
			end:    1000,
			commit: 0,
			want:   updater.BigOneHundredPercent,
		},
		{
			name:   "Large Timestamps",
			start:  1700000000000, // example ms timestamps
			end:    1700000012000, // 12 second duration
			commit: 1700000003000, // 3 seconds passed
			// residual = 1 - 3000/12000 = 1 - 0.25 = 0.75
			// scaled = 0.75 * 100 * PRECISION = 75 * PRECISION
			want: big.NewInt(75 * updater.PRECISION),
		},
		{
			name:   "Minimal Valid Duration",
			start:  1000,
			end:    1001, // duration 1
			commit: 1000,
			want:   updater.BigOneHundredPercent,
		},
		{
			name:   "Minimal Valid Duration, Commit slightly after start",
			start:  1000,
			end:    1002, // duration 2
			commit: 1001, // passed 1
			// residual = 1 - 1/2 = 0.5
			want: big.NewInt(50 * updater.PRECISION),
		},
	}

	for _, tc := range tests {
		tc := tc
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()
			got := u.ComputeResidualAfterDecay(tc.start, tc.end, tc.commit)

			if got.Cmp(tc.want) != 0 {
				t.Errorf("ComputeResidualAfterDecay(%d, %d, %d) = %v, want %v", tc.start, tc.end, tc.commit, got, tc.want)
			}
		})
	}
}

type testSettlement struct {
	commitmentIdx   []byte
	txHash          string
	blockNum        int64
	builder         []byte
	amount          *big.Int
	settlementType  updater.SettlementType
	decayPercentage int64
	chainhash       []byte
	nonce           uint64
	opts            []byte
}

type testWinner struct {
	blockNum int64
	winner   updater.Winner
}

type testWinnerRegister struct {
	mu              sync.Mutex
	winners         []testWinner
	setttlementIdxs [][]byte
	settlements     chan testSettlement
}

func (t *testWinnerRegister) IsSettled(ctx context.Context, commitmentIdx []byte) (bool, error) {
	t.mu.Lock()
	defer t.mu.Unlock()

	for _, idx := range t.setttlementIdxs {
		if bytes.Equal(idx, commitmentIdx) {
			return true, nil
		}
	}
	return false, nil
}

func (t *testWinnerRegister) GetWinner(ctx context.Context, blockNum int64) (updater.Winner, error) {
	for _, w := range t.winners {
		if w.blockNum == blockNum {
			return w.winner, nil
		}
	}
	return updater.Winner{}, sql.ErrNoRows
}

func (t *testWinnerRegister) AddSettlement(
	ctx context.Context,
	commitmentIdx []byte,
	txHash string,
	blockNum int64,
	amount *big.Int,
	builder []byte,
	_ []byte,
	settlementType updater.SettlementType,
	decayPercentage int64,
	chainhash []byte,
	nonce uint64,
	opts []byte,
) error {
	t.mu.Lock()
	t.setttlementIdxs = append(t.setttlementIdxs, commitmentIdx)
	t.mu.Unlock()

	t.settlements <- testSettlement{
		commitmentIdx:   commitmentIdx,
		txHash:          txHash,
		blockNum:        blockNum,
		amount:          amount,
		builder:         builder,
		settlementType:  settlementType,
		decayPercentage: decayPercentage,
		chainhash:       chainhash,
		nonce:           nonce,
		opts:            opts,
	}
	return nil
}

type testEVMClient struct {
	blocks   map[int64]*types.Block
	receipts map[string]*types.Receipt
}

func (t *testEVMClient) BlockByNumber(ctx context.Context, blkNum *big.Int) (*types.Block, error) {
	blk, found := t.blocks[blkNum.Int64()]
	if !found {
		return nil, fmt.Errorf("block %d not found", blkNum.Int64())
	}
	return blk, nil
}

type processedCommitment struct {
	commitmentIdx [32]byte
	blockNum      *big.Int
	builder       common.Address
	isSlash       bool
	residualDecay *big.Int
}

type testOracle struct {
	commitments chan processedCommitment
}

func (t *testOracle) ProcessBuilderCommitmentForBlockNumber(
	commitmentIdx [32]byte,
	blockNum *big.Int,
	builder common.Address,
	isSlash bool,
	residualDecay *big.Int,
) (*types.Transaction, error) {
	t.commitments <- processedCommitment{
		commitmentIdx: commitmentIdx,
		blockNum:      blockNum,
		builder:       builder,
		isSlash:       isSlash,
		residualDecay: residualDecay,
	}
	return types.NewTransaction(0, common.Address{}, nil, 0, nil, nil), nil
}

func publishOpenedCommitment(
	evtMgr events.EventManager,
	pcABI *abi.ABI,
	c preconf.PreconfmanagerOpenedCommitmentStored,
) error {
	event := pcABI.Events["OpenedCommitmentStored"]
	buf, err := event.Inputs.NonIndexed().Pack(
		c.Bidder,
		c.Committer,
		c.BidAmt,
		c.SlashAmt,
		c.BlockNumber,
		c.DecayStartTimeStamp,
		c.DecayEndTimeStamp,
		c.TxnHash,
		c.RevertingTxHashes,
		c.CommitmentDigest,
		c.DispatchTimestamp,
		c.BidOptions,
	)
	if err != nil {
		return err
	}

	commitmentIndex := common.BytesToHash(c.CommitmentIndex[:])

	// Creating a Log object
	testLog := types.Log{
		Topics: []common.Hash{
			event.ID,        // The first topic is the hash of the event signature
			commitmentIndex, // The next topics are the indexed event parameters
		},
		// Since there are no non-indexed parameters, Data is empty
		Data: buf,
	}

	evtMgr.PublishLogEvent(context.Background(), testLog)
	return nil
}

func TestBidOptions(t *testing.T) {
	t.Parallel()

	// timestamp of the First block commitment is X
	startTimestamp := time.UnixMilli(1615195200000)
	midTimestamp := startTimestamp.Add(time.Duration(2.5 * float64(time.Second)))
	endTimestamp := startTimestamp.Add(5 * time.Second)

	key, err := crypto.GenerateKey()
	if err != nil {
		t.Fatal(err)
	}

	builderAddr := common.HexToAddress("0xabcd")
	otherBuilderAddr := common.HexToAddress("0xabdc")

	signer := types.NewLondonSigner(big.NewInt(5))
	var txns []*types.Transaction
	for i := range 10 {
		txns = append(txns, types.MustSignNewTx(key, signer, &types.DynamicFeeTx{
			Nonce:     uint64(i + 1),
			Gas:       1000000,
			Value:     big.NewInt(1),
			GasTipCap: big.NewInt(500),
			GasFeeCap: big.NewInt(500),
		}))
	}

	top20Option := &bidderapiv1.BidOptions{
		Options: []*bidderapiv1.BidOption{
			{
				Opt: &bidderapiv1.BidOption_PositionConstraint{
					PositionConstraint: &bidderapiv1.PositionConstraint{
						Anchor: bidderapiv1.PositionConstraint_ANCHOR_TOP,
						Basis:  bidderapiv1.PositionConstraint_BASIS_PERCENTILE,
						Value:  20,
					},
				},
			},
		},
	}

	bottom20Option := &bidderapiv1.BidOptions{
		Options: []*bidderapiv1.BidOption{
			{
				Opt: &bidderapiv1.BidOption_PositionConstraint{
					PositionConstraint: &bidderapiv1.PositionConstraint{
						Anchor: bidderapiv1.PositionConstraint_ANCHOR_BOTTOM,
						Basis:  bidderapiv1.PositionConstraint_BASIS_PERCENTILE,
						Value:  20,
					},
				},
			},
		},
	}

	absolute5thOption := &bidderapiv1.BidOptions{
		Options: []*bidderapiv1.BidOption{
			{
				Opt: &bidderapiv1.BidOption_PositionConstraint{
					PositionConstraint: &bidderapiv1.PositionConstraint{
						Anchor: bidderapiv1.PositionConstraint_ANCHOR_TOP,
						Basis:  bidderapiv1.PositionConstraint_BASIS_ABSOLUTE,
						Value:  5,
					},
				},
			},
		},
	}

	abosulte2ndLastOption := &bidderapiv1.BidOptions{
		Options: []*bidderapiv1.BidOption{
			{
				Opt: &bidderapiv1.BidOption_PositionConstraint{
					PositionConstraint: &bidderapiv1.PositionConstraint{
						Anchor: bidderapiv1.PositionConstraint_ANCHOR_BOTTOM,
						Basis:  bidderapiv1.PositionConstraint_BASIS_ABSOLUTE,
						Value:  2,
					},
				},
			},
		},
	}

	gasLimit50Option := &bidderapiv1.BidOptions{
		Options: []*bidderapiv1.BidOption{
			{
				Opt: &bidderapiv1.BidOption_PositionConstraint{
					PositionConstraint: &bidderapiv1.PositionConstraint{
						Anchor: bidderapiv1.PositionConstraint_ANCHOR_TOP,
						Basis:  bidderapiv1.PositionConstraint_BASIS_GAS_PERCENTILE,
						Value:  50,
					},
				},
			},
		},
	}

	commitments := make([]preconf.PreconfmanagerOpenedCommitmentStored, 0)

	for i, txn := range txns {
		idxBytes := getIdxBytes(int64(i))

		commitment := preconf.PreconfmanagerOpenedCommitmentStored{
			CommitmentIndex:     idxBytes,
			TxnHash:             strings.TrimPrefix(txn.Hash().Hex(), "0x"),
			BidAmt:              big.NewInt(10),
			SlashAmt:            big.NewInt(0),
			BlockNumber:         5,
			CommitmentDigest:    common.HexToHash(fmt.Sprintf("0x%02d", i)),
			DecayStartTimeStamp: uint64(startTimestamp.UnixMilli()),
			DecayEndTimeStamp:   uint64(endTimestamp.UnixMilli()),
			DispatchTimestamp:   uint64(midTimestamp.UnixMilli()),
			RevertingTxHashes:   "",
			Committer:           builderAddr,
		}

		if i%2 == 0 {
			// use valid option
			if i < 3 {
				commitment.BidOptions, err = proto.Marshal(top20Option)
				if err != nil {
					t.Fatal(err)
				}
			} else if i == 4 {
				commitment.BidOptions, err = proto.Marshal(absolute5thOption)
				if err != nil {
					t.Fatal(err)
				}
			} else if i == 8 {
				commitment.BidOptions, err = proto.Marshal(bottom20Option)
				if err != nil {
					t.Fatal(err)
				}
			}
		} else {
			// use incorrect option
			if i < 9 {
				commitment.BidOptions, err = proto.Marshal(abosulte2ndLastOption)
				if err != nil {
					t.Fatal(err)
				}
			} else {
				commitment.BidOptions, err = proto.Marshal(gasLimit50Option)
				if err != nil {
					t.Fatal(err)
				}
			}
		}
		commitments = append(commitments, commitment)
	}

	// constructing bundles
	for i := range 10 {
		idxBytes := getIdxBytes(int64(i + 10))

		bundle := strings.TrimPrefix(txns[i].Hash().Hex(), "0x")
		for j := i + 1; j < 10; j++ {
			bundle += "," + strings.TrimPrefix(txns[j].Hash().Hex(), "0x")
		}

		commitment := preconf.PreconfmanagerOpenedCommitmentStored{
			CommitmentIndex:     idxBytes,
			Committer:           builderAddr,
			BidAmt:              big.NewInt(10),
			SlashAmt:            big.NewInt(0),
			TxnHash:             bundle,
			BlockNumber:         5,
			CommitmentDigest:    common.HexToHash(fmt.Sprintf("0x%02d", i)),
			DecayStartTimeStamp: uint64(startTimestamp.UnixMilli()),
			DecayEndTimeStamp:   uint64(endTimestamp.UnixMilli()),
			DispatchTimestamp:   uint64(midTimestamp.UnixMilli()),
			RevertingTxHashes:   "",
		}
		commitments = append(commitments, commitment)
	}

	register := &testWinnerRegister{
		winners: []testWinner{
			{
				blockNum: 5,
				winner: updater.Winner{
					Winner: builderAddr.Bytes(),
				},
			},
		},
		settlements: make(chan testSettlement, 1),
	}

	body := &types.Body{Transactions: txns, Uncles: nil}

	l1Client := &testEVMClient{
		blocks: map[int64]*types.Block{
			5: types.NewBlock(
				&types.Header{GasUsed: 10 * 1000000},
				body,
				[]*types.Receipt{},
				trie.NewStackTrie(nil),
			),
		},
		receipts: make(map[string]*types.Receipt),
	}
	for _, txn := range txns {
		receipt := &types.Receipt{
			Status:  types.ReceiptStatusSuccessful,
			TxHash:  txn.Hash(),
			GasUsed: 1000000,
		}
		l1Client.receipts[txn.Hash().Hex()] = receipt
	}

	pcABI, err := abi.JSON(strings.NewReader(preconf.PreconfmanagerABI))
	if err != nil {
		t.Fatal(err)
	}

	evtMgr := events.NewListener(
		util.NewTestLogger(io.Discard),
		&pcABI,
	)

	oracle := &testOracle{
		commitments: make(chan processedCommitment, 1),
	}

	updtr, err := updater.NewUpdater(
		slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{Level: slog.LevelDebug})),
		l1Client,
		register,
		evtMgr,
		oracle,
		&testBatcher{},
	)
	if err != nil {
		t.Fatal(err)
	}

	ctx, cancel := context.WithCancel(context.Background())
	done := updtr.Start(ctx)

	for idx, c := range commitments {
		if err := publishOpenedCommitment(evtMgr, &pcABI, c); err != nil {
			t.Fatal(err)
		}

		if c.Committer.Cmp(otherBuilderAddr) == 0 {
			continue
		}

		select {
		case <-time.After(5 * time.Second):
			t.Fatal("timeout")
		case commitment := <-oracle.commitments:
			if !bytes.Equal(commitment.commitmentIdx[:], c.CommitmentIndex[:]) {
				t.Fatal("wrong commitment index")
			}
			if commitment.blockNum.Cmp(big.NewInt(5)) != 0 {
				t.Fatal("wrong block number")
			}
			if commitment.builder != c.Committer {
				t.Fatal("wrong builder")
			}
			if idx%2 == 0 && commitment.isSlash {
				t.Fatal("wrong isSlash")
			}
			if (idx%2 == 1 && idx < 10) && !commitment.isSlash {
				t.Fatal("wrong isSlash")
			}
			if commitment.residualDecay.Cmp(big.NewInt(50*updater.PRECISION)) != 0 {
				t.Fatal("wrong residual decay")
			}
		}

		select {
		case <-time.After(5 * time.Second):
			t.Fatal("timeout")
		case settlement := <-register.settlements:
			if !bytes.Equal(settlement.commitmentIdx, c.CommitmentIndex[:]) {
				t.Fatal("wrong commitment index")
			}
			if settlement.txHash != c.TxnHash {
				t.Fatal("wrong txn hash")
			}
			if settlement.blockNum != 5 {
				t.Fatal("wrong block number")
			}
			if !bytes.Equal(settlement.builder, c.Committer.Bytes()) {
				t.Fatal("wrong builder")
			}
			if settlement.amount.Uint64() != 10 {
				t.Fatal("wrong amount")
			}
			if idx%2 == 0 && settlement.settlementType != updater.SettlementTypeReward {
				t.Fatal("wrong settlement type")
			}
			if (idx%2 == 1 && idx < 10) && settlement.settlementType != updater.SettlementTypeSlash {
				t.Fatal("wrong settlement type")
			}
			if settlement.decayPercentage != 50*updater.PRECISION {
				t.Fatal("wrong decay percentage")
			}
		}
	}

	cancel()
	select {
	case <-done:
	case <-time.After(5 * time.Second):
		t.Fatal("timeout")
	}
}

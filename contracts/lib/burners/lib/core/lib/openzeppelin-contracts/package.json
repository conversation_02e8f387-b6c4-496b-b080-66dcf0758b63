{"name": "openzeppelin-solidity", "description": "Secure Smart Contract library for Solidity", "version": "5.0.2", "private": true, "files": ["/contracts/**/*.sol", "!/contracts/mocks/**/*"], "scripts": {"compile": "hardhat compile", "coverage": "env COVERAGE=true FOUNDRY=false hardhat coverage", "docs": "npm run prepare-docs && oz-docs", "docs:watch": "oz-docs watch contracts docs/templates docs/config.js", "prepare-docs": "scripts/prepare-docs.sh", "lint": "npm run lint:js && npm run lint:sol", "lint:fix": "npm run lint:js:fix && npm run lint:sol:fix", "lint:js": "prettier --log-level warn --ignore-path .gitignore '**/*.{js,ts}' --check && eslint --ignore-path .gitignore .", "lint:js:fix": "prettier --log-level warn --ignore-path .gitignore '**/*.{js,ts}' --write && eslint --ignore-path .gitignore . --fix", "lint:sol": "prettier --log-level warn --ignore-path .gitignore '{contracts,test}/**/*.sol' --check && solhint '{contracts,test}/**/*.sol'", "lint:sol:fix": "prettier --log-level warn --ignore-path .gitignore '{contracts,test}/**/*.sol' --write", "clean": "hardhat clean && rimraf build contracts/build", "prepack": "scripts/prepack.sh", "generate": "scripts/generate/run.js", "release": "scripts/release/release.sh", "version": "scripts/release/version.sh", "test": "hardhat test", "test:inheritance": "scripts/checks/inheritance-ordering.js artifacts/build-info/*", "test:generation": "scripts/checks/generation.sh", "gas-report": "env ENABLE_GAS_REPORT=true npm run test", "slither": "npm run clean && slither ."}, "repository": {"type": "git", "url": "https://github.com/OpenZeppelin/openzeppelin-contracts.git"}, "keywords": ["solidity", "ethereum", "smart", "contracts", "security", "zeppelin"], "author": "OpenZeppelin Community <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/OpenZeppelin/openzeppelin-contracts/issues"}, "homepage": "https://openzeppelin.com/contracts/", "devDependencies": {"@changesets/changelog-github": "^0.4.8", "@changesets/cli": "^2.26.0", "@changesets/pre": "^1.0.14", "@changesets/read": "^0.5.9", "@nomicfoundation/hardhat-foundry": "^1.1.1", "@nomicfoundation/hardhat-network-helpers": "^1.0.3", "@nomiclabs/hardhat-truffle5": "^2.0.5", "@nomiclabs/hardhat-web3": "^2.0.0", "@openzeppelin/docs-utils": "^0.1.5", "@openzeppelin/test-helpers": "^0.5.13", "@openzeppelin/upgrade-safe-transpiler": "^0.3.32", "@openzeppelin/upgrades-core": "^1.20.6", "array.prototype.at": "^1.1.1", "chai": "^4.2.0", "eslint": "^8.30.0", "eslint-config-prettier": "^9.0.0", "eth-sig-util": "^3.0.0", "ethereumjs-util": "^7.0.7", "ethereumjs-wallet": "^1.0.1", "glob": "^10.3.5", "graphlib": "^2.1.8", "hardhat": "^2.9.1", "hardhat-exposed": "^0.3.13", "hardhat-gas-reporter": "^1.0.9", "hardhat-ignore-warnings": "^0.2.0", "keccak256": "^1.0.2", "lodash.startcase": "^4.4.0", "lodash.zip": "^4.2.0", "merkletreejs": "^0.2.13", "micromatch": "^4.0.2", "p-limit": "^3.1.0", "prettier": "^3.0.0", "prettier-plugin-solidity": "^1.1.0", "rimraf": "^5.0.1", "semver": "^7.3.5", "solhint": "^3.3.6", "solhint-plugin-openzeppelin": "file:scripts/solhint-custom", "solidity-ast": "^0.4.50", "solidity-coverage": "^0.8.5", "solidity-docgen": "^0.6.0-beta.29", "undici": "^5.22.1", "web3": "^1.3.0", "yargs": "^17.0.0"}}
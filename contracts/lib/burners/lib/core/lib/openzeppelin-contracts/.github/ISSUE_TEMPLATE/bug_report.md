---
name: Bug report
about: Report a bug in OpenZeppelin Contracts

---

<!-- Briefly describe the issue you're experiencing. Tell us what you were trying to do and what happened instead. -->

<!-- Remember, this is not a place to ask for help debugging code. For that, we welcome you in the OpenZeppelin Community Forum: https://forum.openzeppelin.com/. -->

**💻 Environment**

<!-- Tell us what version of OpenZeppelin Contracts you're using, and how you're using it: Truffle, Remix, etc. -->

**📝 Details**

<!-- Describe the problem you have been experiencing in more detail. Include as much information as you think is relevant. Keep in mind that transactions can fail for many reasons; context is key here. -->

**🔢 Code to reproduce bug**

<!-- We will be able to better help if you provide a minimal example that triggers the bug. -->

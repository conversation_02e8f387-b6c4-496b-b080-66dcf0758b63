// SPDX-License-Identifier: BSL 1.1

// solhint-disable no-console
// solhint-disable one-contract-per-file

pragma solidity 0.8.26;

/// @notice Constants from https://github.com/Layr-Labs/eigenlayer-contracts?tab=readme-ov-file#deployments,
/// @notice Last updated 11-07-2024
library EigenMainnetReleaseConsts {

    address internal constant DELEGATION_MANAGER = 0x39053D51B77DC0d36036Fc1fCc8Cb819df8Ef37A;
    address internal constant STRATEGY_MANAGER = ******************************************;
    address internal constant EIGENPOD_MANAGER = ******************************************;
    address internal constant AVS_DIRECTORY = ******************************************;
    address internal constant SLASHER = ******************************************;
    address internal constant REWARDS_COORDINATOR = ******************************************;

    address internal constant STRATEGY_BASE_CBETH = ******************************************;
    address internal constant STRATEGY_BASE_STETH = ******************************************;
    address internal constant STRATEGY_BASE_RETH = ******************************************;
    address internal constant STRATEGY_BASE_ETHX = ******************************************;
    address internal constant STRATEGY_BASE_ANKRETH = ******************************************;
    address internal constant STRATEGY_BASE_OETH = ******************************************;
    address internal constant STRATEGY_BASE_OSETH = ******************************************;
    address internal constant STRATEGY_BASE_SWETH = ******************************************;
    address internal constant STRATEGY_BASE_WBETH = ******************************************;
    address internal constant STRATEGY_BASE_SFRXETH = ******************************************;
    address internal constant STRATEGY_BASE_LSETH = ******************************************;
    address internal constant STRATEGY_BASE_METH = ******************************************;
    address internal constant BEACON_CHAIN_ETH = ******************************************;
}

/// @notice Constants from https://github.com/Layr-Labs/eigenlayer-contracts?tab=readme-ov-file#current-testnet-deployment
/// @notice Last updated 07-26-2024
library EigenHoleskyReleaseConsts {

    address internal constant DELEGATION_MANAGER = ******************************************;
    address internal constant STRATEGY_MANAGER = ******************************************;
    address internal constant EIGENPOD_MANAGER = ******************************************;
    address internal constant AVS_DIRECTORY = ******************************************;
    address internal constant SLASHER = ******************************************;
    address internal constant REWARDS_COORDINATOR = ******************************************;

    address internal constant STRATEGY_BASE_STETH = ******************************************;
    address internal constant STRATEGY_BASE_RETH = ******************************************;
    address internal constant STRATEGY_BASE_WETH = ******************************************;
    address internal constant STRATEGY_BASE_LSETH = ******************************************;
    address internal constant STRATEGY_BASE_SFRXETH = ******************************************;
    address internal constant STRATEGY_BASE_ETHX = ******************************************;
    address internal constant STRATEGY_BASE_OSETH = ******************************************;
    address internal constant STRATEGY_BASE_CBETH = ******************************************;
    address internal constant STRATEGY_BASE_METH = ******************************************;
    address internal constant STRATEGY_BASE_ANKRETH = ******************************************;
    address internal constant BEACON_CHAIN_ETH = ******************************************;
}

/// @notice Constants from https://github.com/Layr-Labs/eigenlayer-contracts?tab=readme-ov-file#current-testnet-deployment
/// @notice Last updated 07-25-2025 — for HOODI testnet
library EigenHoodiReleaseConsts {
    // Core
    address internal constant DELEGATION_MANAGER     = ******************************************;
    address internal constant STRATEGY_MANAGER       = ******************************************;
    address internal constant EIGENPOD_MANAGER       = ******************************************;
    address internal constant AVS_DIRECTORY          = ******************************************;
    address internal constant REWARDS_COORDINATOR    = ******************************************;
    address internal constant ALLOCATION_MANAGER     = ******************************************;
    address internal constant PERMISSION_CONTROLLER  = ******************************************;

    // Strategies - Deployed via StrategyFactory
    address internal constant STRATEGY_FACTORY       = ******************************************;
    address internal constant STRATEGY_BASE          = ******************************************;
    address internal constant STRATEGY_BASE_STETH    = ******************************************;
    address internal constant STRATEGY_BASE_WETH     = ******************************************;
    // Special strategies
    address internal constant STRATEGY_BASE_EIGEN    = ******************************************;
    // Beacon Chain ETH placeholder (not a real contract)
    address internal constant BEACON_CHAIN_ETH       = ******************************************;
    // Tokens
    address internal constant EIGEN_TOKEN            = ******************************************;
    address internal constant BACKING_EIGEN          = ******************************************;
}

version: '3.8'

services:
  postgres:
    image: postgres:17
    container_name: mev-commit-postgres
    environment:
      POSTGRES_DB: mevcommit
      POSTGRES_USER: mevcommit
      POSTGRES_PASSWORD: password123
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    networks:
      - mev-commit-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U mevcommit -d mevcommit"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    restart: unless-stopped
    command: >
      postgres

volumes:
  postgres_data:
    driver: local
  pgadmin_data:
    driver: local

networks:
  mev-commit-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

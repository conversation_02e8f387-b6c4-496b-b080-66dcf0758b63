#jinja2: trim_blocks:True, lstrip_blocks:True
job "{{ job.name }}" {
  datacenters = ["{{ datacenter }}"]

  group "{{ job.name }}-group" {
    count = {{ job.count }}

    update {
      healthy_deadline = "25m"
      progress_deadline = "35m"
    }

    {% if env == 'devenv' %}
    restart {
      attempts = 0
      mode = "fail"
    }

    reschedule {
      attempts = 0
      unlimited = false
    }
    {% endif %}

    network {
      dns {
        servers = {{ (ansible_facts['dns']['nameservers'] + ['*******']) | tojson }}
      }

      {% for port_name, port_details in job.ports[0].items() %}
      port "{{ port_name }}" {
        {% if port_details.get('static') %}
        static = {{ port_details['static'] }}
        {% endif %}
        {% if port_details.get('to') %}
        to = {{ port_details['to'] }}
        {% endif %}
      }
      {% endfor %}
    }

    volume "data-volume" {
      type = "host"
      source = "data-volume"
      read_only = false
    }

    task "contracts" {
      driver = "exec"

      {% if profile == 'testnet' or profile == 'mainnet' %}
      resources {
        cpu = 4000
        memory = 2048
      }
      {% elif profile == 'stressnet' or profile == 'stressnet-wl1' %}
      resources {
        cpu = 2000
        memory = 1024
      }
      {% endif %}

      volume_mount {
        volume = "data-volume"
        destination = "/local/data"
        read_only = false
      }

      {% for port_name in job.ports[0] %}
      service {
        name = "{{ job.name }}"
        port = "{{ port_name }}"
        tags = ["{{ port_name }}"]
        provider = "nomad"

        check {
          type = "http"
          path = "/"
          port = "{{ port_name }}"
          interval = "10s"
          timeout  = "5s"
        }
      }
      {% endfor %}

      artifact {
        source = "https://nodejs.org/dist/v20.18.2/node-v20.18.2-linux-{{ 'x64' if target_system_architecture == 'x86_64' else 'arm64' }}.tar.xz"
        options {
          archive = false
        }
      }

      artifact {
        source = "https://foundry.paradigm.xyz"
        destination = "local/foundry.sh"
      }

      {% if env != 'devenv' %}
      artifact {
        source = "https://primev-infrastructure-artifacts.s3.us-west-2.amazonaws.com/contracts_{{ version }}.tar.gz"
      }
      {% else %}
      artifact {
        source = "http://{{ ansible_facts['default_ipv4']['address'] }}:1111/contracts_{{ version }}.tar.gz"
      }
      {% endif %}

      template {
        data = <<-EOH
          L1_FINALIZATION_FEE="5000000000000000"
          SETTLEMENT_FINALIZATION_FEE="1"
          XDG_CONFIG_HOME="local/.config"
          {% raw %}
          KEYSTORE_DIR="/local/data-{{ env "NOMAD_ALLOC_INDEX" }}/keystore"
          KEYSTORE_FILENAME="{{ with secret "secret/data/mev-commit" }}{{ .Data.data.{% endraw %}{{ job.artifacts | selectattr('keystores', 'defined') | map(attribute='keystores') | first | list | first }}{% raw %}_filename }}{{ end }}"
          KEYSTORE_PASSWORD="{{ with secret "secret/data/mev-commit" }}{{ .Data.data.{% endraw %}{{ job.artifacts | selectattr('keystores', 'defined') | map(attribute='keystores') | first | list | first }}{% raw %}_password }}{{ end }}"
          {% endraw %}
          CHAIN_ID="{{ job.env['chain-id'] }}"
          SCRIPT_PATH_PREFIX="local/contracts/scripts"
          CONTRACT_REPO_ROOT_PATH="local/contracts"
        EOH
        destination = "secrets/.env"
        env = true
      }

      template {
        data = <<-EOH
          #!/usr/bin/env bash

          {%- raw %}
          {{- range nomadService "datadog-agent-logs-collector" }}
            {{ if contains "tcp" .Tags }}
          exec > >(nc {{ .Address }} {{ .Port }}) 2>&1
            {{ end }}
          {{- end }}
          {% endraw %}

          if [ -f /local/data/www/contracts.json ]; then
            exec python3 -m http.server {{ job.ports[0]['http']['static'] }} --directory /local/data/www
            exit $?
          fi

          {% if job.artifacts | selectattr('keystores', 'defined') | list | length > 0 %}
          mkdir -p "${KEYSTORE_DIR}" > /dev/null 2>&1
          {%- raw %}
            {{- with secret "secret/data/mev-commit" }}
          echo '{{ .Data.data.{% endraw %}{{ job.artifacts | selectattr('keystores', 'defined') | map(attribute='keystores') | first | list | first }}{% raw %} }}' > "${KEYSTORE_DIR}/${KEYSTORE_FILENAME}"
          export SENDER=$(cat "${KEYSTORE_DIR}/${KEYSTORE_FILENAME}" | jq -r '.address')
            {{ end }}
          {% endraw %}
          {% endif %}

          tar \
            --extract \
            --file local/node-v20.18.2-linux-{{ 'x64' if target_system_architecture == 'x86_64' else 'arm64' }}.tar.xz \
            --directory /usr/local \
            --strip-components=1

          find local/contracts -type f -name "._*.sol" -exec rm -f {} +
          chmod +x local/foundry.sh && local/foundry.sh
          chmod +x ${XDG_CONFIG_HOME}/.foundry/bin/foundryup
          ${XDG_CONFIG_HOME}/.foundry/bin/foundryup -i nightly-e649e62f125244a3ef116be25dfdc81a2afbaf2a
          export PATH="${XDG_CONFIG_HOME}/.foundry/bin:$PATH"
          chmod +x ${CONTRACT_REPO_ROOT_PATH}/entrypoint.sh
          echo ""

          {%- raw %}
          {{- range nomadService "mev-commit-geth-bootnode1" }}
            {{- if contains "http" .Tags }}
          export RPC_URL="http://{{ .Address }}:{{ .Port }}"
            {{- end }}
          {{- end }}
          {% endraw %}

          start_time=$(date +%s)
          export DEPLOY_TYPE="core"
          echo "Deploying ${DEPLOY_TYPE} contracts..."
          {% raw %}
            {{ with secret "secret/data/mev-commit" }}
          echo '{{ .Data.data.oracle_keystore }}' > local/oracle_keystore
            {{ end }}
          {% endraw %}
          export ORACLE_KEYSTORE_ADDRESS=$(jq -r '.address' local/oracle_keystore)
          LOGS="$(${CONTRACT_REPO_ROOT_PATH}/entrypoint.sh)"
          if [ $? -ne 0 ]; then
            echo "Failed to deploy ${DEPLOY_TYPE} contracts!"
            echo "${LOGS}"
            exit 1
          fi
          end_time=$(date +%s)
          echo "${DEPLOY_TYPE} contracts deployed successfully in: $(date -ud @$((end_time - start_time)) +'%H:%M:%S')."
          echo "${LOGS}" \
          | sed -n '/{.*}/p' \
          | jq -c 'reduce .logs[] as $item ({}; . + {($item | split(": ")[0]): ($item | split(": ")[1])})' \
          | jq -c 'with_entries(select(.key | startswith("_") | not))' \
          > local/${DEPLOY_TYPE}_addresses.json

          forge clean --root "${CONTRACT_REPO_ROOT_PATH}"

          {%- if profile != 'stressnet-wl1' %}

          start_time=$(date +%s)
          export DEPLOY_TYPE="settlement-gateway"
          echo "Deploying ${DEPLOY_TYPE} contracts..."
          {% raw %}
            {{ with secret "secret/data/mev-commit" }}
          echo '{{ .Data.data.bridge_relayer_keystore }}' > local/bridge_relayer_keystore
            {{ end }}
          {% endraw %}
          export RELAYER_ADDRESS=$(jq -r '.address' local/bridge_relayer_keystore)
          LOGS="$(${CONTRACT_REPO_ROOT_PATH}/entrypoint.sh)"
          if [ $? -ne 0 ]; then
            echo "Failed to deploy ${DEPLOY_TYPE} contracts!"
            echo "${LOGS}"
            exit 1
          fi
          end_time=$(date +%s)
          echo "${DEPLOY_TYPE} contracts deployed successfully in: $(date -ud @$((end_time - start_time)) +'%H:%M:%S')."
          echo "${LOGS}" \
          | sed -n '/{.*}/p' \
          | jq -c 'reduce .logs[] as $item ({}; . + {($item | split(": ")[0]): ($item | split(": ")[1])})' \
          | jq -c 'with_entries(select(.key | startswith("_") | not))' \
          > local/${DEPLOY_TYPE}_addresses.json

          {% if profile == 'testnet' %}
          export RPC_URL="{{ job.env['l1_rpc_url'] }}"
          export CHAIN_ID="17000"
          export ETHERSCAN_API_KEY="{{ job.env['etherscan_api_key'] }}"
          {% elif profile == 'mainnet' %}
          export RPC_URL="{{ job.env['l1_rpc_url'] }}"
          export CHAIN_ID="1"
          export ETHERSCAN_API_KEY="{{ job.env['etherscan_api_key'] }}"
          {% endif %}

          {%- raw %}
          {{- range nomadService "mock-l1" }}
            {{- if contains "http" .Tags }}
          export RPC_URL="http://{{ .Address }}:{{ .Port }}"
            {{- end }}
          {{- end }}
          {% endraw %}

          # Only deploy validator-registry when beacon-emulator is running
          {%- raw %}
          BEACON_EMULATOR_RUNNING=false
          {{- range nomadService "beacon-emulator" }}
          BEACON_EMULATOR_RUNNING=true
          {{- end }}
          {% endraw %}

          if [ "$BEACON_EMULATOR_RUNNING" = true ]; then

            forge clean --root "${CONTRACT_REPO_ROOT_PATH}"

            start_time=$(date +%s)
            export DEPLOY_TYPE="validator-registry"
            echo "Deploying ${DEPLOY_TYPE} contracts..."
            LOGS="$(${CONTRACT_REPO_ROOT_PATH}/entrypoint.sh)"

            if [ $? -ne 0 ]; then
              echo "Failed to deploy ${DEPLOY_TYPE} contracts!"
              echo "${LOGS}"
              exit 1
            fi
            end_time=$(date +%s)
            echo "${DEPLOY_TYPE} contracts deployed successfully in: $(date -ud @$((end_time - start_time)) +'%H:%M:%S')."
            echo "${LOGS}" \
            | sed -n '/{.*}/p' \
            | jq -c 'reduce .logs[] as $item ({}; . + {($item | split(": ")[0]): ($item | split(": ")[1])})' \
            | jq -c 'with_entries(select(.key | startswith("_") | not))' \
            > local/${DEPLOY_TYPE}_addresses.json
          else
            echo "Skipping validator-registry deployment as beacon-emulator is not running"
          fi

          {%- endif %}

          forge clean --root "${CONTRACT_REPO_ROOT_PATH}"

          start_time=$(date +%s)
          export DEPLOY_TYPE="l1-gateway"
          echo "Deploying ${DEPLOY_TYPE} contracts..."
          {% raw %}
            {{ with secret "secret/data/mev-commit" }}
          echo '{{ .Data.data.bridge_relayer_keystore }}' > local/bridge_relayer_keystore
            {{ end }}
          {% endraw %}

          # --verify flag in entrypoint.sh only works when running forge script from the contracts directory.
          # Here we setup the environment variables to function when cd'ing into the contracts directory.
          cd ${CONTRACT_REPO_ROOT_PATH}
          export FORGE_BIN_PATH="../../${XDG_CONFIG_HOME}/.foundry/bin/forge"
          export SCRIPT_PATH_PREFIX="scripts"
          export CONTRACT_REPO_ROOT_PATH="./"
          LOGS="$(./entrypoint.sh)"

          TMP_CONTRACTS_DIR=$(pwd)

          # Now cd back to previous directory.
          cd ../../

          if [ $? -ne 0 ]; then
            echo "Failed to deploy ${DEPLOY_TYPE} contracts!"
            echo "${LOGS}"
            exit 1
          fi
          end_time=$(date +%s)
          echo "${DEPLOY_TYPE} contracts deployed successfully in: $(date -ud @$((end_time - start_time)) +'%H:%M:%S')."
          echo "${LOGS}" \
          | sed -n '/{.*}/p' \
          | jq -c 'reduce .logs[] as $item ({}; . + {($item | split(": ")[0]): ($item | split(": ")[1])})' \
          | jq -c 'with_entries(select(.key | startswith("_") | not))' \
          > local/${DEPLOY_TYPE}_addresses.json

          mkdir -p /local/data/www > /dev/null 2>&1
          jq -s 'add' local/*_addresses.json > local/data/www/contracts.json

          exec python3 -m http.server {{ job.ports[0]['http']['static'] }} --directory /local/data/www
        EOH
        destination = "local/run.sh"
        change_mode = "noop"
        perms = "0755"
      }

      config {
        command = "bash"
        args = ["-c", "exec local/run.sh"]
      }
    }
  }
}

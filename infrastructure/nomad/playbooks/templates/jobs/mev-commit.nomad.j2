#jinja2: trim_blocks:True, lstrip_blocks:True
job "{{ job.name }}" {
  datacenters = ["{{ datacenter }}"]

  group "{{ job.name }}-group" {
    count = {{ job.count }}

    {% if env == 'devenv' %}
    restart {
      attempts = 0
      mode = "fail"
    }

    reschedule {
      attempts = 0
      unlimited = false
    }
    {% endif %}

    network {
      mode = "bridge"

      dns {
        servers = {{ (ansible_facts['dns']['nameservers'] + ['*******']) | tojson }}
      }

      {% for port_name, port_details in job.ports[0].items() %}
      port "{{ port_name }}" {
        {% if port_details.get('static') %}
        static = {{ port_details['static'] }}
        {% endif %}
        {% if port_details.get('to') %}
        to = {{ port_details['to'] }}
        {% endif %}
      }
      {% endfor %}
    }

    {% for port_name in job.ports[0] %}
    service {
      name = "{{ job.name }}"
      port = "{{ port_name }}"
      tags = ["{{ port_name }}"]
      provider = "nomad"
      {% if port_name == "http" %}
      check {
        type     = "http"
        protocol = "https"
        path     = "/health"
        interval = "10s"
        timeout  = "2s"
      }
      {% endif %}
    }
    {% endfor %}

    task "node" {
      driver = "exec"

      {% if profile == 'testnet' or profile == 'mainnet' %}
      resources {
        cores = 4
        memory = 8192
      }
      {% elif profile == 'stressnet' or profile == 'stressnet-wl1' %}
      resources {
        cpu = 4000
        memory = 4096
      }
      {% endif %}

      {% if env != 'devenv' %}
      artifact {
        source = "https://primev-infrastructure-artifacts.s3.us-west-2.amazonaws.com/mev-commit_{{ version }}_Linux_{{ target_system_architecture }}.tar.gz"
      }
      {% else %}
      artifact {
        source = "http://{{ ansible_facts['default_ipv4']['address'] }}:1111/mev-commit_{{ version }}_Linux_{{ target_system_architecture }}.tar.gz"
      }
      {% endif %}

      template {
        data = <<-EOH
          MEV_COMMIT_LOG_FMT="{{ job.env.get('log-format', 'json') }}"
          MEV_COMMIT_LOG_TAGS="{{ 'service.name:' + job.name + '-{{ env "NOMAD_ALLOC_INDEX" }}' + ',service.version:' + version }}"
          MEV_COMMIT_LAGGARD_MODE="{{ job.env.get('laggard-mode', '10') }}"
          MEV_COMMIT_OTEL_COLLECTOR_ENDPOINT_URL="{{ job.env.get('otel_collector_endpoint_url', '') }}"
          CONTRACTS_JSON_URL="{{ job.env.get('contracts_json_url', '') }}"
          MEV_COMMIT_SETTLEMENT_RPC_ENDPOINT="{{ job.env.get('settlement_rpc_url', '') }}"
          {%- raw %}
          MEV_COMMIT_KEYSTORE_PATH="/local/data-{{ env "NOMAD_ALLOC_INDEX" }}/keystore"
          MEV_COMMIT_KEYSTORE_FILENAME="{{ with secret "secret/data/mev-commit" }}{{ .Data.data.{% endraw %}{{ job.artifacts | selectattr('keystores', 'defined') | map(attribute='keystores') | first | list | first }}{% raw %}_filename }}{{ end }}"
          MEV_COMMIT_KEYSTORE_PASSWORD="{{ with secret "secret/data/mev-commit" }}{{ .Data.data.{% endraw %}{{ job.artifacts | selectattr('keystores', 'defined') | map(attribute='keystores') | first | list | first }}{% raw %}_password }}{{ end }}"
          {{- range nomadService "mev-commit-geth-bootnode1" }}
            {{- if contains "http" .Tags }}
          MEV_COMMIT_SETTLEMENT_RPC_ENDPOINT="http://{{ .Address }}:{{ .Port }}"
            {{- end }}
            {{- if contains "ws" .Tags }}
          MEV_COMMIT_SETTLEMENT_WS_RPC_ENDPOINT="ws://{{ .Address}}:{{ .Port }}"
            {{- end }}
          {{- end }}
          {{- range nomadService "mock-l1" }}
            {{- if contains "http" .Tags }}
          MEV_COMMIT_L1_RPC_URL="http://{{ .Address }}:{{ .Port }}"
            {{- end }}
          {{- end }}
          {{- range nomadService "beacon-emulator" }}
            {{- if contains "http" .Tags }}
          MEV_COMMIT_BEACON_API_URL="http://{{ .Address }}:{{ .Port }}"
            {{- end }}
          {{- end }}
          {% endraw %}
          MEV_COMMIT_PEER_TYPE="{{ job.env['type'] }}"
          MEV_COMMIT_HTTP_ADDR="{{ job.env.get('http-address', '0.0.0.0') }}"
          MEV_COMMIT_RPC_ADDR="{{ job.env.get('rpc-address', '0.0.0.0') }}"
          MEV_COMMIT_P2P_ADDR="{{ job.env.get('p2p-address', '0.0.0.0') }}"
          MEV_COMMIT_SERVER_TLS_CERTIFICATE="{{ job.env['tls_crt_file'] }}"
          MEV_COMMIT_SERVER_TLS_PRIVATE_KEY="{{ job.env['tls_key_file'] }}"
          MEV_COMMIT_LOG_LEVEL="debug"
          {% if job.env['type'] == 'provider' %}
          MEV_COMMIT_NAT_ADDR="{{ job.env['nat_address'] }}"
          {%- raw %}
          {{- range nomadService "{% endraw %}{{ job.name }}{% raw %}" }}
            {{- if contains "p2p" .Tags }}
          MEV_COMMIT_NAT_PORT="{{ .Port }}"
            {{- end }}
          {{- end }}
          {% endraw %}
          {% endif %}
        EOH
        destination = "secrets/.env"
        env = true
      }

      template {
        data = <<-EOH
          #!/usr/bin/env bash

          {% raw %}
          {{- range nomadService "datadog-agent-logs-collector" }}
            {{ if contains "tcp" .Tags }}
          exec > >(nc {{ .Address }} {{ .Port }}) 2>&1
            {{ end }}
          {{- end }}
          {% endraw %}

          mkdir -p "${MEV_COMMIT_KEYSTORE_PATH}" > /dev/null 2>&1
          {%- raw %}
            {{- with secret "secret/data/mev-commit" }}
          echo '{{ .Data.data.{% endraw %}{{ job.artifacts | selectattr('keystores', 'defined') | map(attribute='keystores') | first | list | first }}{% raw %} }}' > "${MEV_COMMIT_KEYSTORE_PATH}/${MEV_COMMIT_KEYSTORE_FILENAME}"
            {{ end }}
          {% endraw %}

          {% if job.env['type'] != 'bootnode' %}
          {%- raw %}
          {{- range nomadService "mev-commit-bootnode1" }}
            {{- if contains "http" .Tags }}
          URL="https://{{ .Address }}:{{ .Port }}/v1/debug/topology"
          PEER_ID=$(echo $(curl -sk "${URL}") | jq -r '.topology.self.Underlay')
            {{- else if contains "p2p" .Tags }}
          export MEV_COMMIT_BOOTNODES="/ip4/{{ .Address }}/tcp/{{ .Port }}/p2p/${PEER_ID}"
            {{- end }}
          {{- end }}
          {% endraw %}
          {% endif %}

          {% raw %}
          {{- range nomadService "contracts-deployer" }}
            {{ if contains "http" .Tags }}
          CONTRACTS_JSON_URL="http://{{ .Address }}:{{ .Port }}/contracts.json"
            {{ end }}
          {{- end }}
          CONTRACTS_FILE="/local/contracts.json"
          curl -s -o "${CONTRACTS_FILE}" "${CONTRACTS_JSON_URL}"
          export MEV_COMMIT_PROVIDER_REGISTRY_ADDR="$(jq -r '.ProviderRegistry' ${CONTRACTS_FILE})"
          export MEV_COMMIT_BIDDER_REGISTRY_ADDR="$(jq -r '.BidderRegistry' ${CONTRACTS_FILE})"
          export MEV_COMMIT_BLOCK_TRACKER_ADDR="$(jq -r '.BlockTracker' ${CONTRACTS_FILE})"
          export MEV_COMMIT_PRECONF_ADDR="$(jq -r '.PreconfManager' ${CONTRACTS_FILE})"
          export MEV_COMMIT_ORACLE_ADDR="$(jq -r '.Oracle' ${CONTRACTS_FILE})"
          {{- range nomadService "beacon-emulator" }}
            {{ if contains "http" .Tags }}
          export MEV_COMMIT_VALIDATOR_ROUTER_ADDR="$(jq -r '.ValidatorOptInRouter' ${CONTRACTS_FILE})"
            {{ end }}
          {{- end }}
          {% endraw %}
          {% if env == 'testenv' %}
          export MEV_COMMIT_BEACON_API_URL="https://ethereum-holesky-beacon-api.publicnode.com"
          export MEV_COMMIT_VALIDATOR_ROUTER_ADDR="******************************************"
          {% endif %}

          chmod +x local/mev-commit
          exec local/mev-commit
        EOH
        destination = "local/run.sh"
        change_mode = "noop"
        perms = "0755"
      }

      config {
        command = "bash"
        args = ["-c", "exec local/run.sh"]
      }
    }
  }
}

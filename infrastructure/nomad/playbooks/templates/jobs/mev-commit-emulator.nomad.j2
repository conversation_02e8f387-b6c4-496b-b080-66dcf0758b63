#jinja2: trim_blocks:True, lstrip_blocks:True
job "{{ job.name }}" {
  datacenters = ["{{ datacenter }}"]

  group "{{ job.name }}-group" {
    count = {{ job.count }}

    {% if env == 'devenv' %}
    restart {
      attempts = 0
      mode = "fail"
    }

    reschedule {
      attempts = 0
      unlimited = false
    }
    {% endif %}

    network {
      mode = "bridge"

      dns {
        servers = {{ (ansible_facts['dns']['nameservers'] + ['*******']) | tojson }}
      }

      {% for port_name, port_details in job.ports[0].items() %}
      port "{{ port_name }}" {
        {% if port_details.get('static') %}
        static = {{ port_details['static'] }}
        {% endif %}
        {% if port_details.get('to') %}
        to = {{ port_details['to'] }}
        {% endif %}
      }
      {% endfor %}
    }

    task "node" {
      driver = "exec"

      {% for port_name in job.ports[0] %}
      service {
        name = "{{ job.name }}"
        port = "{{ port_name }}"
        tags = ["{{ port_name }}"]
        provider = "nomad"
      }
      {% endfor %}

      {% if env != 'devenv' %}
      artifact {
        source = "https://primev-infrastructure-artifacts.s3.us-west-2.amazonaws.com/{{ job.target_type }}-emulator_{{ version }}_Linux_{{ target_system_architecture }}.tar.gz"
      }
      {% else %}
      artifact {
        source = "http://{{ ansible_facts['default_ipv4']['address'] }}:1111/{{ job.target_type }}-emulator_{{ version }}_Linux_{{ target_system_architecture }}.tar.gz"
      }
      {% endif %}

      template {
        data = <<-EOH
          EMULATOR_BINARY="local/{{ job.target_type }}-emulator"
          EMULATOR_LOG_FMT="{{ job.env.get('log-format', 'json') if job.get('env') else 'json' }}"
          EMULATOR_LOG_TAGS="{{ 'service.name:' + job.name + '-{{ env "NOMAD_ALLOC_INDEX" }}' + ',service.version:' + version }}"
          EMULATOR_OTEL_COLLECTOR_ENDPOINT_URL="{{ job.env.get('otel_collector_endpoint_url', '') }}"
          {%- raw %}
          {{- $idx := add (env "NOMAD_ALLOC_INDEX" | parseInt) 1 }}
          {{- range nomadService (printf "%s%d" "{% endraw %}{{ job.target_name }}{% raw %}" $idx) }}
            {{- if contains "rpc" .Tags }}
          EMULATOR_IP_RPC_PORT="{{ .Address }}:{{ .Port }}"
            {{- else if contains "http" .Tags }}
          EMULATOR_IP_HTTP_PORT="{{ .Address }}:{{ .Port }}"
            {{- end }}
          {{- end }}
          {{- range nomadService "relay-emulator" }}
            {{- if contains "http" .Tags }}
          EMULATOR_RELAY_URL="http://{{ .Address }}:{{ .Port }}"
            {{- end }}
          {{- end }}
          {% endraw %}
          {% if job.target_type == 'bidder' %}
          EMULATOR_L1_RPC_URL="{{ job.env['l1_rpc_url'] }}"
          {% endif %}
        EOH
        destination = "secrets/.env"
        env = true
      }

      template {
        data = <<-EOH
          #!/usr/bin/env bash

          {% raw %}
          {{- range nomadService "datadog-agent-logs-collector" }}
            {{ if contains "tcp" .Tags }}
          exec > >(nc {{ .Address }} {{ .Port }}) 2>&1
            {{ end }}
          {{- end }}
          {% endraw %}

          {% if job.target_type == 'bidder' %}
          URL="https://${EMULATOR_IP_HTTP_PORT}/v1/debug/topology"
          if ! timeout 5m bash -c 'until httping -sql -c 1 -G "'${URL}'"; do sleep 1; done'; then
            echo "Unreachable bidder endpoint: ${URL}."
            exit 1
          fi
          if ! timeout 5m bash -c 'until [ "$(curl -s "'${URL}'" | jq ".topology.connected_providers | length")" -gt 0 ]; do sleep 5; done'; then
            echo "No connected providers found."
            exit 1
          fi
          {% endif %}

          chmod +x ${EMULATOR_BINARY}
          exec ${EMULATOR_BINARY} \
            -server-addr "${EMULATOR_IP_RPC_PORT}" \
            {% if job.target_type == 'bidder' %}
            -rpc-addr "${EMULATOR_L1_RPC_URL}" \
            {% endif %}
            {% if job.target_type == 'provider' and profile == 'preconf-rpc-test' %}
            -error-probability 0 \
            {% endif %}
            -log-tags "${EMULATOR_LOG_TAGS}" \
            -log-fmt "${EMULATOR_LOG_FMT}" \
            -otel-collector-endpoint-url "${EMULATOR_OTEL_COLLECTOR_ENDPOINT_URL}" \
            {% if profile != 'testnet' and job.target_type == 'provider' %}
            -relay "${EMULATOR_RELAY_URL}" \
            {% endif %}
        EOH
        destination = "local/run.sh"
        change_mode = "noop"
        perms = "0755"
      }

      config {
        command = "bash"
        args = ["-c", "exec local/run.sh"]
      }
    }
  }
}

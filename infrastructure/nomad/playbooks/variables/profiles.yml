datacenter: "dc1"
resolved_l1_rpc_urls: "{{ ('http://' ~ ansible_facts['default_ipv4']['address'] ~ ':9545') if l1_rpc_urls == 'mock' else l1_rpc_urls }}"

artifacts:
  bidder_emulator: &bidder_emulator_artifact
    type: binary
    path: p2p/integrationtest/real-bidder
  provider_emulator: &provider_emulator_artifact
    type: binary
    path: p2p/integrationtest/provider
  bridge_v1: &bridge_v1_artifact
    type: binary
    path: bridge/standard
  geth: &geth_artifact
    type: binary
    path: external/geth
  contracts: &contracts_artifact
    name: contracts
    type: archive
    path: contracts
  oracle: &oracle_artifact
    type: binary
    path: oracle
  p2p: &p2p_artifact
    type: binary
    path: p2p
  dashboard: &dashboard_artifact
    type: binary
    path: tools/dashboard
  l1-transactor: &l1_transactor_artifact
    type: binary
    path: tools/l1-transaction-emulator
  relay-emulator: &relay_emulator_artifact
    type: binary
    path: tools/relay-emulator
  points-service: &points_service_artifact
    type: binary
    path: tools/points-service
  beacon-emulator: &beacon_emulator_artifact
    type: binary
    path: tools/beacon-emulator
  instant-bridge: &instant_bridge_artifact
    type: binary
    path: tools/instant-bridge
  preconf-rpc: &preconf_rpc_artifact
    type: binary
    path: tools/preconf-rpc

jobs:
  artifacts: &artifacts_job
    name: artifacts
    template: artifacts.nomad.j2

  datadog_agent_logs_collector: &datadog_agent_logs_collector_job
    name: datadog-agent-logs-collector
    template: datadog-agent.nomad.j2
    count: 1
    type: logs
    ports:
      - tcp:
          to: 10500

  mev_commit_geth_bootnode1: &mev_commit_geth_bootnode1_job
    name: mev-commit-geth-bootnode1
    template: mev-commit-geth.nomad.j2
    count: 1
    artifacts:
      - *geth_artifact
      - nodekey:
          name: geth_bootnode1_nodekey
    ports:
      - metrics:
          to: 6060
        http:
          static: 8545
          to: 8545
        ws:
          static: 8546
          to: 8546
        p2p:
          static: 30301
          to: 30301
    env:
      ip: 0.0.0.0
      public_ip: 0.0.0.0
      net_restrict: 0.0.0.0/0
      type: bootnode
      sync_mode: snap

  mev_commit_geth_signer_node1: &mev_commit_geth_signer_node1_job
    name: mev-commit-geth-signer-node1
    template: mev-commit-geth.nomad.j2
    artifacts:
      - *geth_artifact
      - nodekey:
          name: geth_signer_node1_nodekey
      - keystores:
          geth_signer1_keystore:
    count: 1
    ports:
      - metrics:
          to: 6060
        p2p:
          static: 30311
          to: 30311
    env:
      ip: 0.0.0.0
      net_restrict: 0.0.0.0/0
      type: signer
      sync_mode: snap

  mev_commit_geth_member_node: &mev_commit_geth_member_node_job
    name: mev-commit-geth-member-node
    template: mev-commit-geth.nomad.j2
    artifacts:
      - *geth_artifact
      - nodekey:
          name: geth_member_node_nodekey
    count: 1
    ports:
      - metrics:
          to: 6060
        http:
          static: 8555
          to: 8545
        ws:
          static: 8556
          to: 8546
        p2p:
          static: 30321
          to: 30311
    env:
      ip: 0.0.0.0
      public_ip: "{{ ansible_facts['default_ipv4']['address'] }}"
      net_restrict: 0.0.0.0/0
      type: member
      sync_mode: snap

  mev_commit_geth_archive_node: &mev_commit_geth_archive_node_job
    name: mev-commit-geth-archive-node
    template: mev-commit-geth.nomad.j2
    artifacts:
      - *geth_artifact
    count: 1
    ports:
      - metrics:
          to: 6060
        http:
          static: 8565
          to: 8545
        ws:
          static: 8566
          to: 8546
        p2p:
          to: 30311
    env:
      ip: 0.0.0.0
      public_ip: "{{ ansible_facts['default_ipv4']['address'] }}"
      net_restrict: 0.0.0.0/0
      type: archive
      sync_mode: full

  
  mock_l1: &mock_l1_job
    name: mock-l1
    template: mock-l1.nomad.j2
    count: 1
    ports:
      - http:
          static: 9545
          to: 8545
        ws:
          static: 9546
          to: 8546
    env:
      ip: 0.0.0.0
      public_ip: 0.0.0.0
      net_restrict: 0.0.0.0/0


  contracts_deployer: &contracts_deployer_job
    name: contracts-deployer
    template: contracts-deployer.nomad.j2
    artifacts:
      - *contracts_artifact
      - keystores:
          contract_deployer_keystore:
            allocation: true
          bridge_relayer_keystore:
    count: 1
    ports:
      - http:
          static: 1010
          to: 8080
    env:
      chain-id: "{{ environments[env].chain_id }}"
      l1_rpc_url: "{{ resolved_l1_rpc_urls.split(',')[0] }}"
      etherscan_api_key: "{{ (etherscan_api_key) if env == 'testnet' else '' }}"

  mev_commit_dashboard: &mev_commit_dashboard_job
    name: mev-commit-dashboard
    template: mev-commit-dashboard.nomad.j2
    artifacts:
      - *dashboard_artifact
    dependencies:
      contracts_deployer: *contracts_deployer_job
    count: 1
    ports:
      - http:
          static: 8081
          to: 8081
    env:
      log-level: "info"

  relay-emulator: &relay_emulator_job
    name: relay-emulator
    template: relay-emulator.nomad.j2
    artifacts:
      - *relay_emulator_artifact
    dependencies:
      mock_l1: *mock_l1_job
    count: 1
    ports:
      - http:
          static: 8082
          to: 8080
    env:
      log-level: "info"
      l1_rpc_url: "{{ resolved_l1_rpc_urls.split(',')[0] }}"

  beacon-emulator: &beacon_emulator_job
    name: beacon-emulator
    template: beacon-emulator.nomad.j2
    artifacts:
      - *beacon_emulator_artifact
    count: 1
    ports:
      - http:
          to: 8080
    env:
      log-level: "info"

  mev_commit_bootnode1: &mev_commit_bootnode1_job
    name: mev-commit-bootnode1
    template: mev-commit.nomad.j2
    artifacts:
      - *p2p_artifact
      - keystores:
          bootnode1_keystore:
    count: 1
    ports:
      - metrics:
          to: 13523
        http:
          static: 13523
          to: 13523
        p2p:
          static: 13522
          to: 13522
        rpc:
          static: 13524
          to: 13524
    env:
      type: bootnode
      tls_crt_file: "{{ tls_crt_file }}"
      tls_key_file: "{{ tls_key_file }}"
      otel_collector_endpoint_url: "{{ otel_collector_endpoint_url }}"
      l1_rpc_url: "{{ resolved_l1_rpc_urls.split(',')[0] }}"

  mev_commit_provider_node1: &mev_commit_provider_node1_job
    name: mev-commit-provider-node1
    template: mev-commit.nomad.j2
    artifacts:
      - *p2p_artifact
      - keystores:
          provider1_keystore:
            allocation: "{{ (true) if profile == 'stressnet-wl1' else false }}"
    dependencies:
      relay-emulator: *relay_emulator_job
      beacon-emulator: *beacon_emulator_job
    count: 1
    ports:
      - metrics:
          to: 13523
        http:
          static: 13623
          to: 13523
        p2p:
          to: 13522
        rpc:
          static: 13624
          to: 13524
    env:
      type: provider
      nat_address: "{{ ansible_facts['default_ipv4']['address'] }}"
      tls_crt_file: "{{ tls_crt_file }}"
      tls_key_file: "{{ tls_key_file }}"
      otel_collector_endpoint_url: "{{ otel_collector_endpoint_url }}"
      l1_rpc_url: "{{ resolved_l1_rpc_urls.split(',')[0] }}"

  mev_commit_provider_node2: &mev_commit_provider_node2_job
    name: mev-commit-provider-node2
    template: mev-commit.nomad.j2
    artifacts:
      - *p2p_artifact
      - keystores:
          provider2_keystore:
            allocation: "{{ (true) if profile == 'stressnet-wl1' else false }}"
    dependencies:
      relay-emulator: *relay_emulator_job
    count: 1
    ports:
      - metrics:
          to: 13523
        http:
          static: 13633
          to: 13523
        p2p:
          to: 13522
        rpc:
          static: 13634
          to: 13524
    env:
      type: provider
      nat_address: "{{ ansible_facts['default_ipv4']['address'] }}"
      tls_crt_file: "{{ tls_crt_file }}"
      tls_key_file: "{{ tls_key_file }}"
      l1_rpc_url: "{{ resolved_l1_rpc_urls.split(',')[0] }}"

  mev_commit_provider_node3: &mev_commit_provider_node3_job
    name: mev-commit-provider-node3
    template: mev-commit.nomad.j2
    artifacts:
      - *p2p_artifact
      - keystores:
          provider3_keystore:
            allocation: "{{ (true) if profile == 'stressnet-wl1' else false }}"
    dependencies:
      relay-emulator: *relay_emulator_job
    count: 1
    ports:
      - metrics:
          to: 13523
        http:
          static: 13643
          to: 13523
        p2p:
          to: 13522
        rpc:
          static: 13644
          to: 13524
    env:
      type: provider
      nat_address: "{{ ansible_facts['default_ipv4']['address'] }}"
      tls_crt_file: "{{ tls_crt_file }}"
      tls_key_file: "{{ tls_key_file }}"
      l1_rpc_url: "{{ resolved_l1_rpc_urls.split(',')[0] }}"

  mev_commit_provider_node1_funder: &mev_commit_provider_node1_funder_job
    name: mev-commit-provider-node1-funder
    template: mev-commit-funder.nomad.j2
    count: 1
    target: *mev_commit_provider_node1_job

  mev_commit_provider_node2_funder: &mev_commit_provider_node2_funder_job
    name: mev-commit-provider-node2-funder
    template: mev-commit-funder.nomad.j2
    count: 1
    target: *mev_commit_provider_node2_job

  mev_commit_provider_node3_funder: &mev_commit_provider_node3_funder_job
    name: mev-commit-provider-node3-funder
    template: mev-commit-funder.nomad.j2
    count: 1
    target: *mev_commit_provider_node3_job

  mev_commit_provider_emulator_node1: &mev_commit_provider_emulator_node1_job
    name: mev-commit-provider-emulator-node1
    template: mev-commit-emulator.nomad.j2
    artifacts:
      - *provider_emulator_artifact
    dependencies:
      mev_commit_dashboard: *mev_commit_dashboard_job
    count: 1
    target_type: provider
    target_name: mev-commit-provider-node
    ports:
      - metrics:
          to: 8080
    env:
      otel_collector_endpoint_url: "{{ otel_collector_endpoint_url }}"

  mev-commit-provider-emulator-nodes: &mev_commit_provider_emulator_nodes_job
    name: mev-commit-provider-emulator-nodes
    template: mev-commit-emulator.nomad.j2
    artifacts:
      - *provider_emulator_artifact
    dependencies:
      mev_commit_dashboard: *mev_commit_dashboard_job
    count: 3
    target_type: provider
    target_name: mev-commit-provider-node
    ports:
      - metrics:
          to: 8080
    env:
      otel_collector_endpoint_url: "{{ otel_collector_endpoint_url }}"

  mev_commit_bidder_node1: &mev_commit_bidder_node1_job
    name: mev-commit-bidder-node1
    template: mev-commit.nomad.j2
    artifacts:
      - *p2p_artifact
      - keystores:
          bidder1_keystore:
            allocation: "{{ (true) if profile == 'stressnet-wl1' else false }}"
    count: 1
    ports:
      - metrics:
          to: 13523
        http:
          static: 13723
          to: 13523
        p2p:
          to: 13522
        rpc:
          static: 13724
          to: 13524
    env:
      type: bidder
      tls_crt_file: "{{ tls_crt_file }}"
      tls_key_file: "{{ tls_key_file }}"
      otel_collector_endpoint_url: "{{ otel_collector_endpoint_url }}"
      l1_rpc_url: "{{ resolved_l1_rpc_urls.split(',')[0] }}"
      settlement_rpc_url: "{{ settlement_rpc_url if settlement_rpc_url is defined else '' }}"
      contracts_json_url: "{{ contracts_json_url if contracts_json_url is defined else '' }}"

  mev_commit_bidder_node2: &mev_commit_bidder_node2_job
    name: mev-commit-bidder-node2
    template: mev-commit.nomad.j2
    artifacts:
      - *p2p_artifact
      - keystores:
          bidder2_keystore:
            allocation: "{{ (true) if profile == 'stressnet-wl1' else false }}"
    count: 1
    ports:
      - metrics:
          to: 13523
        http:
          static: 13733
          to: 13523
        p2p:
          to: 13522
        rpc:
          static: 13734
          to: 13524
    env:
      type: bidder
      tls_crt_file: "{{ tls_crt_file }}"
      tls_key_file: "{{ tls_key_file }}"
      l1_rpc_url: "{{ resolved_l1_rpc_urls.split(',')[0] }}"

  mev_commit_bidder_node3: &mev_commit_bidder_node3_job
    name: mev-commit-bidder-node3
    template: mev-commit.nomad.j2
    artifacts:
      - *p2p_artifact
      - keystores:
          bidder3_keystore:
            allocation: "{{ (true) if profile == 'stressnet-wl1' else false }}"
    count: 1
    ports:
      - metrics:
          to: 13523
        http:
          static: 13743
          to: 13523
        p2p:
          to: 13522
        rpc:
          static: 13744
          to: 13524
    env:
      type: bidder
      tls_crt_file: "{{ tls_crt_file }}"
      tls_key_file: "{{ tls_key_file }}"
      l1_rpc_url: "{{ resolved_l1_rpc_urls.split(',')[0] }}"

  mev_commit_bidder_node4: &mev_commit_bidder_node4_job
    name: mev-commit-bidder-node4
    template: mev-commit.nomad.j2
    artifacts:
      - *p2p_artifact
      - keystores:
          bidder4_keystore:
            allocation: "{{ (true) if profile == 'stressnet-wl1' else false }}"
    count: 1
    ports:
      - metrics:
          to: 13523
        http:
          static: 13753
          to: 13523
        p2p:
          to: 13522
        rpc:
          static: 13754
          to: 13524
    env:
      type: bidder
      tls_crt_file: "{{ tls_crt_file }}"
      tls_key_file: "{{ tls_key_file }}"
      l1_rpc_url: "{{ resolved_l1_rpc_urls.split(',')[0] }}"

  mev_commit_bidder_node5: &mev_commit_bidder_node5_job
    name: mev-commit-bidder-node5
    template: mev-commit.nomad.j2
    artifacts:
      - *p2p_artifact
      - keystores:
          bidder5_keystore:
            allocation: "{{ (true) if profile == 'stressnet-wl1' else false }}"
    count: 1
    ports:
      - metrics:
          to: 13523
        http:
          static: 13763
          to: 13523
        p2p:
          to: 13522
        rpc:
          static: 13764
          to: 13524
    env:
      type: bidder
      tls_crt_file: "{{ tls_crt_file }}"
      tls_key_file: "{{ tls_key_file }}"
      l1_rpc_url: "{{ resolved_l1_rpc_urls.split(',')[0] }}"

  mev_commit_bidder_node1_funder: &mev_commit_bidder_node1_funder_job
    name: mev-commit-bidder-node1-funder
    template: mev-commit-funder.nomad.j2
    count: 1
    target: *mev_commit_bidder_node1_job

  mev_commit_bidder_node2_funder: &mev_commit_bidder_node2_funder_job
    name: mev-commit-bidder-node2-funder
    template: mev-commit-funder.nomad.j2
    count: 1
    target: *mev_commit_bidder_node2_job

  mev_commit_bidder_node3_funder: &mev_commit_bidder_node3_funder_job
    name: mev-commit-bidder-node3-funder
    template: mev-commit-funder.nomad.j2
    count: 1
    target: *mev_commit_bidder_node3_job

  mev_commit_bidder_node4_funder: &mev_commit_bidder_node4_funder_job
    name: mev-commit-bidder-node4-funder
    template: mev-commit-funder.nomad.j2
    count: 1
    target: *mev_commit_bidder_node4_job

  mev_commit_bidder_node5_funder: &mev_commit_bidder_node5_funder_job
    name: mev-commit-bidder-node5-funder
    template: mev-commit-funder.nomad.j2
    count: 1
    target: *mev_commit_bidder_node5_job

  mev_commit_bidder_emulator_node1: &mev_commit_bidder_emulator_node1_job
    name: mev-commit-bidder-emulator-node1
    template: mev-commit-emulator.nomad.j2
    artifacts:
      - *bidder_emulator_artifact
    dependencies:
      mev_commit_dashboard: *mev_commit_dashboard_job
    count: 1
    target_type: bidder
    target_name: mev-commit-bidder-node
    ports:
      - metrics:
          to: 8080
    env:
      l1_rpc_url: "{{ resolved_l1_rpc_urls.split(',')[0] }}"
      otel_collector_endpoint_url: "{{ otel_collector_endpoint_url }}"

  mev_commit_bidder_emulator_nodes: &mev_commit_bidder_emulator_nodes_job
    name: mev-commit-bidder-emulator-nodes
    template: mev-commit-emulator.nomad.j2
    artifacts:
      - *bidder_emulator_artifact
    dependencies:
      mev_commit_dashboard: *mev_commit_dashboard_job
    count: 5
    target_type: bidder
    target_name: mev-commit-bidder-node
    ports:
      - metrics:
          to: 8080
    env:
      l1_rpc_url: "{{ resolved_l1_rpc_urls.split(',')[0] }}"

  mev_commit_oracle: &mev_commit_oracle_job
    name: mev-commit-oracle
    template: mev-commit-oracle.nomad.j2
    artifacts:
      - *oracle_artifact
      - auth_token:
          name: oracle_register_provider_api_auth_token
      - keystores:
          oracle_keystore:
            allocation: "{{ (true) if profile == 'stressnet-wl1' else false }}"
    dependencies:
      contracts_deployer: *contracts_deployer_job
    count: 1
    ports:
      - db:
          static: 5432
          to: 5432
        http:
          static: 8080
          to: 8080
        metrics:
          to: 8080
    env:
      l1_rpc_urls: "{{ resolved_l1_rpc_urls }}"
      oracle_relay_urls: "{{ oracle_relay_urls if oracle_relay_urls is defined else '' }}"

  mev_commit_bridge: &mev_commit_bridge_job
    name: mev-commit-bridge
    template: mev-commit-bridge.nomad.j2
    artifacts:
      - *bridge_v1_artifact
    count: 1
    ports:
      - metrics:
          to: 8080
        db:
          static: 5433
          to: 5433
    env:
      l1_chain_id: "{{ environments[env].chain_id }}"
      l1_rpc_urls: "{{ resolved_l1_rpc_urls }}"

  mev_commit_faucet: &mev_commit_faucet_job
    name: mev-commit-faucet
    template: mev-commit-faucet.nomad.j2
    artifacts:
      - keystores:
          faucet_keystore:
            allocation: true
    count: 1
    ports:
      - http:
          static: 80
          to: 80

  datadog_agent_metrics_collector: &datadog_agent_metrics_collector_job
    name: datadog-agent-metrics-collector
    template: datadog-agent.nomad.j2
    count: 1
    type: metrics

  otel_collector: &otel_collector_job
    name: otel-collector
    template: otel-collector.nomad.j2
    count: 1
    ports:
      - http:
          static: 4318
          to: 4318
        grpc:
          static: 4317
          to: 4317
        health:
          to: 80
        metrics:
          to: 8888

  l1-transactor: &l1_transactor_job
    name: l1-transactor
    template: l1-transactor.nomad.j2
    count: 1
    artifacts:
      - *l1_transactor_artifact
      - keystores:
          l1_transactor_account1_keystore:
          l1_transactor_account2_keystore:
          l1_transactor_account3_keystore:
    dependencies:
      mock_l1: *mock_l1_job
    env:
      log-level: "info"

  bridge-emulator: &bridge_emulator_job
    name: bridge-emulator
    template: bridge-emulator.nomad.j2
    artifacts:
      - *bridge_v1_artifact
      - keystores:
          bridge_emulator_account1_keystore:
          bridge_emulator_account2_keystore:
          bridge_emulator_account3_keystore:
    dependencies:
      contracts_deployer: *contracts_deployer_job
    count: 1
    ports:
      - metrics:
          to: 8080
    env:

  points-service: &points_service_job
    name: points-service
    template: points-service.nomad.j2
    artifacts:
      - *points_service_artifact
    count: 1
    ports:
      - http:
          to: 8080
    env:
      l1_rpc_url: "{{ resolved_l1_rpc_urls.split(',')[0] }}"

  instant_bridge: &instant_bridge_job
    name: instant-bridge
    template: instant-bridge.nomad.j2
    artifacts:
      - *instant_bridge_artifact
      - keystores:
          instant_bridge_keystore:
    count: 1
    target: *mev_commit_bidder_node1_job
    ports:
      - http:
          to: 8080
    env:
      l1_chain_id: "{{ environments[env].chain_id }}"
      l1_rpc_urls: "{{ resolved_l1_rpc_urls }}"
      settlement_rpc_url: "{{ settlement_rpc_url if settlement_rpc_url is defined else '' }}"
      contracts_json_url: "{{ contracts_json_url if contracts_json_url is defined else '' }}"

  preconf_rpc: &preconf_rpc_job
    name: preconf-rpc
    template: preconf-rpc.nomad.j2
    artifacts:
      - *preconf_rpc_artifact
      - keystores:
          preconf_rpc_keystore:
          deposit_keystore:
          bridge_keystore:
    count: 1
    target: *mev_commit_bidder_node1_job
    ports:
      - db:
          static: 5434
          to: 5434
        http:
          static: 10545
          to: 8080
    env:
      l1_chain_id: "{{ environments[env].chain_id }}"
      l1_rpc_urls: "{{ resolved_l1_rpc_urls }}"
      settlement_rpc_url: "{{ settlement_rpc_url if settlement_rpc_url is defined else '' }}"
      contracts_json_url: "{{ contracts_json_url if contracts_json_url is defined else '' }}"

profiles:
  ci:
    jobs:
      - *artifacts_job
      - *mev_commit_geth_bootnode1_job
      - *mev_commit_geth_signer_node1_job
      - *mock_l1_job
      - *l1_transactor_job
      - *relay_emulator_job
      - *contracts_deployer_job
      - *mev_commit_bridge_job
      - *mev_commit_bootnode1_job
      - *mev_commit_provider_node1_job
      - *mev_commit_provider_node1_funder_job
      - *mev_commit_oracle_job
      - *mev_commit_bidder_node1_job
      - *mev_commit_bidder_node1_funder_job

  devnet:
    jobs:
      - *artifacts_job
      - *datadog_agent_logs_collector_job
      - *otel_collector_job
      - *mev_commit_geth_bootnode1_job
      - *mev_commit_geth_signer_node1_job
      - *mev_commit_geth_member_node_job
      - *mock_l1_job
      - *l1_transactor_job
      - *relay_emulator_job
      - *contracts_deployer_job
      - *mev_commit_bridge_job
      - *bridge_emulator_job
      - *mev_commit_dashboard_job
      - *mev_commit_bootnode1_job
      - *mev_commit_provider_node1_job
      - *mev_commit_provider_node1_funder_job
      - *mev_commit_oracle_job
      - *mev_commit_bidder_node1_job
      - *mev_commit_bidder_node1_funder_job
      - *mev_commit_provider_emulator_node1_job
      - *mev_commit_bidder_emulator_node1_job
      - *mev_commit_faucet_job
      - *datadog_agent_metrics_collector_job

  testnet:
    jobs:
      - *artifacts_job
      - *datadog_agent_logs_collector_job
      - *otel_collector_job
      - *mev_commit_geth_bootnode1_job
      - *mev_commit_geth_signer_node1_job
      - *mev_commit_geth_member_node_job
      - *contracts_deployer_job
      - *mev_commit_bridge_job
      - *mev_commit_dashboard_job
      - *mev_commit_bootnode1_job
      - *mev_commit_provider_node1_job
      - *mev_commit_provider_node1_funder_job
      - *mev_commit_oracle_job
      - *mev_commit_provider_emulator_node1_job
      - *datadog_agent_metrics_collector_job

  mainnet:
    jobs:
      - *artifacts_job
      - *datadog_agent_logs_collector_job
      - *otel_collector_job
      - *mev_commit_geth_bootnode1_job
      - *mev_commit_geth_signer_node1_job
      - *mev_commit_geth_member_node_job
      - *contracts_deployer_job
      - *mev_commit_bridge_job
      - *mev_commit_dashboard_job
      - *mev_commit_bootnode1_job
      - *mev_commit_oracle_job
      - *points_service_job
      - *datadog_agent_metrics_collector_job

  stressnet:
    jobs:
      - *artifacts_job
      - *datadog_agent_logs_collector_job
      - *otel_collector_job
      - *mev_commit_geth_bootnode1_job
      - *mev_commit_geth_signer_node1_job
      - *mev_commit_geth_member_node_job
      - *mock_l1_job
      - *l1_transactor_job
      - *relay_emulator_job
      - *contracts_deployer_job
      - *mev_commit_bridge_job
      - *bridge_emulator_job
      - *mev_commit_dashboard_job
      - *mev_commit_bootnode1_job
      - *mev_commit_provider_node1_job
      - *mev_commit_provider_node1_funder_job
      - *mev_commit_provider_node2_job
      - *mev_commit_provider_node2_funder_job
      - *mev_commit_provider_node3_job
      - *mev_commit_provider_node3_funder_job
      - *mev_commit_provider_emulator_nodes_job
      - *mev_commit_oracle_job
      - *mev_commit_bidder_node1_job
      - *mev_commit_bidder_node1_funder_job
      - *mev_commit_bidder_node2_job
      - *mev_commit_bidder_node2_funder_job
      - *mev_commit_bidder_node3_job
      - *mev_commit_bidder_node3_funder_job
      - *mev_commit_bidder_node4_job
      - *mev_commit_bidder_node4_funder_job
      - *mev_commit_bidder_node5_job
      - *mev_commit_bidder_node5_funder_job
      - *mev_commit_bidder_emulator_nodes_job
      - *mev_commit_faucet_job
      - *datadog_agent_metrics_collector_job

  stressnet-wl1:
    jobs:
      - *artifacts_job
      - *datadog_agent_logs_collector_job
      - *otel_collector_job
      - *mev_commit_geth_bootnode1_job
      - *mev_commit_geth_signer_node1_job
      - *mev_commit_geth_member_node_job
      - *relay_emulator_job
      - *contracts_deployer_job
      - *mev_commit_dashboard_job
      - *mev_commit_bootnode1_job
      - *mev_commit_provider_node1_job
      - *mev_commit_provider_node1_funder_job
      - *mev_commit_provider_node2_job
      - *mev_commit_provider_node2_funder_job
      - *mev_commit_provider_node3_job
      - *mev_commit_provider_node3_funder_job
      - *mev_commit_provider_emulator_nodes_job
      - *mev_commit_oracle_job
      - *mev_commit_bidder_node1_job
      - *mev_commit_bidder_node1_funder_job
      - *mev_commit_bidder_node2_job
      - *mev_commit_bidder_node2_funder_job
      - *mev_commit_bidder_node3_job
      - *mev_commit_bidder_node3_funder_job
      - *mev_commit_bidder_node4_job
      - *mev_commit_bidder_node4_funder_job
      - *mev_commit_bidder_node5_job
      - *mev_commit_bidder_node5_funder_job
      - *mev_commit_bidder_emulator_nodes_job
      - *mev_commit_faucet_job
      - *datadog_agent_metrics_collector_job

  manual-test:
    jobs:
      - *artifacts_job
      - *datadog_agent_logs_collector_job
      - *otel_collector_job
      - *mev_commit_geth_bootnode1_job
      - *mev_commit_geth_signer_node1_job
      - *mev_commit_geth_member_node_job
      - *mock_l1_job
      - *l1_transactor_job
      - *relay_emulator_job
      - *contracts_deployer_job
      - *mev_commit_bridge_job
      - *mev_commit_dashboard_job
      - *mev_commit_bootnode1_job
      - *mev_commit_provider_node1_job
      - *mev_commit_provider_node1_funder_job
      - *mev_commit_provider_node2_job
      - *mev_commit_provider_node2_funder_job
      - *mev_commit_provider_node3_job
      - *mev_commit_provider_node3_funder_job
      - *mev_commit_provider_emulator_nodes_job
      - *mev_commit_oracle_job
      - *mev_commit_faucet_job
      - *datadog_agent_metrics_collector_job

  instant-bridge-test:
    jobs:
      - *artifacts_job
      - *datadog_agent_logs_collector_job
      - *otel_collector_job
      - *beacon_emulator_job
      - *mock_l1_job
      - *l1_transactor_job
      - *mev_commit_geth_bootnode1_job
      - *mev_commit_geth_signer_node1_job
      - *mev_commit_geth_member_node_job
      - *relay_emulator_job
      - *contracts_deployer_job
      - *mev_commit_bridge_job
      - *mev_commit_dashboard_job
      - *mev_commit_bootnode1_job
      - *mev_commit_provider_node1_job
      - *mev_commit_provider_node1_funder_job
      - *mev_commit_provider_node2_job
      - *mev_commit_provider_node2_funder_job
      - *mev_commit_provider_node3_job
      - *mev_commit_provider_node3_funder_job
      - *mev_commit_provider_emulator_nodes_job
      - *mev_commit_oracle_job
      - *mev_commit_bidder_node1_job
      - *mev_commit_bidder_node1_funder_job
      - *instant_bridge_job
      - *datadog_agent_metrics_collector_job

  preconf-rpc-test:
    jobs:
      - *artifacts_job
      - *datadog_agent_logs_collector_job
      - *otel_collector_job
      - *beacon_emulator_job
      - *mock_l1_job
      - *mev_commit_geth_bootnode1_job
      - *mev_commit_geth_signer_node1_job
      - *mev_commit_geth_member_node_job
      - *relay_emulator_job
      - *contracts_deployer_job
      - *mev_commit_bridge_job
      - *mev_commit_dashboard_job
      - *mev_commit_bootnode1_job
      - *mev_commit_provider_node1_job
      - *mev_commit_provider_node1_funder_job
      - *mev_commit_provider_node2_job
      - *mev_commit_provider_node2_funder_job
      - *mev_commit_provider_node3_job
      - *mev_commit_provider_node3_funder_job
      - *mev_commit_provider_emulator_nodes_job
      - *mev_commit_oracle_job
      - *mev_commit_bidder_node1_job
      - *mev_commit_bidder_node1_funder_job
      - *preconf_rpc_job
      - *datadog_agent_metrics_collector_job

  instant-bridge:
    jobs:
      - *artifacts_job
      - *datadog_agent_logs_collector_job
      - *mev_commit_bidder_node1_job
      - *instant_bridge_job

  archive:
    jobs:
      - *artifacts_job
      - *datadog_agent_logs_collector_job
      - *otel_collector_job
      - *mev_commit_geth_archive_node_job

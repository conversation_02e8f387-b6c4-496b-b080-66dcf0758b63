apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "bidder-emulator.fullname" . }}-scripts
  namespace: {{ .Values.namespace | default "default" }}
  labels:
    {{- include "bidder-emulator.labels" . | nindent 4 }}
data:
  setup.sh: |
    #!/bin/sh

    set -e

    # Configuration from environment variables
    KEYSTORE_PATH="${KEYSTORE_PATH:-/shared/keystores}"
    KEYSTORE_URLS="${KEYSTORE_DOWNLOAD_URLS}"  # Space/comma separated URLs
    MAX_RETRIES="${KEYSTORE_RETRIES:-3}"

    echo "=== Bidder Emulator Keystore Initialization ==="
    echo "Keystore path: $KEYSTORE_PATH"

    # Validate required environment variables
    if [ -z "$KEYSTORE_URLS" ]; then
        echo "Error: KEYSTORE_DOWNLOAD_URLS is required"
        exit 1
    fi

    echo "Keystore URLs to process: $KEYSTORE_URLS"

    # Create keystore directory if it doesn't exist
    mkdir -p "$KEYSTORE_PATH"

    # Function to extract address from filename using POSIX shell
    extract_address_from_filename() {
        local filename="$1"
        
        # Check if filename matches UTC--<timestamp>--<address> format
        case "$filename" in
            UTC--????-??-??T??-??-??.??????*Z--*)
                # Extract the address part after the last --
                address_part="${filename##*--}"
                # Validate address is 40 hex characters
                case "$address_part" in
                    *[!0-9a-fA-F]* | ????????????????????????????????????)
                        echo "Invalid address format: $address_part" >&2
                        return 1
                        ;;
                    ????????????????????????????????????????)
                        echo "$address_part"
                        return 0
                        ;;
                    *)
                        echo "Invalid address length: $address_part" >&2
                        return 1
                        ;;
                esac
                ;;
            *)
                echo "Invalid keystore filename format: $filename" >&2
                return 1
                ;;
        esac
    }

    # Function to extract address from keystore JSON content
    extract_address_from_content() {
        local file_path="$1"
        
        if command -v jq >/dev/null 2>&1; then
            jq -r '.address' "$file_path" 2>/dev/null || echo ""
        else
            # Extract address using grep and sed (fallback)
            grep -o '"address"[[:space:]]*:[[:space:]]*"[^"]*"' "$file_path" 2>/dev/null | \
            sed 's/.*"address"[[:space:]]*:[[:space:]]*"\([^"]*\)".*/\1/' || echo ""
        fi
    }

    # Function to normalize address (remove 0x prefix and convert to lowercase)
    normalize_address() {
        local addr="$1"
        # Remove 0x prefix if present and convert to lowercase
        echo "${addr#0x}" | tr '[:upper:]' '[:lower:]'
    }

    # Function to validate keystore file
    validate_keystore() {
        local file_path="$1"
        local filename="$2"
        
        echo "Validating keystore file: $file_path"
        
        if [ ! -f "$file_path" ]; then
            echo "Keystore file does not exist"
            return 1
        fi
        
        # Check if file is readable and not empty
        if [ ! -s "$file_path" ]; then
            echo "Keystore file is empty"
            return 1
        fi
        
        # Extract address from filename
        filename_address=$(extract_address_from_filename "$filename")
        if [ $? -ne 0 ]; then
            echo "✗ Invalid filename format"
            return 1
        fi
        
        # Normalize filename address
        filename_address=$(normalize_address "$filename_address")
        echo "Address from filename: $filename_address"
        
        # Check basic JSON structure first
        if command -v jq >/dev/null 2>&1; then
            echo "Using jq for JSON validation"
            
            # Validate JSON structure
            if ! jq -e '.address and .crypto and .crypto.cipher and .crypto.ciphertext and .crypto.kdf' "$file_path" >/dev/null 2>&1; then
                echo "✗ Invalid keystore JSON structure"
                return 1
            fi
            
            # Extract address from content
            content_address=$(extract_address_from_content "$file_path")
            if [ -z "$content_address" ]; then
                echo "✗ Could not extract address from keystore content"
                return 1
            fi
            
            # Normalize content address
            content_address=$(normalize_address "$content_address")
            echo "Address from content: $content_address"
            
            # Compare addresses (case-insensitive)
            if [ "$filename_address" = "$content_address" ]; then
                echo "✓ Keystore validation successful - addresses match: $filename_address"
                return 0
            else
                echo "✗ Address mismatch - filename: $filename_address, content: $content_address"
                return 1
            fi
        else
            echo "Using basic validation (jq not available)"
            
            # Basic validation without jq
            if ! grep -q '"crypto"' "$file_path" || \
               ! grep -q '"cipher"' "$file_path" || \
               ! grep -q '"ciphertext"' "$file_path" || \
               ! grep -q '"kdf"' "$file_path"; then
                echo "✗ Basic keystore structure validation failed"
                return 1
            fi
            
            # Extract address from content using basic tools
            content_address=$(extract_address_from_content "$file_path")
            if [ -z "$content_address" ]; then
                echo "✗ Could not extract address from keystore content"
                return 1
            fi
            
            # Normalize content address
            content_address=$(normalize_address "$content_address")
            echo "Address from content: $content_address"
            
            # Compare addresses (case-insensitive)
            if [ "$filename_address" = "$content_address" ]; then
                echo "✓ Basic keystore validation successful - addresses match: $filename_address"
                return 0
            else
                echo "✗ Address mismatch - filename: $filename_address, content: $content_address"
                return 1
            fi
        fi
    }

    # Function to download keystore with retries
    download_keystore() {
        local url="$1"
        local output_path="$2"
        local retries=0
        
        echo "Downloading keystore from: $url"
        
        # Download with retries
        while [ $retries -lt $MAX_RETRIES ]; do
            echo "Download attempt $((retries + 1))/$MAX_RETRIES"
            
            if curl -fsSL --connect-timeout 30 --max-time 300 -o "$output_path" "$url"; then
                echo "✓ Download successful"
                return 0
            else
                retries=$((retries + 1))
                if [ $retries -lt $MAX_RETRIES ]; then
                    echo "Download failed, retrying in 5 seconds..."
                    sleep 5
                else
                    echo "✗ Download failed after $MAX_RETRIES attempts"
                    return 1
                fi
            fi
        done
    }

    # Function to get filename from URL
    get_filename_from_url() {
        local url="$1"
        basename "$url"
    }

    # Function to process a single keystore
    process_keystore() {
        local url="$1"
        local keystore_index="$2"
        local temp_download="/tmp/keystore_temp_$(date +%s%N).json"
        
        echo "--- Processing keystore${keystore_index}: $url ---"
        
        # Get expected filename from URL
        local expected_filename=$(get_filename_from_url "$url")
        local keystore_dir="$KEYSTORE_PATH/keystore${keystore_index}"
        local keystore_file_path="$keystore_dir/$expected_filename"
        
        echo "Expected filename: $expected_filename"
        echo "Keystore directory: $keystore_dir"
        
        # Create keystore directory
        mkdir -p "$keystore_dir"
        
        # Validate filename format before proceeding
        if ! extract_address_from_filename "$expected_filename" >/dev/null 2>&1; then
            echo "✗ Invalid filename format in URL: $expected_filename"
            echo "Expected format: UTC--<timestamp>--<address>"
            return 1
        fi
        
        # Check if keystore already exists and is valid
        if [ -f "$keystore_file_path" ]; then
            if validate_keystore "$keystore_file_path" "$expected_filename"; then
                echo "✓ Valid keystore already exists - skipping download"
                return 0
            else
                echo "Removing invalid keystore file..."
                rm -f "$keystore_file_path"
            fi
        fi
        
        # Download keystore to temporary location first
        if ! download_keystore "$url" "$temp_download"; then
            echo "✗ Failed to download keystore from $url"
            rm -f "$temp_download"
            return 1
        fi
        
        # Validate downloaded keystore before moving it to final location
        if ! validate_keystore "$temp_download" "$expected_filename"; then
            echo "✗ Downloaded keystore validation failed for $url"
            rm -f "$temp_download"
            return 1
        fi
        
        # Move validated keystore to final location
        mv "$temp_download" "$keystore_file_path"
        
        # Set proper permissions
        chmod 600 "$keystore_file_path"
        
        # Extract final address for logging
        local final_address=$(extract_address_from_filename "$expected_filename")
        echo "✓ Keystore processed successfully"
        echo "✓ Keystore directory: $keystore_dir"
        echo "✓ Keystore file: $keystore_file_path"
        echo "✓ Address: $final_address"
        
        return 0
    }

    # Main processing logic
    PROCESSED_COUNT=0
    FAILED_COUNT=0
    PROCESSED_ADDRESSES=""

    # Convert space/comma separated URLs to newline separated for processing
    keystore_index=1
    echo "$KEYSTORE_URLS" | tr ' ,' '\n' | while IFS= read -r url; do
        # Skip empty lines
        [ -z "$url" ] && continue
        
        if process_keystore "$url" "$keystore_index"; then
            PROCESSED_COUNT=$((PROCESSED_COUNT + 1))
            # Extract address for summary
            filename=$(get_filename_from_url "$url")
            address=$(extract_address_from_filename "$filename" 2>/dev/null || echo "unknown")
            if [ -z "$PROCESSED_ADDRESSES" ]; then
                PROCESSED_ADDRESSES="$address"
            else
                PROCESSED_ADDRESSES="$PROCESSED_ADDRESSES, $address"
            fi
        else
            FAILED_COUNT=$((FAILED_COUNT + 1))
            echo "✗ Failed to process keystore from URL: $url"
        fi
        
        keystore_index=$((keystore_index + 1))
        echo ""
    done

    # Create a consolidated checksum file for all keystores
    if command -v sha256sum >/dev/null 2>&1; then
        find "$KEYSTORE_PATH" -name "*.json" -type f -exec sha256sum {} \; > "$KEYSTORE_PATH/.keystores.checksum" 2>/dev/null || true
        echo "✓ Consolidated checksum file created"
    fi

    # Final summary
    echo "=== Keystore Initialization Summary ==="
    echo "✓ Keystore directory: $KEYSTORE_PATH"
    echo "✓ Total keystores processed: $PROCESSED_COUNT"
    if [ $FAILED_COUNT -gt 0 ]; then
        echo "✗ Failed downloads: $FAILED_COUNT"
        exit 1
    else
        echo "✓ All keystores initialized successfully"
    fi

    # Clean up any loose keystore files in the root directory
    echo "Cleaning up any loose keystore files in root directory..."
    find "$KEYSTORE_PATH" -maxdepth 1 -name "UTC--*.json" -type f -delete 2>/dev/null || true

    # List all keystore directories for verification
    echo "✓ Available keystore directories:"
    for dir in "$KEYSTORE_PATH"/keystore*; do
        if [ -d "$dir" ]; then
            echo "  $(basename "$dir"):"
            ls -la "$dir"/*.json 2>/dev/null | sed 's/^/    /' || echo "    No keystore files found"
        fi
    done

    echo "✓ Keystore initialization completed successfully"

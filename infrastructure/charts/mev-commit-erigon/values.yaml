genesis:
  accountGeneration:
    enabled: false
    count: 10
    balance: "0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD4A51000FDA0FFFF"
    password: "password123"
    timestamp: "0x0"
  url: "https://storage.googleapis.com/devnet-artifacts/devnet-genesis/genesis.json"

erigon:
  networkId: 141414
  datadir: "/data/erigon"
  ports:
    http: 8545
    ws: 8546
    authrpc: 8551
    metrics: 6061
    privateApi: 9095
    p2p: 30303
  api:
    http:
      enabled: true
      addr: "0.0.0.0"
      vhosts: "*"
      corsdomain: "*"
      api: "eth,erigon,engine,net,web3,txpool"
    ws:
      enabled: true
      addr: "0.0.0.0"
      vhosts: "*"
    metrics:
      enabled: true
      addr: "0.0.0.0"
  zeroFeeTxList:
    - "******************************************"  # Contracts Deployer Keystore
    - "******************************************"  # Oracle Keystore
    #- "******************************************"  # Bridge Relayer Keystore
  pruneMode: "archive"
  nodiscover: true
  # Additional arguments to pass to erigon
  extraArgs:
    common: []  # Args applied to both leader and follower
    leader:
      - "--port=30303"
    follower:
      - "--port=30304"
      - "--staticpeers=enode://<EMAIL>:30303"
      - "--trustedpeers=enode://<EMAIL>:30303"

consensus:
  leader:
    # Leader node configuration
    instanceId: "snode-leader"
    apiAddr: ":9090"                    # Address for member node API endpoint
    healthAddr: ":8080"                 # Address for health check endpoint  
    priorityFeeRecipient: "******************************************"  # Ethereum address for receiving priority fees
    nonAuthRpcUrl: "http://localhost:8545"  # Non-authenticated Ethereum RPC URL
    
    # JWT secret for leader's Ethereum Execution client Engine API
    jwtSecret: "32c2902cc88b19b1c775ffef7e67673f296beb2e610f93230b40e5ef822ae9d4"
    
    # EVM build configuration
    evmBuildDelay: "1ms"                # Delay after initiating payload construction before calling getPayload
    evmBuildDelayEmptyBlock: "1m0s"     # Minimum time since last block to build an empty block
    txPoolPollingInterval: "5ms"        # Wait interval for polling the tx pool while there are no pending transactions
    
    # Database configuration (optional for leader)
    postgresDb:
      dsn: "postgres://mev_commit_consensus:<EMAIL>:5432/mev_commit_consensus?sslmode=disable"  # PostgreSQL DSN - if empty, saving to DB is disabled
    
    # Logging configuration
    logLevel: "info"     # Log level ('debug', 'info', 'warn', 'error')
    logFmt: "text"       # Log format ('text' or 'json')
    logTags: ""          # Comma-separated <name:value> log tags (e.g., env:prod,service:snode)
    
    # Additional arguments
    extraArgs: []

  follower:
    enabled: true
    replicaCount: 1
    
    # Follower node configuration
    instanceId: "snode-follower"
    healthAddr: ":8080"                 # Address for health check endpoint
    
    # JWT secret for follower's Ethereum Execution client Engine API  
    jwtSecret: "13373d9a0257983ad150392d7ddb2f9172c9396b4c450e26af469d123c7aaa5c"
    
    # Database configuration (REQUIRED for followers)
    postgresDb:
      dsn: "postgres://mev_commit_consensus:<EMAIL>:5432/mev_commit_consensus?sslmode=disable"  # REQUIRED: PostgreSQL DSN for follower
    
    # Sync configuration  
    syncBatchSize: 100                  # Number of payloads per request to the EL during sync
    
    # Logging configuration
    logLevel: "info"     # Log level ('debug', 'info', 'warn', 'error')
    logFmt: "text"       # Log format ('text' or 'json')
    logTags: ""          # Comma-separated <name:value> log tags (e.g., env:prod,service:snode)
    
    # Additional arguments
    extraArgs: []

image:
  erigon:
    repository: primev/primev
    tag: erigon-snode-v3.0.7-ms-patch
    pullPolicy: IfNotPresent
  snode:
    repository: primev/primev
    tag: snode-consensus-v3.0.7-retry-fix
    pullPolicy: IfNotPresent

storage:
  size: 100Gi
  storageClassName: openebs-nvme-disk2
  accessMode: ReadWriteOnce

security:
  runAsUser: 0
  runAsGroup: 0
  runAsNonRoot: false
  fsGroup: 0

replicaCount: 1
terminationGracePeriodSeconds: 300

# Resource requests and limits
resources:
  erigon:
    requests:
      cpu: "100m"
      memory: "500Mi"
    limits:
      cpu: "6000m"
      memory: "16Gi"
  snode:
    requests:
      cpu: "100m"
      memory: "100Mi"
    limits:
      cpu: "6000m"
      memory: "16Gi"

# Init container resources
initContainerResources:
  requests:
    cpu: "50m"
    memory: "100Mi"
  limits:
    cpu: "1000m"
    memory: "1Gi"

service:
  erigon:
    type: LoadBalancer
    ports:
      http: 8545
      ws: 8546
      authrpc: 8551
      metrics: 6061
    customSelector: {}
  snode:
    type: ClusterIP
    ports:
      api: 9090
      health: 8080
    customSelector: {}

nodeSelector: {}
tolerations: []
affinity: {}
topologySpreadConstraints: []

# Additional labels for StatefulSet
additionalLabels: {}

ingress:
  enabled: true
  className: "nginx"
  
  # Leader RPC Configuration
  rpc:
    host: dev-test.mev-commit.xyz
    path: /
    pathType: Prefix
    annotations:
      nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
      nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
      nginx.ingress.kubernetes.io/proxy-body-size: "0"
      nginx.ingress.kubernetes.io/proxy-read-timeout: "1200"
      nginx.ingress.kubernetes.io/proxy-send-timeout: "1200"
      nginx.ingress.kubernetes.io/ssl-redirect: "true"
  
  # Leader WebSocket Configuration  
  websocket:
    host: dev-test-wss.mev-commit.xyz
    path: /
    pathType: Prefix
    annotations:
      nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
      nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
      nginx.ingress.kubernetes.io/proxy-body-size: "0"
      nginx.ingress.kubernetes.io/proxy-read-timeout: "36h00"
      nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
      nginx.ingress.kubernetes.io/ssl-redirect: "true"
      nginx.ingress.kubernetes.io/proxy-http-version: "1.1"
      nginx.ingress.kubernetes.io/proxy-set-headers: |
        Connection "upgrade"
        Upgrade $http_upgrade
  
  # TLS Configuration
  tls:
    secretName: mev-commit-tls
  
  # Follower ingress (when follower.enabled=true)
  follower:
    enabled: false  # Set to true to expose follower via ingress
    
    rpc:
      host: "test-dev.mev-commit.xyz"
      path: /
      pathType: Prefix
      annotations:
        nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
        nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
        nginx.ingress.kubernetes.io/proxy-body-size: "0"
        nginx.ingress.kubernetes.io/proxy-read-timeout: "1200"
        nginx.ingress.kubernetes.io/proxy-send-timeout: "1200"
        nginx.ingress.kubernetes.io/ssl-redirect: "true"
    
    websocket:
      host: "test-dev.mev-commit.xyz"
      path: /
      pathType: Prefix
      annotations:
        nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
        nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
        nginx.ingress.kubernetes.io/proxy-body-size: "0"
        nginx.ingress.kubernetes.io/proxy-read-timeout: "36h00"
        nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
        nginx.ingress.kubernetes.io/ssl-redirect: "true"
        nginx.ingress.kubernetes.io/proxy-http-version: "1.1"
        nginx.ingress.kubernetes.io/proxy-set-headers: |
          Connection "upgrade"
          Upgrade $http_upgrade
    
    tls:
      secretName: "mev-commit-tls"

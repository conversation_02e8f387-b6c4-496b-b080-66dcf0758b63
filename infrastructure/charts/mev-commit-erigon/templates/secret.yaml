# Leader JWT Secret
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "erigon-snode.fullname" . }}-leader-jwt
  labels:
    {{- include "erigon-snode.labels" . | nindent 4 }}
    app.kubernetes.io/component: leader-jwt
type: Opaque
stringData:
  jwt: {{ .Values.consensus.leader.jwtSecret | quote }}
---
{{- if .Values.consensus.follower.enabled }}
# Follower JWT Secret
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "erigon-snode.fullname" . }}-follower-jwt
  labels:
    {{- include "erigon-snode.labels" . | nindent 4 }}
    app.kubernetes.io/component: follower-jwt
type: Opaque
stringData:
  jwt: {{ .Values.consensus.follower.jwtSecret | quote }}
{{- end }}

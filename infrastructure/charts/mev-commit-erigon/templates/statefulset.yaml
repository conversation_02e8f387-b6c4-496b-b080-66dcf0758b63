{{- include "erigon-snode.validate" . }}
---
# Leader StatefulSet
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {{ include "erigon-snode.fullname" . }}-leader
  labels:
    {{- include "erigon-snode.labels" . | nindent 4 }}
    app.kubernetes.io/component: leader
    {{- with .Values.additionalLabels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  serviceName: {{ include "erigon-snode.fullname" . }}-leader
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "erigon-snode.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: leader
  template:
    metadata:
      labels:
        {{- include "erigon-snode.selectorLabels" . | nindent 8 }}
        app.kubernetes.io/component: leader
        {{- with .Values.additionalLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.topologySpreadConstraints }}
      topologySpreadConstraints:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      terminationGracePeriodSeconds: {{ .Values.terminationGracePeriodSeconds }}
      volumes:
        {{- include "erigon-snode.volumes" (dict "root" . "component" "leader") | nindent 8 }}
      initContainers:
        {{- include "erigon-snode.initContainers" . | nindent 8 }}
      containers:
        {{- include "erigon-snode.erigonContainer" (dict "root" . "component" "leader") | nindent 8 }}
        {{- include "erigon-snode.snodeLeaderContainer" . | nindent 8 }}
  volumeClaimTemplates:
    {{- include "erigon-snode.volumeClaimTemplate" . | nindent 4 }}
---
{{- if .Values.consensus.follower.enabled }}
# Follower StatefulSet
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {{ include "erigon-snode.fullname" . }}-follower
  labels:
    {{- include "erigon-snode.labels" . | nindent 4 }}
    app.kubernetes.io/component: follower
    {{- with .Values.additionalLabels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  serviceName: {{ include "erigon-snode.fullname" . }}-follower
  replicas: {{ .Values.consensus.follower.replicaCount }}
  selector:
    matchLabels:
      {{- include "erigon-snode.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: follower
  template:
    metadata:
      labels:
        {{- include "erigon-snode.selectorLabels" . | nindent 8 }}
        app.kubernetes.io/component: follower
        {{- with .Values.additionalLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.topologySpreadConstraints }}
      topologySpreadConstraints:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      terminationGracePeriodSeconds: {{ .Values.terminationGracePeriodSeconds }}
      volumes:
        {{- include "erigon-snode.volumes" (dict "root" . "component" "follower") | nindent 8 }}
      initContainers:
        {{- include "erigon-snode.initContainers" . | nindent 8 }}
      containers:
        {{- include "erigon-snode.erigonContainer" (dict "root" . "component" "follower") | nindent 8 }}
        {{- include "erigon-snode.snodeFollowerContainer" . | nindent 8 }}
  volumeClaimTemplates:
    {{- include "erigon-snode.volumeClaimTemplate" . | nindent 4 }}
{{- end }}

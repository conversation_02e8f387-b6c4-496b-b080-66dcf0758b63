apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: {{ include "bridge-relayer.fullname" . }}-keystore
  labels:
    {{- include "bridge-relayer.labels" . | nindent 4 }}
  annotations:
    helm.sh/hook: pre-install,pre-upgrade
    helm.sh/hook-weight: "-2"
spec:
  refreshInterval: {{ .Values.keystore.refreshInterval | default "12h" }}
  secretStoreRef:
    name: {{ .Values.global.externalSecrets.secretStore }}
    kind: {{ .Values.global.externalSecrets.secretStoreKind | default "ClusterSecretStore" }}
  target:
    name: {{ include "bridge-relayer.fullname" . }}-keystore
    creationPolicy: Owner
  data:
    - secretKey: temp_keystore.json
      remoteRef:
        key: {{ .Values.keystore.awsSecretKey | default (printf "%s-keystore" (include "bridge-relayer.fullname" .)) }}
        property: {{ .Values.keystore.properties.keystore | default "bridge_relayer_keystore" }}
    - secretKey: filename.txt
      remoteRef:
        key: {{ .Values.keystore.awsSecretKey | default (printf "%s-keystore" (include "bridge-relayer.fullname" .)) }}
        property: {{ .Values.keystore.properties.keystoreFilename | default "bridge_relayer_keystore_filename" }}
    - secretKey: password.txt
      remoteRef:
        key: {{ .Values.keystore.awsSecretKey | default (printf "%s-keystore" (include "bridge-relayer.fullname" .)) }}
        property: {{ .Values.keystore.properties.keystorePassword | default "bridge_relayer_keystore_password" }}

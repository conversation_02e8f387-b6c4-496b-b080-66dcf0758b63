replicaCount: 1

image:
  repository:
  pullPolicy: IfNotPresent
  tag: ""

nameOverride: ""
fullnameOverride: ""

service:
  type: ClusterIP
  port: 8080

resources:
  limits:
    cpu: 4000m
    memory: 8Gi
  requests:
    cpu: 1000m
    memory: 2Gi

# Global configuration for External Secrets
global:
  externalSecrets:
    enabled: true
    secretStore: "aws-cluster-secret-store"
    secretStoreKind: "ClusterSecretStore"

# Bridge Relayer Configuration
config:
  httpPort: 8080
  logLevel: "info"
  logFormat: "json"
  logTags: "service.name:bridge-relayer"
  
  # RPC URLs
  l1RpcUrls: 
    - ""
  settlementRpcUrl: ""
  
  # Contract addresses (will be set as env var)
  l1ContractAddr: "0x00000000"
  settlementContractAddr: "0x0000000"
  
  # PG config
  postgresql:
    host: "pg-db.bridge.svc.cluster.local"
    port: 5432
    user: ""
    database: ""
    password: ""

# Keystore configuration - managed by External Secrets
keystore:
  refreshInterval: "12h"
  dir: "/app/keystore"
  retries: 3
  awsSecretKey: ""
  properties:
    keystore: ""
    keystoreFilename: ""
    keystorePassword: ""

probes:
  enabled: true
  liveness:
    path: "/health"
    initialDelaySeconds: 30
    periodSeconds: 10
  readiness:
    path: "/health"
    initialDelaySeconds: 5
    periodSeconds: 5

# values.yaml
replicaCount: 1

nameOverride: ""
fullnameOverride: "erigon-mev-commit-mock-l1"

image:
  repository: ethereum/client-go
  tag: "v1.15.11"
  pullPolicy: IfNotPresent

# Geth configuration
geth:

  dataDir: "/data/geth"
  networkId: 133787
  dev:
    enabled: true
    period: 12  # Block time in seconds
  ports:
    http: 8545
    ws: 8546
    p2p: 30303
  rpc:
    apis: "eth,net,web3,debug,admin,personal,txpool"
    cors: "*"
    vhosts: "*"
    wsOrigins: "*"
  logging:
    verbosity: 3
  allowInsecureUnlock: true
  # Extra arguments to pass to geth command
  extraArgs: []
    # - "--mine"
    # - "--miner.threads=1"
    # - "--gcmode=archive"
  passwordFile:
    enabled: true
    password: "password"

# Funding configuration
funding:
  enabled: true
  image:
    repository: ghcr.io/foundry-rs/foundry  # Image with cast tool
    tag: "latest"
    pullPolicy: IfNotPresent
  
  # Keystore configuration - uses the same password as geth
  keystore:
    password: "password"  # Same as geth.passwordFile.password
  
  # Funding parameters
  amount: "10000ether"
  gasPrice: "5000000000"  # 5 gwei
  
  # List of addresses to fund
  addresses:
    - "******************************************"
    - "******************************************"
    - "******************************************"
    - "******************************************"
    - "******************************************"
    - "******************************************"
    - "******************************************"
    - "******************************************"
    - "******************************************"
    - "******************************************"
    - "******************************************"
    - "******************************************"
    - "******************************************"
    - "******************************************"

# Storage
persistence:
  enabled: true
  storageClass: "openebs-nvme-disk2"
  accessMode: ReadWriteOnce
  size: 10Gi

# Service configuration
service:
  type: ClusterIP
  http:
    port: 8545
    targetPort: 8545
  ws:
    port: 8546
    targetPort: 8546

# Resource limits
resources:
  limits:
    cpu: 4000m
    memory: 14Gi
  requests:
    cpu: 50m
    memory: 100Mi

# Node selector and tolerations
#nodeSelector:
#  node.kubernetes.io/instance-type: c2-standard-8
tolerations: []
affinity: {}

# Pod annotations
podAnnotations: {}

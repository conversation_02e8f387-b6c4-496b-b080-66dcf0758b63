apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "relay-emulator.fullname" . }}
  namespace: {{ .Values.namespace | default "default" }}
  labels:
    {{- include "relay-emulator.labels" . | nindent 4 }}
  {{- with .Values.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  replicas: {{ .Values.job.replicas }}
  selector:
    matchLabels:
      {{- include "relay-emulator.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      labels:
        {{- include "relay-emulator.selectorLabels" . | nindent 8 }}
        {{- with .Values.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    spec:
      {{- include "relay-emulator.validateValues" . }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
        - name: {{ .Values.job.name }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command:
            - /usr/local/bin/relay-emulator
          args:
            - "--l1-rpc-url={{ .Values.job.l1RpcUrl }}"
            - "--http-port={{ .Values.job.httpPort }}"
            {{- if index .Values.job.env "log-format" }}
            - "--log-fmt={{ index .Values.job.env "log-format" }}"
            {{- end }}
            {{- if index .Values.job.env "log-level" }}
            - "--log-level={{ index .Values.job.env "log-level" }}"
            {{- end }}
            {{- if include "relay-emulator.logTags" . }}
            - "--log-tags={{ include "relay-emulator.logTags" . }}"
            {{- end }}
          env:
            - name: MOCK_RELAY_L1_RPC_URL
              value: {{ .Values.job.l1RpcUrl | quote }}
            - name: MOCK_RELAY_HTTP_PORT
              value: {{ .Values.job.httpPort | quote }}
            - name: MOCK_RELAY_LOG_FMT
              value: {{ index .Values.job.env "log-format" | default "json" | quote }}
            - name: MOCK_RELAY_LOG_LEVEL
              value: {{ index .Values.job.env "log-level" | default "info" | quote }}
            - name: MOCK_RELAY_LOG_TAGS
              value: {{ include "relay-emulator.logTags" . | quote }}
          ports:
            - containerPort: {{ .Values.job.httpPort }}
              name: http
              protocol: TCP
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          {{- if .Values.livenessProbe.enabled }}
          livenessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: {{ .Values.livenessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.livenessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.livenessProbe.timeoutSeconds }}
            failureThreshold: {{ .Values.livenessProbe.failureThreshold }}
          {{- end }}
          {{- if .Values.readinessProbe.enabled }}
          readinessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: {{ .Values.readinessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.readinessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.readinessProbe.timeoutSeconds }}
            failureThreshold: {{ .Values.readinessProbe.failureThreshold }}
          {{- end }}

# Relay Emulator Helm Chart Values

# Global settings
namespace: default

# Name overrides
nameOverride: ""
fullnameOverride: ""

# Job configuration
job:
  name: "relay-emulator"
  replicas: 1
  
  # Relay emulator specific configuration
  l1RpcUrl: "http://erigon-mev-commit-mock-l1.default.svc.cluster.local:8545"    # L1 RPC URL
  httpPort: 8080                       # HTTP port for relay APIs
  
  # Logging configuration
  env:
    log-format: "json"     # text or json
    log-level: "info"      # debug, info, warn, error
    log-tags: ""           # Additional comma-separated log tags

# Application image
image:
  repository: "primev/primev"
  tag: "rc-1.2.0-relay-emulator"
  pullPolicy: Always

# Service configuration
service:
  enabled: true
  type: ClusterIP
  port: 8080
  targetPort: 8080
  annotations: {}

# Resource configuration
resources:
  limits:
    cpu: 1000m
    memory: 2Gi
  requests:
    cpu: 250m
    memory: 256Mi

# Health checks
livenessProbe:
  enabled: false
  initialDelaySeconds: 30
  periodSeconds: 30
  timeoutSeconds: 10
  failureThreshold: 3

readinessProbe:
  enabled: false
  initialDelaySeconds: 10
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 3

# Pod scheduling and configuration
#nodeSelector:
#  workload-type: general
tolerations: []
affinity: {}

# Labels and annotations
annotations: {}
podLabels: {}
podAnnotations: {}

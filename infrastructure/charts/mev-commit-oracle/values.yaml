nameOverride: "erigon-oracle"
fullnameOverride: "erigon-oracle"

# Deployment settings
replicaCount: 1

image:
  repository: primev/primev
  pullPolicy: Always
  tag: "rc-1.2.0-mev-commit-oracle"

# Pod annotations for monitoring
podAnnotations:
  prometheus.io/scrape: "true"
  prometheus.io/port: "8080"
  prometheus.io/path: "/metrics"

# Service configuration
service:
  type: ClusterIP
  port: 8080
  annotations: {}

# Resource allocation
resources:
  limits:
    cpu: 4000m
    memory: 12Gi
  requests:
    cpu: 2000m
    memory: 4Gi

# Oracle configuration - matching your existing setup
oracle:
  logLevel: "info"
  logFormat: "json"
  logTags: "env:production,service:mev-oracle"
  httpPort: 8080
  gasLimit: "3000000"
  gasTipCap: "100000000"  # 0.1 gwei
  gasFeeCap: "120000000"  # 0.12 gwei
  laggerdMode: 12
  authToken: "4af6db58d4c783d76f5af8e8ad806f9c4960c5323793b48632e042a9693770a8"

# Network configuration
network:
  l1RpcUrls:
    - "http://erigon-mev-commit-mock-l1.default.svc.cluster.local:8545"
  settlementRpcHttp: "http://erigon-snode-leader-erigon.default.svc.cluster.local:8545"
  settlementRpcWs: "ws://erigon-snode-leader-erigon.default.svc.cluster.local:8546"
  relayUrls:
    - "http://erigon-mev-commit-relay-emulator-mev-commit-emulator.default.svc.cluster.local:8080"

# Contract addresses - matching your existing deployment
contracts:
  bidderRegistry: "0x6092Da89AEbC654436e040Aaae155f9ABFC78AD5"
  blockTracker: "0xD56D08FcDB654C110C2b4951b5D0eE20ad982320"
  oracle: "0x6b40dd5e7Bc93EA6802c9294eC2c29Cc360cfd16"
  preconf: "0xD1306A917EF2c1915209de38F2860300DE83835d" 
  providerRegistry: "0x4cbDB65b9a8B742f4d77d7E6C5cf5C616dF0fa73"


keystore:
  path: "/keystore"
  
  # URL download mode (awsEnabled: false)
  awsEnabled: false
  downloadUrl: "https://storage.googleapis.com/devnet-artifacts/keystores/erigon-keystores/keystore2/UTC--2025-06-24T18-20-44.554617000Z--1c533735c11dd317bc816629f86e00f479d097a3"
  retries: 3
  defaultPassword: "CXBMf4xEDO0I"  # Your existing keystore password
  
  # Storage configuration
  persistentVolumeClaim: erigon-mev-commit-oracle
  size: 10Mi
  storageClassName: "openebs-hostpath"
  
  # AWS configuration (not used when awsEnabled: false)
  refreshInterval: "12h"
  awsSecretKey: "testenv/testnet/mev-commit"
  properties:
    keystore: "oracle_keystore"
    keystoreFilename: "oracle_keystore_filename"
    keystorePassword: "oracle_keystore_password"

# External PostgreSQL configuration - matching your existing setup
postgresql:
  database: mev_oracle
  host: oracle-devnet-postgresql.default.svc.cluster.local
  password: TGvWZN9NSfEsgQOIoSLtoo2yhUnVSaqNUrA0nGfNILSDeZOC9kEZBcYbK2xg7zfB
  port: 5432
  username: mev_oracle

# Global configuration - DISABLED for URL download mode
global:
  externalSecrets:
    enabled: false  # Disabled since we're using URL download
    secretStore: "aws-cluster-secret-store"
    secretStoreKind: "ClusterSecretStore"

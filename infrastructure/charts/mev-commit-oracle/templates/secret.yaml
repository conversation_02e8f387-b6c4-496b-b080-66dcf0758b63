apiVersion: v1
kind: Secret
metadata:
  name: {{ include "mev-oracle.fullname" . }}-postgresql
  labels:
    {{- include "mev-oracle.labels" . | nindent 4 }}
type: Opaque
data:
  # PostgreSQL password
  MEV_ORACLE_PG_PASSWORD: {{ .Values.postgresql.password | b64enc | quote }}
  
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "mev-oracle.authTokenSecretName" . }}
  labels:
    {{- include "mev-oracle.labels" . | nindent 4 }}
type: Opaque
data:
  # Oracle API authentication token
  MEV_ORACLE_REGISTER_PROVIDER_API_AUTH_TOKEN: {{ .Values.oracle.authToken | b64enc | quote }}
  
{{- if and (not .Values.keystore.awsEnabled) (not .Values.keystore.existingSecret) }}
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "mev-oracle.keystoreSecretName" . }}
  labels:
    {{- include "mev-oracle.labels" . | nindent 4 }}
type: Opaque
data:
  # Keystore password (only created when <PERSON><PERSON> is disabled)
  MEV_ORACLE_KEYSTORE_PASSWORD: {{ .Values.keystore.defaultPassword | default "changeme" | b64enc | quote }}
{{- end }}

{{- if and .Values.keystore.awsEnabled .Values.global.externalSecrets.enabled }}
apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: {{ include "mev-oracle.fullname" . }}-keystore
  labels:
    {{- include "mev-oracle.labels" . | nindent 4 }}
  annotations:
    helm.sh/hook: pre-install,pre-upgrade
    helm.sh/hook-weight: "-2"
spec:
  refreshInterval: {{ .Values.keystore.refreshInterval | default "12h" }}
  secretStoreRef:
    name: {{ .Values.global.externalSecrets.secretStore }}
    kind: {{ .Values.global.externalSecrets.secretStoreKind | default "ClusterSecretStore" }}
  target:
    name: {{ include "mev-oracle.fullname" . }}-keystore
    creationPolicy: Owner
  data:
    - secretKey: temp_keystore.json
      remoteRef:
        key: {{ .Values.keystore.awsSecretKey | default (printf "%s-keystore" (include "mev-oracle.fullname" .)) }}
        property: {{ .Values.keystore.properties.keystore | default "mev_oracle_keystore" }}
    - secretKey: filename.txt
      remoteRef:
        key: {{ .Values.keystore.awsSecretKey | default (printf "%s-keystore" (include "mev-oracle.fullname" .)) }}
        property: {{ .Values.keystore.properties.keystoreFilename | default "mev_oracle_keystore_filename" }}
    - secretKey: password.txt
      remoteRef:
        key: {{ .Values.keystore.awsSecretKey | default (printf "%s-keystore" (include "mev-oracle.fullname" .)) }}
        property: {{ .Values.keystore.properties.keystorePassword | default "mev_oracle_keystore_password" }}
{{- end }}

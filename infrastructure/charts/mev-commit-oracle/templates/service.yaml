apiVersion: v1
kind: Service
metadata:
  name: {{ include "mev-oracle.fullname" . }}
  labels:
    {{- include "mev-oracle.labels" . | nindent 4 }}
  {{- with .Values.service.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "mev-oracle.selectorLabels" . | nindent 4 }}

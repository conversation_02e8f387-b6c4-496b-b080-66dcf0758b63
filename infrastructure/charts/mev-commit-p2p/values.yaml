# Default values for mev-commit-p2p.

nameOverride: ""
fullnameOverride: ""

global:
  # Docker image configuration
  image:
    repository: primev/primev
    tag: "rc-1.2.0-mev-commit"
    pullPolicy: Always

  # TLS certificate configuration
  tls:
    selfSigned: true
    existingSecret: ""
    forceRegenerate: false
    validityDays: 365

  # External Secret Operator configuration
  externalSecrets:
    enabled: false
    secretStore: "aws-cluster-secret-store"
    secretStoreKind: "ClusterSecretStore"
    nodeSecretKey: "mev-commit-node-secrets"
    tlsSecretKey: "mev-commit-tls"

  # Persistence configuration
  persistence:
    enabled: true
    storageClass: "openebs-nvme-disk1"
    dataSize: "10Gi"
    keystoreSize: "2Mi"

  # Logging configuration
  logging:
    format: "json"
    level: "info"
    logTags: "service.name:mev-commit-bootnode,service.version:1.2.0,environment:production,team:protocol"

  # Contract addresses
  contracts:
    bidderRegistry: "0x6092Da89AEbC654436e040Aaae155f9ABFC78AD5"
    blockTracker: "0xD56D08FcDB654C110C2b4951b5D0eE20ad982320"
    # oracle: "0xB241520A1a049e16Dd9193B1FCE23cB458f1f676"
    preconfStore: "0xD1306A917EF2c1915209de38F2860300DE83835d" 
    providerRegistry: "0x4cbDB65b9a8B742f4d77d7E6C5cf5C616dF0fa73"
    #validatorRouter: "******************************************"

  # Gas settings for transactions
  gas:
    limit: "2000000"
    tipCap: "50000000"
    feeCap: "60000000"

  # RPC endpoints
  rpc:
    settlementEndpoint: "http://erigon-snode-leader-erigon.default.svc.cluster.local:8545"
    settlementWsEndpoint: "ws://erigon-snode-leader-erigon.default.svc.cluster.local:8546"
    l1Endpoint: "http://erigon-mev-commit-mock-l1.default.svc.cluster.local:8545"

  # Notifications
  notifications:
    bufferCapacity: "100"

  # Ethereum chain configuration
  laggardMode: "10"

# Node configuration template (bootnode, provider, or bidder)
node:
  type: "bootnode"
  replicas: 1

  service:
    type: LoadBalancer
    annotations: {}
    nodePorts:
      http: null
      p2p: null
      rpc: null

  ports:
    http: 13523
    p2p: 13522
    rpc: 13524

  securityContext:
    capabilities:
      drop:
        - ALL
    readOnlyRootFilesystem: false
    runAsNonRoot: false
    runAsUser: 0

  podSecurityContext:
    fsGroup: 0

  resources:
    limits:
      cpu: 1000m
      memory: 2048Mi
    requests:
      cpu: 500m
      memory: 1024Mi

  bootnodeConnectionString: ""

  defaultKeystorePassword: "CXBMf4xEDO0I"
  defaultP2PSecret: ""
  existingSecret: ""

  nodeSelector: {}
  tolerations: []
  affinity: {}

  podAnnotations: {}
  imagePullSecrets: []

# Explicit config for use in ConfigMap
keystore:
  path: "/p2p-keystore"


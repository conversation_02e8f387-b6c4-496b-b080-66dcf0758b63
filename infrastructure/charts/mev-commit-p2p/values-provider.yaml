# Override values specific to provider deployment

global:
  # TLS configuration - disabled for bidder
  tls:
    disabled: false
    selfSigned: true

  # Contract addresses
  contracts:
    bidderRegistry: "0x6092Da89AEbC654436e040Aaae155f9ABFC78AD5"
    blockTracker: "0xD56D08FcDB654C110C2b4951b5D0eE20ad982320"
    # oracle: "0xB241520A1a049e16Dd9193B1FCE23cB458f1f676"
    preconfStore: "0xD1306A917EF2c1915209de38F2860300DE83835d" 
    providerRegistry: "0x4cbDB65b9a8B742f4d77d7E6C5cf5C616dF0fa73"
    #validatorRouter: "0x6c7A44A9A1836EF14129261D23166F7BD0196fb8"

  # Gas settings for transactions
  gas:
    limit: "2000000"
    tipCap: "50000000"
    feeCap: "60000000"

  # RPC endpoints
  rpc:
    settlementEndpoint: "http://erigon-snode-leader-erigon.default.svc.cluster.local:8545"
    settlementWsEndpoint: "ws://erigon-snode-leader-erigon.default.svc.cluster.local:8546"
    l1Endpoint: "http://erigon-mev-commit-mock-l1.default.svc.cluster.local:8545"

    

node:
  type: "provider"
  replicas: 1

  ports:
    http: 13623
    p2p: 13622
    rpc: 13624

  service:
    type: ClusterIP
    annotations: {}

  defaultKeystorePassword: "CXBMf4xEDO0I"
  defaultP2PSecret: "mev-commit-provider-secret"

  bootnodeConnectionString: "/ip4/*************/tcp/13522/p2p/16Uiu2HAmKxA6EYiYo6EUo91uAATGmMKjNydKVXhP2gTW6jPvyHN7"

  resources:
    limits:
      cpu: 16000m
      memory: 32Gi
    requests:
      cpu: 700m
      memory: 10Mi

  securityContext:
    capabilities:
      drop:
        - ALL
    readOnlyRootFilesystem: false
    runAsNonRoot: false
    runAsUser: 0

  podSecurityContext:
    fsGroup: 0

  nodeSelector: {}
  tolerations: []
  affinity: {}

  podAnnotations: {}
  imagePullSecrets: []

# Provider-specific configuration
provider:
  decisionTimeout: "30s"

#nodeSelector:
#  workload-type: general

keystore:
  path: "/keystore"
  downloadUrl: "https://storage.googleapis.com/devnet-artifacts/keystores/erigon-keystores/keystore5/UTC--2025-06-24T18-20-46.524919000Z--44b5a4b0c879844033e11b88b507816492ff98b0"

  
  # AWS Secrets Manager keystore (still enabled for keystore files)
  awsEnabled: false  # Keep AWS SM for keystore files only

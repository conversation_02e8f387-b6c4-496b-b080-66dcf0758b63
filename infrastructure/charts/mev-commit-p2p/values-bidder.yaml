# Override values specific to bidder deployment

global:
  # TLS configuration - disabled for bidder
  tls:
    disabled: false
    selfSigned: true

  # Contract addresses
  contracts:
    bidderRegistry: "0x6092Da89AEbC654436e040Aaae155f9ABFC78AD5"
    blockTracker: "0xD56D08FcDB654C110C2b4951b5D0eE20ad982320"
    # oracle: "0xB241520A1a049e16Dd9193B1FCE23cB458f1f676"
    preconfStore: "0xD1306A917EF2c1915209de38F2860300DE83835d" 
    providerRegistry: "0x4cbDB65b9a8B742f4d77d7E6C5cf5C616dF0fa73"
    #validatorRouter: "0x6c7A44A9A1836EF14129261D23166F7BD0196fb8"

  # Gas settings for transactions
  gas:
    limit: "2000000"
    tipCap: "50000000"
    feeCap: "60000000"

  # RPC endpoints
  rpc:
    settlementEndpoint: "http://erigon-snode-leader-erigon.default.svc.cluster.local:8545"
    settlementWsEndpoint: "ws://erigon-snode-leader-erigon.default.svc.cluster.local:8546"
    l1Endpoint: "http://erigon-mev-commit-mock-l1.default.svc.cluster.local:8545"

 
node:
  type: "bidder"
  replicas: 1

  ports:
    http: 13723
    p2p: 13722
    rpc: 13724

  service:
    type: ClusterIP
    annotations: {}

  defaultKeystorePassword: "CXBMf4xEDO0I"
  defaultP2PSecret: ""

  bootnodeConnectionString: "/ip4/*************/tcp/13522/p2p/16Uiu2HAmKxA6EYiYo6EUo91uAATGmMKjNydKVXhP2gTW6jPvyHN7"
  resources:
    limits:
      cpu: 6000m
      memory: 12Gi
    requests:
      cpu: 280m
      memory: 10Mi

  securityContext:
    capabilities:
      drop:
        - ALL
    readOnlyRootFilesystem: false
    runAsNonRoot: false
    runAsUser: 0

  podSecurityContext:
    fsGroup: 0

  nodeSelector: {}
  tolerations: []
  affinity: {}

  podAnnotations: {}
  imagePullSecrets: []

# Bidder-specific configuration
bidder:
  bidTimeout: "60s"

  autodeposit:
    enabled: false
    amount: ""  # leave empty or set to a value like "0.01"

  providerWhitelist: ""  # comma-separated addresses, e.g. "0xabc...,0xdef..."

keystore:
  path: "/keystore"
  downloadUrl: "https://storage.googleapis.com/devnet-artifacts/keystores/erigon-keystores/keystore4/UTC--2025-06-24T18-20-45.876172000Z--cede115ffaa36313bde4e3dcf1fc50869b308791"
  retries: 3

#nodeSelector:
#  workload-type: general

{{- if .Values.global.externalSecrets.enabled }}
---
{{- if and (not .Values.global.tls.selfSigned) (not .Values.global.tls.disabled) }}
# External Secret for TLS certificates (only if not using self-signed certs and TLS is not disabled)
apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: {{ include "mev-commit-p2p.fullname" . }}-tls-secret
  labels:
    {{- include "mev-commit-p2p.labels" . | nindent 4 }}
spec:
  refreshInterval: "24h"
  secretStoreRef:
    name: {{ .Values.global.externalSecrets.secretStore }}
    kind: {{ .Values.global.externalSecrets.secretStoreKind | default "ClusterSecretStore" }}
  target:
    name: {{ include "mev-commit-p2p.fullname" . }}-tls
    creationPolicy: Owner
  data:
    - secretKey: tls.crt
      remoteRef:
        key: {{ .Values.global.externalSecrets.tlsSecretKey | default (printf "%s-tls" (include "mev-commit-p2p.fullname" .)) }}
        property: tls.crt
    - secretKey: tls.key
      remoteRef:
        key: {{ .Values.global.externalSecrets.tlsSecretKey | default (printf "%s-tls" (include "mev-commit-p2p.fullname" .)) }}
        property: tls.key
{{- end }}

---
# External Secret for node secrets (passwords, P2P secrets) - only when ESO is enabled
apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: {{ include "mev-commit-p2p.fullname" . }}-node-secrets
  labels:
    {{- include "mev-commit-p2p.labels" . | nindent 4 }}
spec:
  refreshInterval: "12h"
  secretStoreRef:
    name: {{ .Values.global.externalSecrets.secretStore }}
    kind: {{ .Values.global.externalSecrets.secretStoreKind | default "ClusterSecretStore" }}
  target:
    name: {{ include "mev-commit-p2p.fullname" . }}-{{ .Values.node.type }}-secrets
    creationPolicy: Owner
  data:
    - secretKey: MEV_COMMIT_KEYSTORE_PASSWORD
      remoteRef:
        key: {{ .Values.global.externalSecrets.nodeSecretKey | default (printf "%s-node-secrets" (include "mev-commit-p2p.fullname" .)) }}
        property: {{ .Values.global.externalSecrets.nodeSecretProperties.keystorePassword | default "keystore-password" }}
    - secretKey: MEV_COMMIT_SECRET
      remoteRef:
        key: {{ .Values.global.externalSecrets.nodeSecretKey | default (printf "%s-node-secrets" (include "mev-commit-p2p.fullname" .)) }}
        property: {{ .Values.global.externalSecrets.nodeSecretProperties.p2pSecret | default "p2p-secret" }}
{{- end }}

{{- if .Values.keystore.awsEnabled }}
---
# External Secret for keystore files (independent of global ESO setting)
apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: {{ include "mev-commit-p2p.fullname" . }}-{{ .Values.node.type }}-keystore
  labels:
    {{- include "mev-commit-p2p.labels" . | nindent 4 }}
  annotations:
    helm.sh/hook: pre-install,pre-upgrade
    helm.sh/hook-weight: "-2"
spec:
  refreshInterval: {{ .Values.keystore.refreshInterval | default "12h" }}
  secretStoreRef:
    name: {{ .Values.global.externalSecrets.secretStore }}
    kind: {{ .Values.global.externalSecrets.secretStoreKind | default "ClusterSecretStore" }}
  target:
    name: {{ include "mev-commit-p2p.fullname" . }}-{{ .Values.node.type }}-keystore
    creationPolicy: Owner
  data:
    - secretKey: temp_keystore.json
      remoteRef:
        key: {{ .Values.keystore.awsSecretKey | default (printf "%s-%s-keystore" (include "mev-commit-p2p.fullname" .) .Values.node.type) }}
        property: {{ .Values.keystore.properties.keystore | default "keystore-json" }}
    - secretKey: filename.txt
      remoteRef:
        key: {{ .Values.keystore.awsSecretKey | default (printf "%s-%s-keystore" (include "mev-commit-p2p.fullname" .) .Values.node.type) }}
        property: {{ .Values.keystore.properties.keystoreFilename | default "keystore-filename" }}
{{- end }}

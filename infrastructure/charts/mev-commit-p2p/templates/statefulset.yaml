apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {{ include "mev-commit-p2p.fullname" . }}-{{ .Values.node.type }}
  labels:
    app.kubernetes.io/name: {{ include "mev-commit-p2p.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/component: {{ .Values.node.type }}
    {{- include "mev-commit-p2p.labels" . | nindent 4 }}
spec:
  serviceName: {{ include "mev-commit-p2p.fullname" . }}-{{ .Values.node.type }}
  replicas: {{ .Values.node.replicas }}
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "mev-commit-p2p.name" . }}
      app.kubernetes.io/instance: {{ .Release.Name }}
      app.kubernetes.io/component: {{ .Values.node.type }}
  template:
    metadata:
      labels:
        app.kubernetes.io/name: {{ include "mev-commit-p2p.name" . }}
        app.kubernetes.io/instance: {{ .Release.Name }}
        app.kubernetes.io/component: {{ .Values.node.type }}
    spec:
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      securityContext:
        {{- toYaml .Values.node.podSecurityContext | nindent 8 }}

      initContainers:
        {{- if .Values.keystore.awsEnabled }}
        # AWS SM Keystore Init Container (when keystore.awsEnabled is true)
        - name: keystore-aws-init
          image: "busybox:latest"
          imagePullPolicy: IfNotPresent
          command:
            - sh
            - -c
            - |
              echo "=== AWS SM Keystore Initialization ==="
              FILENAME=$(cat /temp/filename.txt)
              echo "Keystore filename: $FILENAME"
              echo "Copying keystore to: {{ .Values.keystore.path }}/$FILENAME"
              
              # Create keystore directory
              mkdir -p "{{ .Values.keystore.path }}"
              
              # Copy keystore with proper filename
              cp /temp/temp_keystore.json "{{ .Values.keystore.path }}/$FILENAME"
              
              # Set proper permissions
              chmod 600 "{{ .Values.keystore.path }}/$FILENAME"
              
              echo "✓ AWS SM keystore initialization completed"
              echo "✓ Keystore file: {{ .Values.keystore.path }}/$FILENAME"
          volumeMounts:
            - name: keystore
              mountPath: {{ .Values.keystore.path }}
            - name: temp-keystore
              mountPath: /temp
          securityContext:
            runAsUser: 0
            runAsGroup: 0
            runAsNonRoot: false
        {{- else }}
        # URL-based Keystore Init Container (when keystore.awsEnabled is false)
        - name: keystore-url-init
          image: "alpine/curl:8.5.0"
          imagePullPolicy: IfNotPresent
          command: ["/bin/sh", "/scripts/keystore-init.sh"]
          env:
            - name: KEYSTORE_PATH
              value: "{{ .Values.keystore.path }}"
            - name: KEYSTORE_DOWNLOAD_URL
              value: "{{ .Values.keystore.downloadUrl }}"
            - name: KEYSTORE_RETRIES
              value: "{{ .Values.keystore.retries | default "3" }}"
          volumeMounts:
            - name: keystore
              mountPath: {{ .Values.keystore.path }}
            - name: init-scripts
              mountPath: /scripts
          securityContext:
            runAsUser: 0
            runAsGroup: 0
            runAsNonRoot: false
        {{- end }}

      containers:
        - name: {{ .Values.node.type }}
          image: "{{ .Values.global.image.repository }}:{{ .Values.global.image.tag }}"
          imagePullPolicy: {{ .Values.global.image.pullPolicy }}
          command: ["mev-commit"]
          ports:
            - name: http
              containerPort: {{ .Values.node.ports.http }}
              protocol: TCP
            - name: p2p
              containerPort: {{ .Values.node.ports.p2p }}
              protocol: TCP
            - name: rpc
              containerPort: {{ .Values.node.ports.rpc }}
              protocol: TCP
          env:
            - name: MEV_COMMIT_NAT_ADDR
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
          envFrom:
            - configMapRef:
                name: {{ include "mev-commit-p2p.fullname" . }}-{{ .Values.node.type }}-config
            - secretRef:
                name: {{ include "mev-commit-p2p.fullname" . }}-{{ .Values.node.type }}-secrets
          resources:
            {{- toYaml .Values.node.resources | nindent 12 }}
          securityContext:
            {{- toYaml .Values.node.securityContext | nindent 12 }}
          volumeMounts:
            - name: data
              mountPath: /data
            - name: keystore
              mountPath: {{ .Values.keystore.path }}
            {{- if not .Values.global.tls.disabled }}
            - name: tls-certs
              mountPath: /certs
            {{- end }}

      volumes:
        {{- if not .Values.global.tls.disabled }}
        - name: tls-certs
          secret:
            secretName: {{ include "mev-commit-p2p.fullname" . }}-tls
        {{- end }}
        {{- if .Values.keystore.awsEnabled }}
        # AWS SM volumes
        - name: temp-keystore
          secret:
            secretName: {{ include "mev-commit-p2p.fullname" . }}-{{ .Values.node.type }}-keystore
        {{- else }}
        # URL-based volumes
        - name: init-scripts
          configMap:
            name: {{ include "mev-commit-p2p.fullname" . }}-{{ .Values.node.type }}-init
            defaultMode: 0755
        {{- end }}

  volumeClaimTemplates:
    - metadata:
        name: data
      spec:
        accessModes: ["ReadWriteOnce"]
        storageClassName: {{ .Values.global.persistence.storageClass | default "premium-rwo" }}
        resources:
          requests:
            storage: {{ .Values.global.persistence.dataSize | default "10Gi" }}
    - metadata:
        name: keystore
      spec:
        accessModes: ["ReadWriteOnce"]
        storageClassName: {{ .Values.global.persistence.storageClass | default "premium-rwo" }}
        resources:
          requests:
            storage: {{ .Values.global.persistence.keystoreSize | default "5Mi" }}

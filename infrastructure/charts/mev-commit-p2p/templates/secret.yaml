{{- if and .Values.global.tls.selfSigned (not .Values.global.tls.disabled) }}
{{- $secretName := printf "%s-tls" (include "mev-commit-p2p.fullname" .) }}
{{- $existingSecret := lookup "v1" "Secret" .Release.Namespace $secretName }}
{{- $altNames := list (printf "%s-%s" (include "mev-commit-p2p.fullname" .) .Values.node.type) (printf "%s-%s.%s" (include "mev-commit-p2p.fullname" .) .Values.node.type .Release.Namespace) (printf "%s-%s.%s.svc" (include "mev-commit-p2p.fullname" .) .Values.node.type .Release.Namespace) }}
{{- $forceRegenerate := .Values.global.tls.forceRegenerate | default false }}

{{- if and $existingSecret (not $forceRegenerate) }}
# Preserve existing TLS certificate
apiVersion: v1
kind: Secret
metadata:
  name: {{ $secretName }}
  labels:
    app.kubernetes.io/name: {{ include "mev-commit-p2p.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    {{- include "mev-commit-p2p.labels" . | nindent 4 }}
  annotations:
    # Track certificate generation
    {{- if $existingSecret.metadata.annotations }}
    {{- if index $existingSecret.metadata.annotations "mev-commit.io/cert-created" }}
    mev-commit.io/cert-created: {{ index $existingSecret.metadata.annotations "mev-commit.io/cert-created" | quote }}
    {{- else }}
    mev-commit.io/cert-created: {{ now | quote }}
    {{- end }}
    {{- else }}
    mev-commit.io/cert-created: {{ now | quote }}
    {{- end }}
    mev-commit.io/cert-preserved: {{ now | quote }}
type: kubernetes.io/tls
data:
  tls.crt: {{ index $existingSecret.data "tls.crt" }}
  tls.key: {{ index $existingSecret.data "tls.key" }}
{{- else }}
# Generate new self-signed certificate
{{- $validityDays := .Values.global.tls.validityDays | default 365 | int }}
{{- $ca := genCA "mev-commit-ca" $validityDays }}
{{- $cert := genSignedCert (include "mev-commit-p2p.fullname" .) nil $altNames $validityDays $ca }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ $secretName }}
  labels:
    app.kubernetes.io/name: {{ include "mev-commit-p2p.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    {{- include "mev-commit-p2p.labels" . | nindent 4 }}
  annotations:
    # Track certificate generation
    mev-commit.io/cert-created: {{ now | quote }}
    mev-commit.io/cert-validity-days: {{ $validityDays | quote }}
    {{- if $forceRegenerate }}
    mev-commit.io/cert-force-regenerated: {{ now | quote }}
    {{- end }}
type: kubernetes.io/tls
data:
  tls.crt: {{ $cert.Cert | b64enc }}
  tls.key: {{ $cert.Key | b64enc }}
{{- end }}
{{- else if .Values.global.tls.disabled }}
---
# Create empty TLS secret when TLS is disabled (for compatibility)
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "mev-commit-p2p.fullname" . }}-tls
  labels:
    app.kubernetes.io/name: {{ include "mev-commit-p2p.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    {{- include "mev-commit-p2p.labels" . | nindent 4 }}
  annotations:
    mev-commit.io/tls-disabled: "true"
type: Opaque
data:
  tls.crt: ""
  tls.key: ""
{{- end }}

{{- if and (not .Values.global.externalSecrets.enabled) (not .Values.node.existingSecret) }}
---
# Create default secrets if neither ESO nor existing secrets are specified
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "mev-commit-p2p.fullname" . }}-{{ .Values.node.type }}-secrets
  labels:
    app.kubernetes.io/name: {{ include "mev-commit-p2p.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/component: {{ .Values.node.type }}
    {{- include "mev-commit-p2p.labels" . | nindent 4 }}
type: Opaque
data:
  MEV_COMMIT_KEYSTORE_PASSWORD: {{ .Values.node.defaultKeystorePassword | default "changeme" | b64enc }}
  MEV_COMMIT_SECRET: {{ .Values.node.defaultP2PSecret | default (printf "mev-commit-%s-secret" .Values.node.type) | b64enc }}
{{- end }}

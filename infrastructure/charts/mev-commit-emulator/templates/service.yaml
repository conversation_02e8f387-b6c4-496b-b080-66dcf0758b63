{{- if .Values.service.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "emulator.fullname" . }}
  namespace: {{ .Values.namespace | default "default" }}
  labels:
    {{- include "emulator.labels" . | nindent 4 }}
  {{- with .Values.service.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.targetPort }}
      protocol: TCP
      name: metrics
  selector:
    {{- include "emulator.selectorLabels" . | nindent 4 }}
{{- end }}

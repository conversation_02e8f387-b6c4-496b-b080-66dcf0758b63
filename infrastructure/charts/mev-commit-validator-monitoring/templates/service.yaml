apiVersion: v1
kind: Service
metadata:
  name: {{ include "mev-commit-validator-monitor.fullname" . }}
  namespace: {{ .Values.namespace | default "default" }}
  labels:
    {{- include "mev-commit-validator-monitor.labels" . | nindent 4 }}
  {{- with .Values.commonAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.targetPort }}
      protocol: TCP
      name: health
  selector:
    {{- if .Values.service.customSelectors }}
    {{- toYaml .Values.service.customSelectors | nindent 4 }}
    {{- else }}
    {{- include "mev-commit-validator-monitor.selectorLabels" . | nindent 4 }}
    {{- end }}

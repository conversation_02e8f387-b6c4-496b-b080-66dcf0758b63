apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "mev-commit-validator-monitor.fullname" . }}
  namespace: {{ .Values.namespace | default "default" }}
  labels:
    {{- include "mev-commit-validator-monitor.labels" . | nindent 4 }}
  {{- with .Values.commonAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  replicas: {{ .Values.replicaCount }}
  strategy:
    type: Recreate
  selector:
    matchLabels:
      {{- include "mev-commit-validator-monitor.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      labels:
        {{- include "mev-commit-validator-monitor.selectorLabels" . | nindent 8 }}
        {{- with .Values.commonLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- with .Values.commonAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    spec:
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
      - name: validator-monitor
        image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
        imagePullPolicy: {{ .Values.image.pullPolicy }}
        ports:
        - containerPort: {{ .Values.monitoring.healthPort | int }}
          name: health
        envFrom:
        - configMapRef:
            name: {{ include "mev-commit-validator-monitor.fullname" . }}-config
        env:
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: {{ include "mev-commit-validator-monitor.dbSecretName" . }}
              key: db-user
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: {{ include "mev-commit-validator-monitor.dbSecretName" . }}
              key: db-password
        - name: WEBHOOK_URLS
          valueFrom:
            secretKeyRef:
              name: {{ include "mev-commit-validator-monitor.slackSecretName" . }}
              key: webhook-url

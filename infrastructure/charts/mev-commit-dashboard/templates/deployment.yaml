apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "mev-commit-dashboard.fullname" . }}
  namespace: {{ .Values.namespace | default "default" }}
  labels:
    {{- include "mev-commit-dashboard.labels" . | nindent 4 }}
  {{- with .Values.commonAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "mev-commit-dashboard.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      labels:
        {{- include "mev-commit-dashboard.selectorLabels" . | nindent 8 }}
        {{- with .Values.commonLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- with .Values.commonAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    spec:
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
      - name: dashboard
        image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
        imagePullPolicy: {{ .Values.image.pullPolicy }}
        ports:
        - containerPort: {{ .Values.service.port }}
          name: http
        envFrom:
        - configMapRef:
            name: {{ include "mev-commit-dashboard.fullname" . }}-config
        resources:
          requests:
            cpu: {{ .Values.resources.requests.cpu | default "100m" }}
            memory: {{ .Values.resources.requests.memory | default "128Mi" }}
          limits:
            cpu: {{ .Values.resources.limits.cpu | default "500m" }}
            memory: {{ .Values.resources.limits.memory | default "512Mi" }}

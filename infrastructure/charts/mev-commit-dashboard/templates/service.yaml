apiVersion: v1
kind: Service
metadata:
  name: {{ include "mev-commit-dashboard.fullname" . }}
  namespace: {{ .Values.namespace | default "default" }}
  labels:
    {{- include "mev-commit-dashboard.labels" . | nindent 4 }}
  {{- with .Values.commonAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.targetPort }}
      protocol: TCP
      name: http
  selector:
    {{- if .Values.service.customSelectors }}
    {{- toYaml .Values.service.customSelectors | nindent 4 }}
    {{- else }}
    {{- include "mev-commit-dashboard.selectorLabels" . | nindent 4 }}
    {{- end }}

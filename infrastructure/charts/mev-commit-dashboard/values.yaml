replicaCount: 1

image:
  repository: primev/primev
  tag: "rc-1.2.0-mev-commit-dashboard"
  pullPolicy: Always

nameOverride: ""
fullnameOverride: "erigon-mev-commit-dashboard-snode"

# Namespace to deploy resources
namespace: default

# Common labels to apply to all resources
commonLabels: {}
  # team: blockchain
  # environment: production

# Common annotations to apply to all resources
commonAnnotations: {}
  # prometheus.io/scrape: "true"
  # prometheus.io/port: "8080"

# Node selector for pod assignment
nodeSelector: {}
  # disktype: ssd
  # kubernetes.io/os: linux

# Service configuration
service:
  type: ClusterIP
  port: 8080
  targetPort: 8080
  # Custom selectors for the service (if empty, uses the default selector labels)
  customSelectors: {}
    # app: mev-commit-dashboard
    # tier: frontend

# resource configuration 
resources:
  requests:
    cpu: "100m"
    memory: "50Mi"
  limits:
    cpu: "1000m"
    memory: "256Mi"

# Configuration for the Dashboard
config:
  rpcUrl: "ws://erigon-snode-leader-erigon.default.svc.cluster.local:8546"
  httpPort: "8080"
  startBlock: "0"
  logFormat: "json"
  logLevel: "info"
  bidderregistryContractAddr: "******************************************"
  blocktrackerContractAddr: "******************************************"
  oracleContractAddr: "******************************************"
  preconfContractAddr: "******************************************"
  providerregistryContractAddr: "0x4cbDB65b9a8B742f4d77d7E6C5cf5C616dF0fa73"

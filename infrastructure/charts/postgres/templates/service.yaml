# templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: {{ include "postgresql.fullname" . }}
  labels:
    {{- include "postgresql.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: postgresql
      protocol: TCP
      name: postgresql
  selector:
    {{- if .Values.service.customSelector }}
    {{- toYaml .Values.service.customSelector | nindent 4 }}
    {{- else }}
    {{- include "postgresql.selectorLabels" . | nindent 4 }}
    {{- end }}

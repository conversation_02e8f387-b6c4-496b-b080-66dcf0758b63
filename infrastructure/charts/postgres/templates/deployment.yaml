apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "postgresql.fullname" . }}
  labels:
    {{- include "postgresql.labels" . | nindent 4 }}
    {{- with .Values.additionalLabels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  replicas: {{ .Values.replicaCount }}
  strategy:
    type: {{ .Values.strategy.type }}
  selector:
    matchLabels:
      {{- include "postgresql.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      labels:
        {{- include "postgresql.selectorLabels" . | nindent 8 }}
        {{- with .Values.additionalLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      securityContext:
        {{- toYaml .Values.securityContext | nindent 8 }}
      {{- if .Values.persistence.enabled }}
      initContainers:
        - name: init-permissions
          image: busybox:1.36
          command:
            - sh
            - -c
            - |
              mkdir -p /var/lib/postgresql/data/pgdata
              chown -R 999:999 /var/lib/postgresql/data
              chmod -R 750 /var/lib/postgresql/data
          volumeMounts:
            - name: postgresql-data
              mountPath: /var/lib/postgresql/data
          securityContext:
            runAsUser: 0
      {{- end }}
      containers:
        - name: postgresql
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: postgresql
              containerPort: 5432
              protocol: TCP
          env:
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "postgresql.fullname" . }}-secret
                  key: postgres-password
            - name: PGDATA
              value: /var/lib/postgresql/data/{{ .Values.persistence.subPath }}
          envFrom:
            - configMapRef:
                name: {{ include "postgresql.fullname" . }}-config
          volumeMounts:
            {{- if .Values.persistence.enabled }}
            - name: postgresql-data
              mountPath: /var/lib/postgresql/data
            {{- end }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          {{- if .Values.probes.liveness.enabled }}
          livenessProbe:
            exec:
              command:
                - /bin/sh
                - -c
                - pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}
            initialDelaySeconds: {{ .Values.probes.liveness.initialDelaySeconds }}
            periodSeconds: {{ .Values.probes.liveness.periodSeconds }}
            timeoutSeconds: {{ .Values.probes.liveness.timeoutSeconds }}
            failureThreshold: {{ .Values.probes.liveness.failureThreshold }}
            successThreshold: {{ .Values.probes.liveness.successThreshold }}
          {{- end }}
          {{- if .Values.probes.readiness.enabled }}
          readinessProbe:
            exec:
              command:
                - /bin/sh
                - -c
                - pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}
            initialDelaySeconds: {{ .Values.probes.readiness.initialDelaySeconds }}
            periodSeconds: {{ .Values.probes.readiness.periodSeconds }}
            timeoutSeconds: {{ .Values.probes.readiness.timeoutSeconds }}
            failureThreshold: {{ .Values.probes.readiness.failureThreshold }}
            successThreshold: {{ .Values.probes.readiness.successThreshold }}
          {{- end }}
      volumes:
        {{- if .Values.persistence.enabled }}
        - name: postgresql-data
          persistentVolumeClaim:
            claimName: {{ include "postgresql.fullname" . }}-pvc
        {{- end }}

---
# ExternalSecret for AWS SM - Keystore + Filename
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: {{ include "preconf-rpc.fullname" . }}-keystore 
  labels:
    {{- include "preconf-rpc.labels" . | nindent 4 }}
  annotations:
    helm.sh/hook: pre-install,pre-upgrade
    helm.sh/hook-weight: "-2"
spec:
  refreshInterval: {{ .Values.keystore.refreshInterval }}
  secretStoreRef:
    name: {{ .Values.keystore.secretStore.name }}
    kind: {{ .Values.keystore.secretStore.kind }}
  target:
    name: {{ include "preconf-rpc.fullname" . }}-keystore
    creationPolicy: Owner
  data:
    - secretKey: temp_keystore.json
      remoteRef:
        key: {{ .Values.keystore.awsSecretName }}
        property: {{ .Values.keystore.properties.keystore }}
    - secretKey: filename.txt
      remoteRef:
        key: {{ .Values.keystore.awsSecretName }}
        property: {{ .Values.keystore.properties.keystoreFilename }}

---
# ExternalSecret for keystore password
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: {{ include "preconf-rpc.fullname" . }}-keystore-password
  labels:
    {{- include "preconf-rpc.labels" . | nindent 4 }}
  annotations:
    helm.sh/hook: pre-install,pre-upgrade
    helm.sh/hook-weight: "-2"
spec:
  refreshInterval: {{ .Values.keystore.refreshInterval }}
  secretStoreRef:
    name: {{ .Values.keystore.secretStore.name }}
    kind: {{ .Values.keystore.secretStore.kind }}
  target:
    name: {{ include "preconf-rpc.fullname" . }}-keystore-password
    creationPolicy: Owner
  data:
    - secretKey: password
      remoteRef:
        key: {{ .Values.keystore.awsSecretName }}
        property: {{ .Values.keystore.properties.keystorePassword }}

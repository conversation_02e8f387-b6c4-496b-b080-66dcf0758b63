apiVersion: v1
kind: Service
metadata:
  name: {{ include "points-service.fullname" . }}
  labels:
    {{- include "points-service.labels" . | nindent 4 }}
  {{- with .Values.commonAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.targetPort }}
      protocol: TCP
      name: http
  selector:
    {{- if .Values.service.customSelectors }}
    {{- toYaml .Values.service.customSelectors | nindent 4 }}
    {{- else }}
    {{- include "points-service.selectorLabels" . | nindent 4 }}
    {{- end }}

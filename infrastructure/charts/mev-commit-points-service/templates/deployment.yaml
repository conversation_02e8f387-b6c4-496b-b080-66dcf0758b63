apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "points-service.fullname" . }}
  labels:
    {{- include "points-service.labels" . | nindent 4 }}
  {{- with .Values.commonAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  replicas: {{ .Values.replicaCount }}
  strategy:
    type: Recreate
  selector:
    matchLabels:
      {{- include "points-service.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      labels:
        {{- include "points-service.selectorLabels" . | nindent 8 }}
        {{- with .Values.commonLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- with .Values.commonAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    spec:
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
      - name: {{ .Chart.Name }}
        image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
        imagePullPolicy: {{ .Values.image.pullPolicy }}
        securityContext:
          runAsUser: 0    # Run as root
          runAsGroup: 0
        ports:
        - containerPort: {{ .Values.service.port }}
          name: http
        envFrom:
        - configMapRef:
            name: {{ include "points-service.fullname" . }}-config
        {{- if .Values.resources }}
        resources:
          {{- toYaml .Values.resources | nindent 10 }}
        {{- end }}
        volumeMounts:
        - name: points-data
          mountPath: {{ .Values.persistence.mountPath | default "/app/data" }}
        livenessProbe:
          httpGet:
            path: {{ .Values.probes.liveness.path }}
            port: {{ .Values.probes.liveness.port }}
          initialDelaySeconds: {{ .Values.probes.liveness.initialDelaySeconds }}
          periodSeconds: {{ .Values.probes.liveness.periodSeconds }}
        readinessProbe:
          httpGet:
            path: {{ .Values.probes.readiness.path }}
            port: {{ .Values.probes.readiness.port }}
          initialDelaySeconds: {{ .Values.probes.readiness.initialDelaySeconds }}
          periodSeconds: {{ .Values.probes.readiness.periodSeconds }}
      volumes:
      - name: points-data
        {{- if .Values.persistence.enabled }}
        persistentVolumeClaim:
          claimName: {{ include "points-service.fullname" . }}-pvc
        {{- else }}
        emptyDir: {}
        {{- end }}

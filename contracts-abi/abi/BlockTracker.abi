[{"type": "constructor", "inputs": [], "stateMutability": "nonpayable"}, {"type": "fallback", "stateMutability": "payable"}, {"type": "receive", "stateMutability": "payable"}, {"type": "function", "name": "UPGRADE_INTERFACE_VERSION", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "acceptOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "addBuilderAddress", "inputs": [{"name": "builderName", "type": "string", "internalType": "string"}, {"name": "builder<PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "blockBuilderNameToAddress", "inputs": [{"name": "", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "blockWinners", "inputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "getBlockWinner", "inputs": [{"name": "blockNumber", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "getBuilder", "inputs": [{"name": "builderNameGraffiti", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "initialize", "inputs": [{"name": "oracleAccount_", "type": "address", "internalType": "address"}, {"name": "owner_", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "oracleAccount", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "pause", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "paused", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "pending<PERSON><PERSON>er", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "providerRegistry", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IProviderRegistry"}], "stateMutability": "view"}, {"type": "function", "name": "proxiableUUID", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "recordL1Block", "inputs": [{"name": "_blockNumber", "type": "uint256", "internalType": "uint256"}, {"name": "_winnerBLSKey", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>unt", "inputs": [{"name": "newO<PERSON>leAccount", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setProviderRegistry", "inputs": [{"name": "newProviderRegistry", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "unpause", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "upgradeToAndCall", "inputs": [{"name": "newImplementation", "type": "address", "internalType": "address"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "payable"}, {"type": "event", "name": "BuilderAddressAdded", "inputs": [{"name": "builderName", "type": "string", "indexed": true, "internalType": "string"}, {"name": "builder<PERSON><PERSON><PERSON>", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Initialized", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "NewL1Block", "inputs": [{"name": "blockNumber", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "winner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "OracleAccountSet", "inputs": [{"name": "old<PERSON><PERSON>leAccount", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newO<PERSON>leAccount", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferStarted", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Paused", "inputs": [{"name": "account", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "ProviderRegistrySet", "inputs": [{"name": "oldProviderRegistry", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newProviderRegistry", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Unpaused", "inputs": [{"name": "account", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Upgraded", "inputs": [{"name": "implementation", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "AddressEmptyCode", "inputs": [{"name": "target", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "BlockNumberIsZero", "inputs": []}, {"type": "error", "name": "ERC1967InvalidImplementation", "inputs": [{"name": "implementation", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC1967Non<PERSON>ayable", "inputs": []}, {"type": "error", "name": "EnforcedPause", "inputs": []}, {"type": "error", "name": "ExpectedPause", "inputs": []}, {"type": "error", "name": "FailedInnerCall", "inputs": []}, {"type": "error", "name": "InvalidFallback", "inputs": []}, {"type": "error", "name": "InvalidInitialization", "inputs": []}, {"type": "error", "name": "InvalidReceive", "inputs": []}, {"type": "error", "name": "NotInitializing", "inputs": []}, {"type": "error", "name": "NotO<PERSON>leAccount", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}, {"name": "oracleAccount", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableInvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "UUPSUnauthorizedCallContext", "inputs": []}, {"type": "error", "name": "UUPSUnsupportedProxiableUUID", "inputs": [{"name": "slot", "type": "bytes32", "internalType": "bytes32"}]}]
[{"type": "constructor", "inputs": [], "stateMutability": "nonpayable"}, {"type": "fallback", "stateMutability": "payable"}, {"type": "receive", "stateMutability": "payable"}, {"type": "function", "name": "EIP712_BID_TYPEHASH", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "EIP712_COMMITMENT_TYPEHASH", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "HEXCHARS", "inputs": [], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "UPGRADE_INTERFACE_VERSION", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "acceptOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "bidderRegistry", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IBidderRegistry"}], "stateMutability": "view"}, {"type": "function", "name": "blockTracker", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IBlockTracker"}], "stateMutability": "view"}, {"type": "function", "name": "commitmentDispatchWindow", "inputs": [], "outputs": [{"name": "", "type": "uint64", "internalType": "uint64"}], "stateMutability": "view"}, {"type": "function", "name": "commitmentsCount", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "domainSeparatorBid", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "domainSeparatorPreconf", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "getBidHash", "inputs": [{"name": "params", "type": "tuple", "internalType": "struct IPreconfManager.OpenCommitmentParams", "components": [{"name": "unopenedCommitmentIndex", "type": "bytes32", "internalType": "bytes32"}, {"name": "bidAmt", "type": "uint256", "internalType": "uint256"}, {"name": "slashAmt", "type": "uint256", "internalType": "uint256"}, {"name": "blockNumber", "type": "uint64", "internalType": "uint64"}, {"name": "decayStartTimeStamp", "type": "uint64", "internalType": "uint64"}, {"name": "decayEndTimeStamp", "type": "uint64", "internalType": "uint64"}, {"name": "txnHash", "type": "string", "internalType": "string"}, {"name": "revertingTxHashes", "type": "string", "internalType": "string"}, {"name": "bidSignature", "type": "bytes", "internalType": "bytes"}, {"name": "zkProof", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "bidOptions", "type": "bytes", "internalType": "bytes"}]}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "getCommitment", "inputs": [{"name": "commitmentIndex", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct IPreconfManager.OpenedCommitment", "components": [{"name": "bidder", "type": "address", "internalType": "address"}, {"name": "isSettled", "type": "bool", "internalType": "bool"}, {"name": "blockNumber", "type": "uint64", "internalType": "uint64"}, {"name": "decayStartTimeStamp", "type": "uint64", "internalType": "uint64"}, {"name": "decayEndTimeStamp", "type": "uint64", "internalType": "uint64"}, {"name": "dispatchTimestamp", "type": "uint64", "internalType": "uint64"}, {"name": "committer", "type": "address", "internalType": "address"}, {"name": "bidAmt", "type": "uint256", "internalType": "uint256"}, {"name": "slashAmt", "type": "uint256", "internalType": "uint256"}, {"name": "commitmentDigest", "type": "bytes32", "internalType": "bytes32"}, {"name": "commitmentSignature", "type": "bytes", "internalType": "bytes"}, {"name": "txnHash", "type": "string", "internalType": "string"}, {"name": "revertingTxHashes", "type": "string", "internalType": "string"}, {"name": "bidOptions", "type": "bytes", "internalType": "bytes"}]}], "stateMutability": "view"}, {"type": "function", "name": "getOpenedCommitmentIndex", "inputs": [{"name": "commitment", "type": "tuple", "internalType": "struct IPreconfManager.OpenedCommitment", "components": [{"name": "bidder", "type": "address", "internalType": "address"}, {"name": "isSettled", "type": "bool", "internalType": "bool"}, {"name": "blockNumber", "type": "uint64", "internalType": "uint64"}, {"name": "decayStartTimeStamp", "type": "uint64", "internalType": "uint64"}, {"name": "decayEndTimeStamp", "type": "uint64", "internalType": "uint64"}, {"name": "dispatchTimestamp", "type": "uint64", "internalType": "uint64"}, {"name": "committer", "type": "address", "internalType": "address"}, {"name": "bidAmt", "type": "uint256", "internalType": "uint256"}, {"name": "slashAmt", "type": "uint256", "internalType": "uint256"}, {"name": "commitmentDigest", "type": "bytes32", "internalType": "bytes32"}, {"name": "commitmentSignature", "type": "bytes", "internalType": "bytes"}, {"name": "txnHash", "type": "string", "internalType": "string"}, {"name": "revertingTxHashes", "type": "string", "internalType": "string"}, {"name": "bidOptions", "type": "bytes", "internalType": "bytes"}]}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"type": "function", "name": "getPreConfHash", "inputs": [{"name": "_bidHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "_bidSignature", "type": "bytes", "internalType": "bytes"}, {"name": "_zkProof", "type": "uint256[]", "internalType": "uint256[]"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "getTxnHashFromCommitment", "inputs": [{"name": "commitmentIndex", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "txnHash", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "getUnopenedCommitment", "inputs": [{"name": "commitmentIndex", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct IPreconfManager.UnopenedCommitment", "components": [{"name": "isOpened", "type": "bool", "internalType": "bool"}, {"name": "committer", "type": "address", "internalType": "address"}, {"name": "dispatchTimestamp", "type": "uint64", "internalType": "uint64"}, {"name": "commitmentDigest", "type": "bytes32", "internalType": "bytes32"}, {"name": "commitmentSignature", "type": "bytes", "internalType": "bytes"}]}], "stateMutability": "view"}, {"type": "function", "name": "getUnopenedCommitmentIndex", "inputs": [{"name": "commitment", "type": "tuple", "internalType": "struct IPreconfManager.UnopenedCommitment", "components": [{"name": "isOpened", "type": "bool", "internalType": "bool"}, {"name": "committer", "type": "address", "internalType": "address"}, {"name": "dispatchTimestamp", "type": "uint64", "internalType": "uint64"}, {"name": "commitmentDigest", "type": "bytes32", "internalType": "bytes32"}, {"name": "commitmentSignature", "type": "bytes", "internalType": "bytes"}]}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"type": "function", "name": "initialize", "inputs": [{"name": "_providerRegistry", "type": "address", "internalType": "address"}, {"name": "_bidderRegistry", "type": "address", "internalType": "address"}, {"name": "_oracleContract", "type": "address", "internalType": "address"}, {"name": "_owner", "type": "address", "internalType": "address"}, {"name": "_blockTracker", "type": "address", "internalType": "address"}, {"name": "_commitmentDispatchWindow", "type": "uint64", "internalType": "uint64"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "initiate<PERSON>eward", "inputs": [{"name": "commitmentIndex", "type": "bytes32", "internalType": "bytes32"}, {"name": "residualBidPercentAfterDecay", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "initiateSlash", "inputs": [{"name": "commitmentIndex", "type": "bytes32", "internalType": "bytes32"}, {"name": "residualBidPercentAfterDecay", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "openCommitment", "inputs": [{"name": "params", "type": "tuple", "internalType": "struct IPreconfManager.OpenCommitmentParams", "components": [{"name": "unopenedCommitmentIndex", "type": "bytes32", "internalType": "bytes32"}, {"name": "bidAmt", "type": "uint256", "internalType": "uint256"}, {"name": "slashAmt", "type": "uint256", "internalType": "uint256"}, {"name": "blockNumber", "type": "uint64", "internalType": "uint64"}, {"name": "decayStartTimeStamp", "type": "uint64", "internalType": "uint64"}, {"name": "decayEndTimeStamp", "type": "uint64", "internalType": "uint64"}, {"name": "txnHash", "type": "string", "internalType": "string"}, {"name": "revertingTxHashes", "type": "string", "internalType": "string"}, {"name": "bidSignature", "type": "bytes", "internalType": "bytes"}, {"name": "zkProof", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "bidOptions", "type": "bytes", "internalType": "bytes"}]}], "outputs": [{"name": "commitmentIndex", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "openedCommitments", "inputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "bidder", "type": "address", "internalType": "address"}, {"name": "isSettled", "type": "bool", "internalType": "bool"}, {"name": "blockNumber", "type": "uint64", "internalType": "uint64"}, {"name": "decayStartTimeStamp", "type": "uint64", "internalType": "uint64"}, {"name": "decayEndTimeStamp", "type": "uint64", "internalType": "uint64"}, {"name": "dispatchTimestamp", "type": "uint64", "internalType": "uint64"}, {"name": "committer", "type": "address", "internalType": "address"}, {"name": "bidAmt", "type": "uint256", "internalType": "uint256"}, {"name": "slashAmt", "type": "uint256", "internalType": "uint256"}, {"name": "commitmentDigest", "type": "bytes32", "internalType": "bytes32"}, {"name": "commitmentSignature", "type": "bytes", "internalType": "bytes"}, {"name": "txnHash", "type": "string", "internalType": "string"}, {"name": "revertingTxHashes", "type": "string", "internalType": "string"}, {"name": "bidOptions", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "oracleContract", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "pause", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "paused", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "pending<PERSON><PERSON>er", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "processedTxnHashes", "inputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "providerRegistry", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IProviderRegistry"}], "stateMutability": "view"}, {"type": "function", "name": "proxiableUUID", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "storeUnopenedCommitment", "inputs": [{"name": "commitmentDigest", "type": "bytes32", "internalType": "bytes32"}, {"name": "commitmentSignature", "type": "bytes", "internalType": "bytes"}, {"name": "dispatchTimestamp", "type": "uint64", "internalType": "uint64"}], "outputs": [{"name": "commitmentIndex", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "unopenedCommitments", "inputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "isOpened", "type": "bool", "internalType": "bool"}, {"name": "committer", "type": "address", "internalType": "address"}, {"name": "dispatchTimestamp", "type": "uint64", "internalType": "uint64"}, {"name": "commitmentDigest", "type": "bytes32", "internalType": "bytes32"}, {"name": "commitmentSignature", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "unpause", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "updateBidderRegistry", "inputs": [{"name": "newBidderRegistry", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "updateBlockTracker", "inputs": [{"name": "newBlockTracker", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "updateCommitmentDispatchWindow", "inputs": [{"name": "newDispatchWindow", "type": "uint64", "internalType": "uint64"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "updateOracleContract", "inputs": [{"name": "newOracleContract", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "updateProviderRegistry", "inputs": [{"name": "newProviderRegistry", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "upgradeToAndCall", "inputs": [{"name": "newImplementation", "type": "address", "internalType": "address"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "verifyBid", "inputs": [{"name": "params", "type": "tuple", "internalType": "struct IPreconfManager.OpenCommitmentParams", "components": [{"name": "unopenedCommitmentIndex", "type": "bytes32", "internalType": "bytes32"}, {"name": "bidAmt", "type": "uint256", "internalType": "uint256"}, {"name": "slashAmt", "type": "uint256", "internalType": "uint256"}, {"name": "blockNumber", "type": "uint64", "internalType": "uint64"}, {"name": "decayStartTimeStamp", "type": "uint64", "internalType": "uint64"}, {"name": "decayEndTimeStamp", "type": "uint64", "internalType": "uint64"}, {"name": "txnHash", "type": "string", "internalType": "string"}, {"name": "revertingTxHashes", "type": "string", "internalType": "string"}, {"name": "bidSignature", "type": "bytes", "internalType": "bytes"}, {"name": "zkProof", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "bidOptions", "type": "bytes", "internalType": "bytes"}]}], "outputs": [{"name": "messageDigest", "type": "bytes32", "internalType": "bytes32"}, {"name": "recovered<PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "verifyPreConfCommitment", "inputs": [{"name": "params", "type": "tuple", "internalType": "struct IPreconfManager.CommitmentParams", "components": [{"name": "txnHash", "type": "string", "internalType": "string"}, {"name": "revertingTxHashes", "type": "string", "internalType": "string"}, {"name": "bidAmt", "type": "uint256", "internalType": "uint256"}, {"name": "slashAmt", "type": "uint256", "internalType": "uint256"}, {"name": "blockNumber", "type": "uint64", "internalType": "uint64"}, {"name": "decayStartTimeStamp", "type": "uint64", "internalType": "uint64"}, {"name": "decayEndTimeStamp", "type": "uint64", "internalType": "uint64"}, {"name": "bidHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "bidSignature", "type": "bytes", "internalType": "bytes"}, {"name": "commitmentSignature", "type": "bytes", "internalType": "bytes"}, {"name": "zkProof", "type": "uint256[]", "internalType": "uint256[]"}]}], "outputs": [{"name": "preConfHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "committer<PERSON><PERSON>ress", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "zkContextHash", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "event", "name": "BidderRegistryUpdated", "inputs": [{"name": "newBidderRegistry", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "BlockTrackerUpdated", "inputs": [{"name": "newBlockTracker", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "CommitmentDispatchWindowUpdated", "inputs": [{"name": "newDispatchWindow", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "Initialized", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "OpenedCommitmentStored", "inputs": [{"name": "commitmentIndex", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "bidder", "type": "address", "indexed": false, "internalType": "address"}, {"name": "committer", "type": "address", "indexed": false, "internalType": "address"}, {"name": "bidAmt", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "slashAmt", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "blockNumber", "type": "uint64", "indexed": false, "internalType": "uint64"}, {"name": "decayStartTimeStamp", "type": "uint64", "indexed": false, "internalType": "uint64"}, {"name": "decayEndTimeStamp", "type": "uint64", "indexed": false, "internalType": "uint64"}, {"name": "txnHash", "type": "string", "indexed": false, "internalType": "string"}, {"name": "revertingTxHashes", "type": "string", "indexed": false, "internalType": "string"}, {"name": "commitmentDigest", "type": "bytes32", "indexed": false, "internalType": "bytes32"}, {"name": "dispatchTimestamp", "type": "uint64", "indexed": false, "internalType": "uint64"}, {"name": "bidOptions", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "OracleContractUpdated", "inputs": [{"name": "newOracleContract", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferStarted", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Paused", "inputs": [{"name": "account", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "ProviderRegistryUpdated", "inputs": [{"name": "newProviderRegistry", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "SignatureVerified", "inputs": [{"name": "signer", "type": "address", "indexed": true, "internalType": "address"}, {"name": "txnHash", "type": "string", "indexed": false, "internalType": "string"}, {"name": "revertingTxHashes", "type": "string", "indexed": false, "internalType": "string"}, {"name": "bidAmt", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "blockNumber", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "UnopenedCommitmentStored", "inputs": [{"name": "commitmentIndex", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "committer", "type": "address", "indexed": false, "internalType": "address"}, {"name": "commitmentDigest", "type": "bytes32", "indexed": false, "internalType": "bytes32"}, {"name": "commitmentSignature", "type": "bytes", "indexed": false, "internalType": "bytes"}, {"name": "dispatchTimestamp", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "Unpaused", "inputs": [{"name": "account", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Upgraded", "inputs": [{"name": "implementation", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "AddressEmptyCode", "inputs": [{"name": "target", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "BN128AddFailed", "inputs": []}, {"type": "error", "name": "BN128MulFailed", "inputs": []}, {"type": "error", "name": "CommitmentAlreadyOpened", "inputs": [{"name": "commitmentIndex", "type": "bytes32", "internalType": "bytes32"}]}, {"type": "error", "name": "CommitmentAlreadySettled", "inputs": [{"name": "commitmentIndex", "type": "bytes32", "internalType": "bytes32"}]}, {"type": "error", "name": "ERC1967InvalidImplementation", "inputs": [{"name": "implementation", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC1967Non<PERSON>ayable", "inputs": []}, {"type": "error", "name": "EnforcedPause", "inputs": []}, {"type": "error", "name": "ExpectedPause", "inputs": []}, {"type": "error", "name": "FailedInnerCall", "inputs": []}, {"type": "error", "name": "InvalidCommitmentDigest", "inputs": [{"name": "commitmentDigest", "type": "bytes32", "internalType": "bytes32"}, {"name": "computedDigest", "type": "bytes32", "internalType": "bytes32"}]}, {"type": "error", "name": "InvalidDecayTime", "inputs": [{"name": "startTime", "type": "uint64", "internalType": "uint64"}, {"name": "endTime", "type": "uint64", "internalType": "uint64"}]}, {"type": "error", "name": "InvalidDispatchTimestamp", "inputs": [{"name": "minTime", "type": "uint256", "internalType": "uint256"}, {"name": "dispatchTimestamp", "type": "uint64", "internalType": "uint64"}]}, {"type": "error", "name": "InvalidFallback", "inputs": []}, {"type": "error", "name": "InvalidInitialization", "inputs": []}, {"type": "error", "name": "InvalidReceive", "inputs": []}, {"type": "error", "name": "NotInitializing", "inputs": []}, {"type": "error", "name": "OwnableInvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ProviderZKProofInvalid", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}, {"name": "commitmentDigest", "type": "bytes32", "internalType": "bytes32"}]}, {"type": "error", "name": "SenderIsNotCommitter", "inputs": [{"name": "expected", "type": "address", "internalType": "address"}, {"name": "actual", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "SenderIsNotOracleContract", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}, {"name": "oracleContract", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "TxnHashAlreadyProcessed", "inputs": [{"name": "txnHash", "type": "string", "internalType": "string"}, {"name": "bidder<PERSON>ddress", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "UUPSUnauthorizedCallContext", "inputs": []}, {"type": "error", "name": "UUPSUnsupportedProxiableUUID", "inputs": [{"name": "slot", "type": "bytes32", "internalType": "bytes32"}]}, {"type": "error", "name": "UnauthorizedOpenCommitment", "inputs": [{"name": "committer", "type": "address", "internalType": "address"}, {"name": "bidder", "type": "address", "internalType": "address"}, {"name": "sender", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "UnopenedCommitmentAlreadyExists", "inputs": [{"name": "commitmentIndex", "type": "bytes32", "internalType": "bytes32"}]}, {"type": "error", "name": "WinnerIsNotCommitter", "inputs": [{"name": "committer", "type": "address", "internalType": "address"}, {"name": "winner", "type": "address", "internalType": "address"}]}]
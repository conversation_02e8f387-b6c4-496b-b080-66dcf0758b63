[{"type": "constructor", "inputs": [{"name": "_bidderRegistry", "type": "address", "internalType": "address"}, {"name": "_minBalance", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "fallback", "stateMutability": "payable"}, {"type": "receive", "stateMutability": "payable"}, {"type": "function", "name": "BIDDER_REGISTRY", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "MIN_BALANCE", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "setTargetDeposit", "inputs": [{"name": "provider", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setTargetDeposits", "inputs": [{"name": "providers", "type": "address[]", "internalType": "address[]"}, {"name": "amounts", "type": "uint256[]", "internalType": "uint256[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "targetDeposits", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "topUpDeposit", "inputs": [{"name": "provider", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "topUpDeposits", "inputs": [{"name": "providers", "type": "address[]", "internalType": "address[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "CurrentBalanceAtOrBelowMin", "inputs": [{"name": "provider", "type": "address", "indexed": true, "internalType": "address"}, {"name": "currentBalance", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "minBalance", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "CurrentDepositIsSufficient", "inputs": [{"name": "provider", "type": "address", "indexed": true, "internalType": "address"}, {"name": "currentDeposit", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "targetDeposit", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "DepositToppedUp", "inputs": [{"name": "provider", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "TargetDepositDoesNotExist", "inputs": [{"name": "provider", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "TargetDepositSet", "inputs": [{"name": "provider", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "TopUpReduced", "inputs": [{"name": "provider", "type": "address", "indexed": true, "internalType": "address"}, {"name": "available", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "needed", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "WithdrawalRequestExists", "inputs": [{"name": "provider", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "InvalidFallback", "inputs": []}, {"type": "error", "name": "NotThisEOA", "inputs": [{"name": "msgSender", "type": "address", "internalType": "address"}, {"name": "this<PERSON>dd<PERSON>", "type": "address", "internalType": "address"}]}]
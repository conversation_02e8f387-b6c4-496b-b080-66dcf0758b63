[{"type": "constructor", "inputs": [], "stateMutability": "nonpayable"}, {"type": "fallback", "stateMutability": "payable"}, {"type": "receive", "stateMutability": "payable"}, {"type": "function", "name": "UPGRADE_INTERFACE_VERSION", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "acceptOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "initialize", "inputs": [{"name": "initialOwner", "type": "address", "internalType": "address"}, {"name": "rewardsPctBps", "type": "uint256", "internalType": "uint256"}, {"name": "treasury", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "payProposer", "inputs": [{"name": "feeRecipient", "type": "address", "internalType": "address payable"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "pending<PERSON><PERSON>er", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "proxiableUUID", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "rewardsPctBps", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "setRewardsPctBps", "inputs": [{"name": "rewardsPctBps", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setTreasury", "inputs": [{"name": "treasury", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "toTreasury", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "treasury", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address payable"}], "stateMutability": "view"}, {"type": "function", "name": "upgradeToAndCall", "inputs": [{"name": "newImplementation", "type": "address", "internalType": "address"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "withdrawToTreasury", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "Initialized", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferStarted", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "ProposerPaid", "inputs": [{"name": "feeRecipient", "type": "address", "indexed": true, "internalType": "address"}, {"name": "proposerAmt", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "rewardAmt", "type": "uint256", "indexed": true, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "RewardsPctBpsSet", "inputs": [{"name": "rewardsPctBps", "type": "uint256", "indexed": true, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "TreasurySet", "inputs": [{"name": "treasury", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "TreasuryWithdrawn", "inputs": [{"name": "treasuryAmt", "type": "uint256", "indexed": true, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Upgraded", "inputs": [{"name": "implementation", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "AddressEmptyCode", "inputs": [{"name": "target", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC1967InvalidImplementation", "inputs": [{"name": "implementation", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC1967Non<PERSON>ayable", "inputs": []}, {"type": "error", "name": "FailedInnerCall", "inputs": []}, {"type": "error", "name": "InvalidFallback", "inputs": []}, {"type": "error", "name": "InvalidInitialization", "inputs": []}, {"type": "error", "name": "InvalidReceive", "inputs": []}, {"type": "error", "name": "NoFundsToWithdraw", "inputs": []}, {"type": "error", "name": "NotInitializing", "inputs": []}, {"type": "error", "name": "OnlyOwnerOrTreasury", "inputs": []}, {"type": "error", "name": "OwnableInvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ProposerTransferFailed", "inputs": [{"name": "feeRecipient", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ReentrancyGuardReentrantCall", "inputs": []}, {"type": "error", "name": "RewardsPctTooHigh", "inputs": []}, {"type": "error", "name": "TreasuryIsZero", "inputs": []}, {"type": "error", "name": "TreasuryTransferFailed", "inputs": [{"name": "treasury", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "UUPSUnauthorizedCallContext", "inputs": []}, {"type": "error", "name": "UUPSUnsupportedProxiableUUID", "inputs": [{"name": "slot", "type": "bytes32", "internalType": "bytes32"}]}]
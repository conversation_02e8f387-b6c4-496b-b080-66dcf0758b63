[{"type": "constructor", "inputs": [], "stateMutability": "nonpayable"}, {"type": "fallback", "stateMutability": "payable"}, {"type": "receive", "stateMutability": "payable"}, {"type": "function", "name": "ONE_HUNDRED_PERCENT", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "PRECISION", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "UPGRADE_INTERFACE_VERSION", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "acceptOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "addVerifiedBLSKey", "inputs": [{"name": "blsPublicKey", "type": "bytes", "internalType": "bytes"}, {"name": "signature", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "areProvidersValid", "inputs": [{"name": "providers", "type": "address[]", "internalType": "address[]"}], "outputs": [{"name": "", "type": "bool[]", "internalType": "bool[]"}], "stateMutability": "view"}, {"type": "function", "name": "bidderSlashedAmount", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "blockBuilderBLSKeyToAddress", "inputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "delegateRegisterAndStake", "inputs": [{"name": "provider", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "delegateStake", "inputs": [{"name": "provider", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "eoaToBlsPubkeys", "inputs": [{"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "feePercent", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getAccumulatedPenaltyFee", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getBLSKeys", "inputs": [{"name": "provider", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bytes[]", "internalType": "bytes[]"}], "stateMutability": "view"}, {"type": "function", "name": "getEoaFromBLSKey", "inputs": [{"name": "bls<PERSON>ey", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "getProviderStake", "inputs": [{"name": "provider", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "initialize", "inputs": [{"name": "_minStake", "type": "uint256", "internalType": "uint256"}, {"name": "_penaltyFee<PERSON><PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}, {"name": "_feePercent", "type": "uint256", "internalType": "uint256"}, {"name": "_owner", "type": "address", "internalType": "address"}, {"name": "_withdrawalDelay", "type": "uint256", "internalType": "uint256"}, {"name": "_penaltyFeePayoutPeriod", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "isProviderValid", "inputs": [{"name": "provider", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "view"}, {"type": "function", "name": "manuallyWithdrawPenaltyFee", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "minStake", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "overrideAddBLSKey", "inputs": [{"name": "provider", "type": "address", "internalType": "address"}, {"name": "blsPublicKey", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "pause", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "paused", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "penaltyFeeTracker", "inputs": [], "outputs": [{"name": "recipient", "type": "address", "internalType": "address"}, {"name": "accumulatedAmount", "type": "uint256", "internalType": "uint256"}, {"name": "lastPayoutTimestamp", "type": "uint256", "internalType": "uint256"}, {"name": "payoutTimePeriod", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "pending<PERSON><PERSON>er", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "preconfManager", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "providerRegistered", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "providerStakes", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "proxiableUUID", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "registerAndStake", "inputs": [], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setFeePayoutPeriod", "inputs": [{"name": "_feePayoutPeriod", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setMinStake", "inputs": [{"name": "_minStake", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setNewFeePercent", "inputs": [{"name": "new<PERSON>eeP<PERSON><PERSON>", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setNewPenaltyFeeRecipient", "inputs": [{"name": "newFeeRecipient", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setPreconfManager", "inputs": [{"name": "contractAddress", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "_withdrawalDelay", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "slash", "inputs": [{"name": "amt", "type": "uint256", "internalType": "uint256"}, {"name": "slashAmt", "type": "uint256", "internalType": "uint256"}, {"name": "provider", "type": "address", "internalType": "address"}, {"name": "bidder", "type": "address", "internalType": "address payable"}, {"name": "residualBidPercentAfterDecay", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "stake", "inputs": [], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "unpause", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "unstake", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "upgradeToAndCall", "inputs": [{"name": "newImplementation", "type": "address", "internalType": "address"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "verifySignature", "inputs": [{"name": "pubKey", "type": "bytes", "internalType": "bytes"}, {"name": "message", "type": "bytes32", "internalType": "bytes32"}, {"name": "signature", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "withdraw", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "withdrawSlashedAmount", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "withdrawalDelay", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "withdrawalRequests", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "event", "name": "BLSKeyAdded", "inputs": [{"name": "provider", "type": "address", "indexed": true, "internalType": "address"}, {"name": "blsPublicKey", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "BidderWithdrawSlashedAmount", "inputs": [{"name": "bidder", "type": "address", "indexed": false, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "FeePayoutPeriodUpdated", "inputs": [{"name": "newFeePayoutPeriod", "type": "uint256", "indexed": true, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "FeePercentUpdated", "inputs": [{"name": "new<PERSON>eeP<PERSON><PERSON>", "type": "uint256", "indexed": true, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "FeeTransfer", "inputs": [{"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "recipient", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "FundsDeposited", "inputs": [{"name": "provider", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "FundsSlashed", "inputs": [{"name": "provider", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Initialized", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "InsufficientFundsToSlash", "inputs": [{"name": "provider", "type": "address", "indexed": true, "internalType": "address"}, {"name": "providerStake", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "residualAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "penaltyFee", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "slashAmt", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "MinStakeUpdated", "inputs": [{"name": "newMinStake", "type": "uint256", "indexed": true, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferStarted", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Paused", "inputs": [{"name": "account", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "PenaltyFeeRecipientUpdated", "inputs": [{"name": "newPenaltyFeeRecipient", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "PreconfManagerUpdated", "inputs": [{"name": "newPreconfManager", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "ProviderRegistered", "inputs": [{"name": "provider", "type": "address", "indexed": true, "internalType": "address"}, {"name": "stakedAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "TransferToBidderFailed", "inputs": [{"name": "bidder", "type": "address", "indexed": false, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Unpaused", "inputs": [{"name": "account", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Unstake", "inputs": [{"name": "provider", "type": "address", "indexed": true, "internalType": "address"}, {"name": "timestamp", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Upgraded", "inputs": [{"name": "implementation", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Withdraw", "inputs": [{"name": "provider", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "WithdrawalDelayUpdated", "inputs": [{"name": "newWithdrawalDelay", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "error", "name": "AddressEmptyCode", "inputs": [{"name": "target", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "AtLeastOneBLSKeyRequired", "inputs": []}, {"type": "error", "name": "BLSSignatureInvalid", "inputs": []}, {"type": "error", "name": "BidderAmountIsZero", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "BidderWithdrawalTransferFailed", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "DelayNotPassed", "inputs": [{"name": "withdrawalRequestTimestamp", "type": "uint256", "internalType": "uint256"}, {"name": "withdrawalDelay", "type": "uint256", "internalType": "uint256"}, {"name": "currentBlockTimestamp", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC1967InvalidImplementation", "inputs": [{"name": "implementation", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC1967Non<PERSON>ayable", "inputs": []}, {"type": "error", "name": "EnforcedPause", "inputs": []}, {"type": "error", "name": "ExpectedPause", "inputs": []}, {"type": "error", "name": "FailedInnerCall", "inputs": []}, {"type": "error", "name": "FeeRecipientIsZero", "inputs": []}, {"type": "error", "name": "InsufficientStake", "inputs": [{"name": "stake", "type": "uint256", "internalType": "uint256"}, {"name": "minStake", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "InvalidBLSPublicKeyLength", "inputs": [{"name": "length", "type": "uint256", "internalType": "uint256"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "InvalidFallback", "inputs": []}, {"type": "error", "name": "InvalidInitialization", "inputs": []}, {"type": "error", "name": "InvalidReceive", "inputs": []}, {"type": "error", "name": "NoStakeToWithdraw", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "NoUnstakeRequest", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "NotInitializing", "inputs": []}, {"type": "error", "name": "NotPreconfContract", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}, {"name": "preconfManager", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableInvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "PayoutPeriodMustBePositive", "inputs": []}, {"type": "error", "name": "PendingWithdrawalRequest", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "PreconfManagerNotSet", "inputs": []}, {"type": "error", "name": "ProviderAlreadyRegistered", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ProviderCommitmentsPending", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}, {"name": "numPending", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ProviderNotRegistered", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "PublicKeyLengthInvalid", "inputs": [{"name": "exp", "type": "uint256", "internalType": "uint256"}, {"name": "got", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ReentrancyGuardReentrantCall", "inputs": []}, {"type": "error", "name": "SignatureLengthInvalid", "inputs": [{"name": "exp", "type": "uint256", "internalType": "uint256"}, {"name": "got", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "StakeTransferFailed", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "TransferToRecipientFailed", "inputs": []}, {"type": "error", "name": "UUPSUnauthorizedCallContext", "inputs": []}, {"type": "error", "name": "UUPSUnsupportedProxiableUUID", "inputs": [{"name": "slot", "type": "bytes32", "internalType": "bytes32"}]}, {"type": "error", "name": "UnstakeRequestExists", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}]}]
[{"type": "constructor", "inputs": [], "stateMutability": "nonpayable"}, {"type": "fallback", "stateMutability": "payable"}, {"type": "receive", "stateMutability": "payable"}, {"type": "function", "name": "ONE_HUNDRED_PERCENT", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "PRECISION", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "UPGRADE_INTERFACE_VERSION", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "acceptOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "bidPayment", "inputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "bidder", "type": "address", "internalType": "address"}, {"name": "bidAmt", "type": "uint256", "internalType": "uint256"}, {"name": "state", "type": "uint8", "internalType": "enum IBidderRegistry.State"}], "stateMutability": "view"}, {"type": "function", "name": "bidderWithdrawalPeriodMs", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "blockTrackerContract", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IBlockTracker"}], "stateMutability": "view"}, {"type": "function", "name": "convertFundsToProviderReward", "inputs": [{"name": "commitmentDigest", "type": "bytes32", "internalType": "bytes32"}, {"name": "provider", "type": "address", "internalType": "address payable"}, {"name": "residualBidPercentAfterDecay", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "depositAsBidder", "inputs": [{"name": "provider", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "depositEvenlyAsBidder", "inputs": [{"name": "providers", "type": "address[]", "internalType": "address[]"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "depositManagerHash", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "depositManagerImpl", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "deposits", "inputs": [{"name": "bidder", "type": "address", "internalType": "address"}, {"name": "provider", "type": "address", "internalType": "address"}], "outputs": [{"name": "exists", "type": "bool", "internalType": "bool"}, {"name": "availableAmount", "type": "uint256", "internalType": "uint256"}, {"name": "escrowedAmount", "type": "uint256", "internalType": "uint256"}, {"name": "withdrawalRequestOccurrence", "type": "tuple", "internalType": "struct TimestampOccurrence.Occurrence", "components": [{"name": "exists", "type": "bool", "internalType": "bool"}, {"name": "timestamp", "type": "uint256", "internalType": "uint256"}]}], "stateMutability": "view"}, {"type": "function", "name": "feePercent", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getAccumulatedProtocolFee", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getDeposit", "inputs": [{"name": "bidder", "type": "address", "internalType": "address"}, {"name": "provider", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getDepositConsideringWithdrawalRequest", "inputs": [{"name": "bidder", "type": "address", "internalType": "address"}, {"name": "provider", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getEscrowedAmount", "inputs": [{"name": "bidder", "type": "address", "internalType": "address"}, {"name": "provider", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getProviderAmount", "inputs": [{"name": "provider", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "initialize", "inputs": [{"name": "_protocolFeeRecipient", "type": "address", "internalType": "address"}, {"name": "_feePercent", "type": "uint256", "internalType": "uint256"}, {"name": "_owner", "type": "address", "internalType": "address"}, {"name": "_blockTracker", "type": "address", "internalType": "address"}, {"name": "_feePayoutPeriod", "type": "uint256", "internalType": "uint256"}, {"name": "_bidderWithdrawalPeriodMs", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "manuallyWithdrawProtocolFee", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "openBid", "inputs": [{"name": "commitmentDigest", "type": "bytes32", "internalType": "bytes32"}, {"name": "bidAmt", "type": "uint256", "internalType": "uint256"}, {"name": "bidder", "type": "address", "internalType": "address"}, {"name": "provider", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "pause", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "paused", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "pending<PERSON><PERSON>er", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "preconfManager", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "protocolFeeTracker", "inputs": [], "outputs": [{"name": "recipient", "type": "address", "internalType": "address"}, {"name": "accumulatedAmount", "type": "uint256", "internalType": "uint256"}, {"name": "lastPayoutTimestamp", "type": "uint256", "internalType": "uint256"}, {"name": "payoutTimePeriod", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "providerAmount", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "proxiableUUID", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "requestWithdrawalsAsBidder", "inputs": [{"name": "providers", "type": "address[]", "internalType": "address[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setBlockTrackerContract", "inputs": [{"name": "newBlockTrackerContract", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setDepositManagerImpl", "inputs": [{"name": "_depositManagerImpl", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setNewFeePayoutPeriod", "inputs": [{"name": "newFeePayoutPeriod", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setNewFeePercent", "inputs": [{"name": "new<PERSON>eeP<PERSON><PERSON>", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setNewProtocolFeeRecipient", "inputs": [{"name": "newProtocolFeeRecipient", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setPreconfManager", "inputs": [{"name": "contractAddress", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "unlockFunds", "inputs": [{"name": "provider", "type": "address", "internalType": "address"}, {"name": "commitmentDigest", "type": "bytes32", "internalType": "bytes32"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "unpause", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "upgradeToAndCall", "inputs": [{"name": "newImplementation", "type": "address", "internalType": "address"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "withdrawAsBidder", "inputs": [{"name": "providers", "type": "address[]", "internalType": "address[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "withdrawProviderAmount", "inputs": [{"name": "provider", "type": "address", "internalType": "address payable"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "withdrawalRequestExists", "inputs": [{"name": "bidder", "type": "address", "internalType": "address"}, {"name": "provider", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "event", "name": "BidAmountReduced", "inputs": [{"name": "bidder", "type": "address", "indexed": true, "internalType": "address"}, {"name": "provider", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newBidAmt", "type": "uint256", "indexed": true, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "BidderDeposited", "inputs": [{"name": "bidder", "type": "address", "indexed": true, "internalType": "address"}, {"name": "provider", "type": "address", "indexed": true, "internalType": "address"}, {"name": "depositedAmount", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "newAvailableAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "bidder", "type": "address", "indexed": true, "internalType": "address"}, {"name": "provider", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amountWithdrawn", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "amountStillEscrowed", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "BlockTrackerUpdated", "inputs": [{"name": "newBlockTracker", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "DepositManagerImplUpdated", "inputs": [{"name": "newDepositManagerImpl", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "FeePayoutPeriodUpdated", "inputs": [{"name": "newFeePayoutPeriod", "type": "uint256", "indexed": true, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "FeePercentUpdated", "inputs": [{"name": "new<PERSON>eeP<PERSON><PERSON>", "type": "uint256", "indexed": true, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "FeeTransfer", "inputs": [{"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "recipient", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "FundsRewarded", "inputs": [{"name": "commitmentDigest", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "bidder", "type": "address", "indexed": true, "internalType": "address"}, {"name": "provider", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "FundsUnlocked", "inputs": [{"name": "commitmentDigest", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "bidder", "type": "address", "indexed": true, "internalType": "address"}, {"name": "provider", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Initialized", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferStarted", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Paused", "inputs": [{"name": "account", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "PreconfManagerUpdated", "inputs": [{"name": "newPreconfManager", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "ProtocolFeeRecipientUpdated", "inputs": [{"name": "newProtocolFeeRecipient", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "TopUpFailed", "inputs": [{"name": "bidder", "type": "address", "indexed": true, "internalType": "address"}, {"name": "provider", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "TransferToBidderFailed", "inputs": [{"name": "bidder", "type": "address", "indexed": false, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Unpaused", "inputs": [{"name": "account", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Upgraded", "inputs": [{"name": "implementation", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "WithdrawalRequested", "inputs": [{"name": "bidder", "type": "address", "indexed": true, "internalType": "address"}, {"name": "provider", "type": "address", "indexed": true, "internalType": "address"}, {"name": "availableAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "escrowedAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "timestamp", "type": "uint256", "indexed": true, "internalType": "uint256"}], "anonymous": false}, {"type": "error", "name": "AddressEmptyCode", "inputs": [{"name": "target", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "BidNotPreConfirmed", "inputs": [{"name": "commitmentDigest", "type": "bytes32", "internalType": "bytes32"}, {"name": "actualState", "type": "uint8", "internalType": "enum IBidderRegistry.State"}, {"name": "expectedState", "type": "uint8", "internalType": "enum IBidderRegistry.State"}]}, {"type": "error", "name": "BidderWithdrawalTransferFailed", "inputs": [{"name": "bidder", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "DepositAmountIsLessThanProviders", "inputs": [{"name": "depositAmount", "type": "uint256", "internalType": "uint256"}, {"name": "numProviders", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "DepositAmountIsZero", "inputs": []}, {"type": "error", "name": "DepositDoesNotExist", "inputs": [{"name": "bidder", "type": "address", "internalType": "address"}, {"name": "provider", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "DepositManagerNotSet", "inputs": []}, {"type": "error", "name": "ERC1967InvalidImplementation", "inputs": [{"name": "implementation", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC1967Non<PERSON>ayable", "inputs": []}, {"type": "error", "name": "EnforcedPause", "inputs": []}, {"type": "error", "name": "ExpectedPause", "inputs": []}, {"type": "error", "name": "FailedInnerCall", "inputs": []}, {"type": "error", "name": "FeeRecipientIsZero", "inputs": []}, {"type": "error", "name": "InvalidInitialization", "inputs": []}, {"type": "error", "name": "NoProviders", "inputs": []}, {"type": "error", "name": "NotInitializing", "inputs": []}, {"type": "error", "name": "OnlyBidderCanWithdraw", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}, {"name": "bidder", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableInvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "PayoutPeriodMustBePositive", "inputs": []}, {"type": "error", "name": "ProviderAmountIsZero", "inputs": [{"name": "provider", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ProviderIsZeroAddress", "inputs": []}, {"type": "error", "name": "ReentrancyGuardReentrantCall", "inputs": []}, {"type": "error", "name": "SenderIsNotPreconfManager", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}, {"name": "preconfManager", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "TransferToProviderFailed", "inputs": [{"name": "provider", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "TransferToRecipientFailed", "inputs": []}, {"type": "error", "name": "UUPSUnauthorizedCallContext", "inputs": []}, {"type": "error", "name": "UUPSUnsupportedProxiableUUID", "inputs": [{"name": "slot", "type": "bytes32", "internalType": "bytes32"}]}, {"type": "error", "name": "WithdrawalOccurrenceExists", "inputs": [{"name": "bidder", "type": "address", "internalType": "address"}, {"name": "provider", "type": "address", "internalType": "address"}, {"name": "requestTimestamp", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "WithdrawalPeriodNotElapsed", "inputs": [{"name": "currentTimestampMs", "type": "uint256", "internalType": "uint256"}, {"name": "withdrawalTimestampMs", "type": "uint256", "internalType": "uint256"}, {"name": "withdrawalPeriodMs", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "WithdrawalRequestAlreadyExists", "inputs": [{"name": "bidder", "type": "address", "internalType": "address"}, {"name": "provider", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "WithdrawalRequestDoesNotExist", "inputs": [{"name": "bidder", "type": "address", "internalType": "address"}, {"name": "provider", "type": "address", "internalType": "address"}]}]
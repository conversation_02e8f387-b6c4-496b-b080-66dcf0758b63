[{"type": "constructor", "inputs": [], "stateMutability": "nonpayable"}, {"type": "fallback", "stateMutability": "payable"}, {"type": "receive", "stateMutability": "payable"}, {"type": "function", "name": "UPGRADE_INTERFACE_VERSION", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "acceptOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "claimDelegate", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "recipient", "type": "address", "internalType": "address"}, {"name": "delegate", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "claimOnbehalfOfOperator", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "recipients", "type": "address[]", "internalType": "address[]"}, {"name": "tokenID", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "claimRewards", "inputs": [{"name": "recipients", "type": "address[]", "internalType": "address[]"}, {"name": "tokenID", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "getKeyRecipient", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "pubkey", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "getPendingRewards", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "recipient", "type": "address", "internalType": "address"}, {"name": "tokenID", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint128", "internalType": "uint128"}], "stateMutability": "view"}, {"type": "function", "name": "grantETHRewards", "inputs": [{"name": "rewardList", "type": "tuple[]", "internalType": "struct IRewardDistributor.Distribution[]", "components": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "recipient", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint128", "internalType": "uint128"}]}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "grantTokenRewards", "inputs": [{"name": "rewardList", "type": "tuple[]", "internalType": "struct IRewardDistributor.Distribution[]", "components": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "recipient", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint128", "internalType": "uint128"}]}, {"name": "tokenID", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "initialize", "inputs": [{"name": "_owner", "type": "address", "internalType": "address"}, {"name": "_rewardManager", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "migrateExistingRewards", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "tokenID", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "operatorGlobalOverride", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}], "outputs": [{"name": "recipient", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "operatorKeyOverrides", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "<PERSON><PERSON><PERSON>", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "recipient", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "overrideRecipientByPubkey", "inputs": [{"name": "pubkeys", "type": "bytes[]", "internalType": "bytes[]"}, {"name": "recipient", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "pause", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "paused", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "pending<PERSON><PERSON>er", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "proxiableUUID", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "reclaimStipendsToOwner", "inputs": [{"name": "operators", "type": "address[]", "internalType": "address[]"}, {"name": "recipients", "type": "address[]", "internalType": "address[]"}, {"name": "tokenID", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "rewardData", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "recipient", "type": "address", "internalType": "address"}, {"name": "tokenID", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "accrued", "type": "uint128", "internalType": "uint128"}, {"name": "claimed", "type": "uint128", "internalType": "uint128"}], "stateMutability": "view"}, {"type": "function", "name": "reward<PERSON>anager", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "rewardTokens", "inputs": [{"name": "id", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "token", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "setClaimDelegate", "inputs": [{"name": "delegate", "type": "address", "internalType": "address"}, {"name": "recipient", "type": "address", "internalType": "address"}, {"name": "status", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setOperatorGlobalOverride", "inputs": [{"name": "recipient", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setRewardManager", "inputs": [{"name": "_rewardManager", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setRewardToken", "inputs": [{"name": "_rewardToken", "type": "address", "internalType": "address"}, {"name": "_id", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "unpause", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "upgradeToAndCall", "inputs": [{"name": "newImplementation", "type": "address", "internalType": "address"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "payable"}, {"type": "event", "name": "ClaimDelegateSet", "inputs": [{"name": "operator", "type": "address", "indexed": true, "internalType": "address"}, {"name": "recipient", "type": "address", "indexed": true, "internalType": "address"}, {"name": "delegate", "type": "address", "indexed": true, "internalType": "address"}, {"name": "status", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "ETHGranted", "inputs": [{"name": "operator", "type": "address", "indexed": true, "internalType": "address"}, {"name": "recipient", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": true, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "ETHRewardsClaimed", "inputs": [{"name": "operator", "type": "address", "indexed": true, "internalType": "address"}, {"name": "recipient", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": true, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Initialized", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "OperatorGlobalOverrideSet", "inputs": [{"name": "operator", "type": "address", "indexed": true, "internalType": "address"}, {"name": "recipient", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferStarted", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Paused", "inputs": [{"name": "account", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "RecipientSet", "inputs": [{"name": "operator", "type": "address", "indexed": true, "internalType": "address"}, {"name": "pubkey", "type": "bytes", "indexed": false, "internalType": "bytes"}, {"name": "recipient", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "RewardManagerSet", "inputs": [{"name": "reward<PERSON>anager", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "RewardTokenSet", "inputs": [{"name": "rewardToken", "type": "address", "indexed": true, "internalType": "address"}, {"name": "tokenID", "type": "uint256", "indexed": true, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "RewardsBatchGranted", "inputs": [{"name": "tokenID", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "amount", "type": "uint256", "indexed": true, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "RewardsMigrated", "inputs": [{"name": "tokenID", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "operator", "type": "address", "indexed": true, "internalType": "address"}, {"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint128", "indexed": false, "internalType": "uint128"}], "anonymous": false}, {"type": "event", "name": "RewardsReclaimed", "inputs": [{"name": "tokenID", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "operator", "type": "address", "indexed": true, "internalType": "address"}, {"name": "recipient", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "TokenRewardsClaimed", "inputs": [{"name": "operator", "type": "address", "indexed": true, "internalType": "address"}, {"name": "recipient", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": true, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "TokensGranted", "inputs": [{"name": "operator", "type": "address", "indexed": true, "internalType": "address"}, {"name": "recipient", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": true, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Unpaused", "inputs": [{"name": "account", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Upgraded", "inputs": [{"name": "implementation", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "AddressEmptyCode", "inputs": [{"name": "target", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "AddressInsufficientBalance", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC1967InvalidImplementation", "inputs": [{"name": "implementation", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC1967Non<PERSON>ayable", "inputs": []}, {"type": "error", "name": "EnforcedPause", "inputs": []}, {"type": "error", "name": "ExpectedPause", "inputs": []}, {"type": "error", "name": "FailedInnerCall", "inputs": []}, {"type": "error", "name": "IncorrectPaymentAmount", "inputs": [{"name": "received", "type": "uint256", "internalType": "uint256"}, {"name": "expected", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "InvalidBLSPubKeyLength", "inputs": []}, {"type": "error", "name": "InvalidClaimDelegate", "inputs": []}, {"type": "error", "name": "InvalidFallback", "inputs": []}, {"type": "error", "name": "InvalidInitialization", "inputs": []}, {"type": "error", "name": "InvalidOperator", "inputs": []}, {"type": "error", "name": "InvalidReceive", "inputs": []}, {"type": "error", "name": "InvalidRecipient", "inputs": []}, {"type": "error", "name": "InvalidRewardToken", "inputs": []}, {"type": "error", "name": "InvalidTokenID", "inputs": []}, {"type": "error", "name": "LengthMismatch", "inputs": []}, {"type": "error", "name": "NoClaimableRewards", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "recipient", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "NotInitializing", "inputs": []}, {"type": "error", "name": "NotOwnerOrRewardManager", "inputs": []}, {"type": "error", "name": "OwnableInvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ReentrancyGuardReentrantCall", "inputs": []}, {"type": "error", "name": "RewardsTransferFailed", "inputs": [{"name": "recipient", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "SafeERC20FailedOperation", "inputs": [{"name": "token", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "UUPSUnauthorizedCallContext", "inputs": []}, {"type": "error", "name": "UUPSUnsupportedProxiableUUID", "inputs": [{"name": "slot", "type": "bytes32", "internalType": "bytes32"}]}, {"type": "error", "name": "ZeroAddress", "inputs": []}]
[{"type": "constructor", "inputs": [], "stateMutability": "nonpayable"}, {"type": "fallback", "stateMutability": "payable"}, {"type": "receive", "stateMutability": "payable"}, {"type": "function", "name": "UPGRADE_INTERFACE_VERSION", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "acceptOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "addStake", "inputs": [{"name": "bls<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "bytes[]", "internalType": "bytes[]"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "claimForceWithdrawnFunds", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "delegateStake", "inputs": [{"name": "bls<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "bytes[]", "internalType": "bytes[]"}, {"name": "withdrawalAddress", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "forceWithdrawalAsOwner", "inputs": [{"name": "bls<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "bytes[]", "internalType": "bytes[]"}, {"name": "withdrawalAddress", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "forceWithdrawnFunds", "inputs": [{"name": "withdrawalAddress", "type": "address", "internalType": "address"}], "outputs": [{"name": "amountToClaim", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getAccumulatedSlashingFunds", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getBlocksTillWithdrawAllowed", "inputs": [{"name": "valBLSPubKey", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getStakedAmount", "inputs": [{"name": "valBLSPubKey", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getStakedValidator", "inputs": [{"name": "valBLSPubKey", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct IVanillaRegistry.StakedValidator", "components": [{"name": "exists", "type": "bool", "internalType": "bool"}, {"name": "withdrawalAddress", "type": "address", "internalType": "address"}, {"name": "balance", "type": "uint256", "internalType": "uint256"}, {"name": "unstakeOccurrence", "type": "tuple", "internalType": "struct BlockHeightOccurrence.Occurrence", "components": [{"name": "exists", "type": "bool", "internalType": "bool"}, {"name": "blockHeight", "type": "uint256", "internalType": "uint256"}]}]}], "stateMutability": "view"}, {"type": "function", "name": "initialize", "inputs": [{"name": "_minStake", "type": "uint256", "internalType": "uint256"}, {"name": "_slashO<PERSON>le", "type": "address", "internalType": "address"}, {"name": "_slashReceiver", "type": "address", "internalType": "address"}, {"name": "_unstakePeriodBlocks", "type": "uint256", "internalType": "uint256"}, {"name": "_slashingPayoutPeriodBlocks", "type": "uint256", "internalType": "uint256"}, {"name": "_owner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "isSlashingPayoutDue", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isUnstaking", "inputs": [{"name": "valBLSPubKey", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isValidatorOptedIn", "inputs": [{"name": "valBLSPubKey", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "manuallyTransferSlashingFunds", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "minStake", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "pause", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "paused", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "pending<PERSON><PERSON>er", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "proxiableUUID", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "remove<PERSON><PERSON><PERSON>stedStakers", "inputs": [{"name": "stakers", "type": "address[]", "internalType": "address[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setMinStake", "inputs": [{"name": "newMinStake", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setSlashOracle", "inputs": [{"name": "newSlashOracle", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setSlashReceiver", "inputs": [{"name": "newSlashReceiver", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setSlashingPayoutPeriodBlocks", "inputs": [{"name": "newSlashingPayoutPeriodBlocks", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setUnstakePeriodBlocks", "inputs": [{"name": "newUnstakePeriodBlocks", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "slash", "inputs": [{"name": "bls<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "bytes[]", "internalType": "bytes[]"}, {"name": "payoutIfDue", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "slash<PERSON><PERSON>le", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "slashingFundsTracker", "inputs": [], "outputs": [{"name": "recipient", "type": "address", "internalType": "address"}, {"name": "accumulatedAmount", "type": "uint256", "internalType": "uint256"}, {"name": "lastPayoutBlock", "type": "uint256", "internalType": "uint256"}, {"name": "payoutPeriodBlocks", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "stake", "inputs": [{"name": "bls<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "bytes[]", "internalType": "bytes[]"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "stakedValidators", "inputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "exists", "type": "bool", "internalType": "bool"}, {"name": "withdrawalAddress", "type": "address", "internalType": "address"}, {"name": "balance", "type": "uint256", "internalType": "uint256"}, {"name": "unstakeOccurrence", "type": "tuple", "internalType": "struct BlockHeightOccurrence.Occurrence", "components": [{"name": "exists", "type": "bool", "internalType": "bool"}, {"name": "blockHeight", "type": "uint256", "internalType": "uint256"}]}], "stateMutability": "view"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "unpause", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "unstake", "inputs": [{"name": "bls<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "bytes[]", "internalType": "bytes[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "unstakePeriodBlocks", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "upgradeToAndCall", "inputs": [{"name": "newImplementation", "type": "address", "internalType": "address"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "whitelistStakers", "inputs": [{"name": "stakers", "type": "address[]", "internalType": "address[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "whitelistedStakers", "inputs": [{"name": "staker", "type": "address", "internalType": "address"}], "outputs": [{"name": "whitelisted", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "withdraw", "inputs": [{"name": "bls<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "bytes[]", "internalType": "bytes[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "FeeTransfer", "inputs": [{"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "recipient", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Initialized", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "MinStakeSet", "inputs": [{"name": "msgSender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newMinStake", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferStarted", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Paused", "inputs": [{"name": "account", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "SlashOracleSet", "inputs": [{"name": "msgSender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newSlashOracle", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "SlashReceiverSet", "inputs": [{"name": "msgSender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newSlashReceiver", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Slashed", "inputs": [{"name": "msgSender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "slashReceiver", "type": "address", "indexed": true, "internalType": "address"}, {"name": "withdrawalAddress", "type": "address", "indexed": true, "internalType": "address"}, {"name": "valBLSPubKey", "type": "bytes", "indexed": false, "internalType": "bytes"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "SlashingPayoutPeriodBlocksSet", "inputs": [{"name": "msgSender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newSlashingPayoutPeriodBlocks", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "StakeAdded", "inputs": [{"name": "msgSender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "withdrawalAddress", "type": "address", "indexed": true, "internalType": "address"}, {"name": "valBLSPubKey", "type": "bytes", "indexed": false, "internalType": "bytes"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "newBalance", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "StakeWithdrawn", "inputs": [{"name": "msgSender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "withdrawalAddress", "type": "address", "indexed": true, "internalType": "address"}, {"name": "valBLSPubKey", "type": "bytes", "indexed": false, "internalType": "bytes"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Staked", "inputs": [{"name": "msgSender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "withdrawalAddress", "type": "address", "indexed": true, "internalType": "address"}, {"name": "valBLSPubKey", "type": "bytes", "indexed": false, "internalType": "bytes"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "StakerRemovedFrom<PERSON><PERSON>elist", "inputs": [{"name": "msgSender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "staker", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "StakerW<PERSON>elisted", "inputs": [{"name": "msgSender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "staker", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "TotalStakeWithdrawn", "inputs": [{"name": "msgSender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "withdrawalAddress", "type": "address", "indexed": true, "internalType": "address"}, {"name": "totalAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Unpaused", "inputs": [{"name": "account", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "UnstakePeriodBlocksSet", "inputs": [{"name": "msgSender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newUnstakePeriodBlocks", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Unstaked", "inputs": [{"name": "msgSender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "withdrawalAddress", "type": "address", "indexed": true, "internalType": "address"}, {"name": "valBLSPubKey", "type": "bytes", "indexed": false, "internalType": "bytes"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Upgraded", "inputs": [{"name": "implementation", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "AddressEmptyCode", "inputs": [{"name": "target", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "AtLeastOneRecipientRequired", "inputs": []}, {"type": "error", "name": "ERC1967InvalidImplementation", "inputs": [{"name": "implementation", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC1967Non<PERSON>ayable", "inputs": []}, {"type": "error", "name": "EnforcedPause", "inputs": []}, {"type": "error", "name": "ExpectedPause", "inputs": []}, {"type": "error", "name": "FailedInnerCall", "inputs": []}, {"type": "error", "name": "FeeRecipientIsZero", "inputs": []}, {"type": "error", "name": "InvalidBLSPubKeyLength", "inputs": [{"name": "expected", "type": "uint256", "internalType": "uint256"}, {"name": "actual", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "InvalidFallback", "inputs": []}, {"type": "error", "name": "InvalidInitialization", "inputs": []}, {"type": "error", "name": "InvalidReceive", "inputs": []}, {"type": "error", "name": "MinStakeMustBePositive", "inputs": []}, {"type": "error", "name": "MustUnstakeToWithdraw", "inputs": []}, {"type": "error", "name": "NoFundsToWithdraw", "inputs": []}, {"type": "error", "name": "NotInitializing", "inputs": []}, {"type": "error", "name": "OwnableInvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "PayoutPeriodMustBePositive", "inputs": []}, {"type": "error", "name": "SenderIsNotSlashOracle", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}, {"name": "slash<PERSON><PERSON>le", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "SenderIsNotWhitelistedStaker", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "SenderIsNotWithdrawalAddress", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}, {"name": "withdrawalAddress", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "SlashAmountMustBeLessThanMinStake", "inputs": []}, {"type": "error", "name": "SlashAmountMustBePositive", "inputs": []}, {"type": "error", "name": "SlashOracleMustBeSet", "inputs": []}, {"type": "error", "name": "SlashReceiverMustBeSet", "inputs": []}, {"type": "error", "name": "SlashingPayoutPeriodMustBePositive", "inputs": []}, {"type": "error", "name": "SlashingTransferFailed", "inputs": []}, {"type": "error", "name": "StakeTooLowForNumberOfKeys", "inputs": [{"name": "msgValue", "type": "uint256", "internalType": "uint256"}, {"name": "required", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "StakerAlready<PERSON><PERSON>", "inputs": [{"name": "staker", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "StakerNotWhitelisted", "inputs": [{"name": "staker", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "TransferToRecipientFailed", "inputs": []}, {"type": "error", "name": "UUPSUnauthorizedCallContext", "inputs": []}, {"type": "error", "name": "UUPSUnsupportedProxiableUUID", "inputs": [{"name": "slot", "type": "bytes32", "internalType": "bytes32"}]}, {"type": "error", "name": "UnstakePeriodMustBePositive", "inputs": []}, {"type": "error", "name": "ValidatorCannotBeUnstaking", "inputs": [{"name": "valBLSPubKey", "type": "bytes", "internalType": "bytes"}]}, {"type": "error", "name": "ValidatorRecordMustExist", "inputs": [{"name": "valBLSPubKey", "type": "bytes", "internalType": "bytes"}]}, {"type": "error", "name": "ValidatorRecordMustNotExist", "inputs": [{"name": "valBLSPubKey", "type": "bytes", "internalType": "bytes"}]}, {"type": "error", "name": "WithdrawalAddressMismatch", "inputs": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "WithdrawalAddressMustBeSet", "inputs": []}, {"type": "error", "name": "WithdrawalFailed", "inputs": []}, {"type": "error", "name": "WithdrawingTooSoon", "inputs": []}]
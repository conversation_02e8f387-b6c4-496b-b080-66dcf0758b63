[{"type": "constructor", "inputs": [], "stateMutability": "nonpayable"}, {"type": "fallback", "stateMutability": "payable"}, {"type": "receive", "stateMutability": "payable"}, {"type": "function", "name": "UPGRADE_INTERFACE_VERSION", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "acceptOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "autoClaim", "inputs": [{"name": "receiver", "type": "address", "internalType": "address"}], "outputs": [{"name": "enabled", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "autoClaimBlacklist", "inputs": [{"name": "receiver", "type": "address", "internalType": "address"}], "outputs": [{"name": "blacklisted", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "autoClaimGasLimit", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "claimOrphanedRewards", "inputs": [{"name": "pubkeys", "type": "bytes[]", "internalType": "bytes[]"}, {"name": "toPay", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "claimRewards", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "disable<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "enableAutoClaim", "inputs": [{"name": "claimExistingRewards", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "initialize", "inputs": [{"name": "vanillaRegistry", "type": "address", "internalType": "address"}, {"name": "mevCommitAVS", "type": "address", "internalType": "address"}, {"name": "mevCommitMiddleware", "type": "address", "internalType": "address"}, {"name": "autoClaimGasLimit", "type": "uint256", "internalType": "uint256"}, {"name": "owner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "orphanedRewards", "inputs": [{"name": "pubkey", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "amount", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "overrideAddresses", "inputs": [{"name": "receiver", "type": "address", "internalType": "address"}], "outputs": [{"name": "override<PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "overrideReceiver", "inputs": [{"name": "override<PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}, {"name": "migrateExistingRewards", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "pause", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "paused", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "payProposer", "inputs": [{"name": "pubkey", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "pending<PERSON><PERSON>er", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "proxiableUUID", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "removeFromAutoClaimBlacklist", "inputs": [{"name": "addr", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "removeOverrideAddress", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setAutoClaimGasLimit", "inputs": [{"name": "autoClaimGasLimit", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setMevCommitAVS", "inputs": [{"name": "mevCommitAVS", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setMevCommitMiddleware", "inputs": [{"name": "mevCommitMiddleware", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setVanillaRegistry", "inputs": [{"name": "vanillaRegistry", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "unclaimedRewards", "inputs": [{"name": "addr", "type": "address", "internalType": "address"}], "outputs": [{"name": "amount", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "unpause", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "upgradeToAndCall", "inputs": [{"name": "newImplementation", "type": "address", "internalType": "address"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "payable"}, {"type": "event", "name": "AutoClaimDisabled", "inputs": [{"name": "receiver", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "AutoClaimEnabled", "inputs": [{"name": "receiver", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "AutoClaimGasLimitSet", "inputs": [{"name": "autoClaimGasLimit", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "AutoClaimTransferFailed", "inputs": [{"name": "provider", "type": "address", "indexed": true, "internalType": "address"}, {"name": "receiver", "type": "address", "indexed": true, "internalType": "address"}, {"name": "toPay", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "AutoClaimed", "inputs": [{"name": "provider", "type": "address", "indexed": true, "internalType": "address"}, {"name": "receiver", "type": "address", "indexed": true, "internalType": "address"}, {"name": "toPay", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Initialized", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "MevCommitAVSSet", "inputs": [{"name": "newMevCommitAVS", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "MevCommitMiddlewareSet", "inputs": [{"name": "newMevCommitMiddleware", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "NoRewards", "inputs": [{"name": "addr", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "OrphanedRewardsAccumulated", "inputs": [{"name": "provider", "type": "address", "indexed": true, "internalType": "address"}, {"name": "pubkey", "type": "bytes", "indexed": true, "internalType": "bytes"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "OrphanedRewardsClaimed", "inputs": [{"name": "toPay", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "OverrideAddressRemoved", "inputs": [{"name": "receiver", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "OverrideAddressSet", "inputs": [{"name": "receiver", "type": "address", "indexed": true, "internalType": "address"}, {"name": "override<PERSON><PERSON><PERSON>", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferStarted", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Paused", "inputs": [{"name": "account", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "PaymentStored", "inputs": [{"name": "provider", "type": "address", "indexed": true, "internalType": "address"}, {"name": "receiver", "type": "address", "indexed": true, "internalType": "address"}, {"name": "toPay", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "ProposerNotFound", "inputs": [{"name": "pubkey", "type": "bytes", "indexed": true, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "RemovedFromAutoClaimBlacklist", "inputs": [{"name": "addr", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "RewardsClaimed", "inputs": [{"name": "msgSender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "RewardsMigrated", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Unpaused", "inputs": [{"name": "account", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Upgraded", "inputs": [{"name": "implementation", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "VanillaRegistrySet", "inputs": [{"name": "newVanillaRegistry", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "AddressEmptyCode", "inputs": [{"name": "target", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC1967InvalidImplementation", "inputs": [{"name": "implementation", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC1967Non<PERSON>ayable", "inputs": []}, {"type": "error", "name": "EnforcedPause", "inputs": []}, {"type": "error", "name": "ExpectedPause", "inputs": []}, {"type": "error", "name": "FailedInnerCall", "inputs": []}, {"type": "error", "name": "InvalidAddress", "inputs": []}, {"type": "error", "name": "InvalidAutoClaimGasLimit", "inputs": []}, {"type": "error", "name": "InvalidBLSPubKeyLength", "inputs": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "uint256", "internalType": "uint256"}, {"name": "actualLength", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "InvalidFallback", "inputs": []}, {"type": "error", "name": "InvalidInitialization", "inputs": []}, {"type": "error", "name": "InvalidReceive", "inputs": []}, {"type": "error", "name": "NoEthPayable", "inputs": []}, {"type": "error", "name": "NoOrphanedRewards", "inputs": []}, {"type": "error", "name": "NoOverriddenAddressToRemove", "inputs": []}, {"type": "error", "name": "NotInitializing", "inputs": []}, {"type": "error", "name": "OrphanedRewardsClaimFailed", "inputs": []}, {"type": "error", "name": "OwnableInvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ReentrancyGuardReentrantCall", "inputs": []}, {"type": "error", "name": "RewardsClaimFailed", "inputs": []}, {"type": "error", "name": "UUPSUnauthorizedCallContext", "inputs": []}, {"type": "error", "name": "UUPSUnsupportedProxiableUUID", "inputs": [{"name": "slot", "type": "bytes32", "internalType": "bytes32"}]}]